# How to Contribute

jsSHA welcomes all contributions whether they are bug reports, feature requests,
or pull requests (PR). Please note the scope of this library is limited to
official SHA variants or their direct descendants.

In order to speed up the process, please adhere to the following guidelines:

## Bug Reports

When submitting bug reports please ensure your report has the minimum following
information:

- JavaScript engine name and version (i.e. the browser or Node.js version).
- Whether the module is being imported as a ECMAScript Module (ESM) or via other
  means.
- The full error reported, if applicable.
- If the code is not sensitive, the code snippet that caused the error.

## Pull Requests

First off, thank you for being willing to contribute code directly to jsSHA! We
tried to make the tooling take care of all the mundane issues like code
formatting to make it easy to contribute. That being said, we ask that you
follow these guidelines when submitting PRs:

- Please do not include any files in the `dist/` directory. For security
  reasons, only `dist` files created by the maintainer(s) are distributed.
- Although the CI/CD pipeline should take care of this, please, at minimum, run
  `npm build && npm test` before submitting your PR.
- Some of our users still use JavaScript engines that only support ECMAScript 3
  (ES3) which was adopted back in 1999. Please try to make sure that any new
  code is compatible with ES3.
