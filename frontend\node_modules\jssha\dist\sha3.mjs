/**
 * A JavaScript implementation of the SHA family of hashes - defined in FIPS PUB 180-4, FIPS PUB 202,
 * and SP 800-185 - as well as the corresponding HMAC implementation as defined in FIPS PUB 198-1.
 *
 * Copyright 2008-2023 <PERSON>, 1998-2009 <PERSON> & Contributors
 * Distributed under the BSD License
 * See http://caligatio.github.com/jsSHA/ for more information
 */
const t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",n="ARRAYBUFFER not supported by this environment",r="UINT8ARRAY not supported by this environment";function i(t,n,r,i){let s,e,o;const h=n||[0],u=(r=r||0)>>>3,c=-1===i?3:0;for(s=0;s<t.length;s+=1)o=s+u,e=o>>>2,h.length<=e&&h.push(0),h[e]|=t[s]<<8*(c+i*(o%4));return{value:h,binLen:8*t.length+r}}function s(s,e,o){switch(e){case"UTF8":case"UTF16BE":case"UTF16LE":break;default:throw new Error("encoding must be UTF8, UTF16BE, or UTF16LE")}switch(s){case"HEX":return function(t,n,r){return function(t,n,r,i){let s,e,o,h;if(0!=t.length%2)throw new Error("String of HEX type must be in byte increments");const u=n||[0],c=(r=r||0)>>>3,f=-1===i?3:0;for(s=0;s<t.length;s+=2){if(e=parseInt(t.substr(s,2),16),isNaN(e))throw new Error("String of HEX type contains invalid characters");for(h=(s>>>1)+c,o=h>>>2;u.length<=o;)u.push(0);u[o]|=e<<8*(f+i*(h%4))}return{value:u,binLen:4*t.length+r}}(t,n,r,o)};case"TEXT":return function(t,n,r){return function(t,n,r,i,s){let e,o,h,u,c,f,a,w,l=0;const E=r||[0],A=(i=i||0)>>>3;if("UTF8"===n)for(a=-1===s?3:0,h=0;h<t.length;h+=1)for(e=t.charCodeAt(h),o=[],128>e?o.push(e):2048>e?(o.push(192|e>>>6),o.push(128|63&e)):55296>e||57344<=e?o.push(224|e>>>12,128|e>>>6&63,128|63&e):(h+=1,e=65536+((1023&e)<<10|1023&t.charCodeAt(h)),o.push(240|e>>>18,128|e>>>12&63,128|e>>>6&63,128|63&e)),u=0;u<o.length;u+=1){for(f=l+A,c=f>>>2;E.length<=c;)E.push(0);E[c]|=o[u]<<8*(a+s*(f%4)),l+=1}else for(a=-1===s?2:0,w="UTF16LE"===n&&1!==s||"UTF16LE"!==n&&1===s,h=0;h<t.length;h+=1){for(e=t.charCodeAt(h),!0===w&&(u=255&e,e=u<<8|e>>>8),f=l+A,c=f>>>2;E.length<=c;)E.push(0);E[c]|=e<<8*(a+s*(f%4)),l+=2}return{value:E,binLen:8*l+i}}(t,e,n,r,o)};case"B64":return function(n,r,i){return function(n,r,i,s){let e,o,h,u,c,f,a,w=0;const l=r||[0],E=(i=i||0)>>>3,A=-1===s?3:0,b=n.indexOf("=");if(-1===n.search(/^[a-zA-Z0-9=+/]+$/))throw new Error("Invalid character in base-64 string");if(n=n.replace(/=/g,""),-1!==b&&b<n.length)throw new Error("Invalid '=' found in base-64 string");for(o=0;o<n.length;o+=4){for(c=n.substr(o,4),u=0,h=0;h<c.length;h+=1)e=t.indexOf(c.charAt(h)),u|=e<<18-6*h;for(h=0;h<c.length-1;h+=1){for(a=w+E,f=a>>>2;l.length<=f;)l.push(0);l[f]|=(u>>>16-8*h&255)<<8*(A+s*(a%4)),w+=1}}return{value:l,binLen:8*w+i}}(n,r,i,o)};case"BYTES":return function(t,n,r){return function(t,n,r,i){let s,e,o,h;const u=n||[0],c=(r=r||0)>>>3,f=-1===i?3:0;for(e=0;e<t.length;e+=1)s=t.charCodeAt(e),h=e+c,o=h>>>2,u.length<=o&&u.push(0),u[o]|=s<<8*(f+i*(h%4));return{value:u,binLen:8*t.length+r}}(t,n,r,o)};case"ARRAYBUFFER":try{new ArrayBuffer(0)}catch(t){throw new Error(n)}return function(t,n,r){return function(t,n,r,s){return i(new Uint8Array(t),n,r,s)}(t,n,r,o)};case"UINT8ARRAY":try{new Uint8Array(0)}catch(t){throw new Error(r)}return function(t,n,r){return i(t,n,r,o)};default:throw new Error("format must be HEX, TEXT, B64, BYTES, ARRAYBUFFER, or UINT8ARRAY")}}function e(i,s,e,o){switch(i){case"HEX":return function(t){return function(t,n,r,i){const s="0123456789abcdef";let e,o,h="";const u=n/8,c=-1===r?3:0;for(e=0;e<u;e+=1)o=t[e>>>2]>>>8*(c+r*(e%4)),h+=s.charAt(o>>>4&15)+s.charAt(15&o);return i.outputUpper?h.toUpperCase():h}(t,s,e,o)};case"B64":return function(n){return function(n,r,i,s){let e,o,h,u,c,f="";const a=r/8,w=-1===i?3:0;for(e=0;e<a;e+=3)for(u=e+1<a?n[e+1>>>2]:0,c=e+2<a?n[e+2>>>2]:0,h=(n[e>>>2]>>>8*(w+i*(e%4))&255)<<16|(u>>>8*(w+i*((e+1)%4))&255)<<8|c>>>8*(w+i*((e+2)%4))&255,o=0;o<4;o+=1)f+=8*e+6*o<=r?t.charAt(h>>>6*(3-o)&63):s.b64Pad;return f}(n,s,e,o)};case"BYTES":return function(t){return function(t,n,r){let i,s,e="";const o=n/8,h=-1===r?3:0;for(i=0;i<o;i+=1)s=t[i>>>2]>>>8*(h+r*(i%4))&255,e+=String.fromCharCode(s);return e}(t,s,e)};case"ARRAYBUFFER":try{new ArrayBuffer(0)}catch(t){throw new Error(n)}return function(t){return function(t,n,r){let i;const s=n/8,e=new ArrayBuffer(s),o=new Uint8Array(e),h=-1===r?3:0;for(i=0;i<s;i+=1)o[i]=t[i>>>2]>>>8*(h+r*(i%4))&255;return e}(t,s,e)};case"UINT8ARRAY":try{new Uint8Array(0)}catch(t){throw new Error(r)}return function(t){return function(t,n,r){let i;const s=n/8,e=-1===r?3:0,o=new Uint8Array(s);for(i=0;i<s;i+=1)o[i]=t[i>>>2]>>>8*(e+r*(i%4))&255;return o}(t,s,e)};default:throw new Error("format must be HEX, B64, BYTES, ARRAYBUFFER, or UINT8ARRAY")}}const o=4294967296,h="Cannot set numRounds with MAC";function u(t,n){let r,i;const s=t.binLen>>>3,e=n.binLen>>>3,o=s<<3,h=4-s<<3;if(s%4!=0){for(r=0;r<e;r+=4)i=s+r>>>2,t.value[i]|=n.value[r>>>2]<<o,t.value.push(0),t.value[i+1]|=n.value[r>>>2]>>>h;return(t.value.length<<2)-4>=e+s&&t.value.pop(),{value:t.value,binLen:t.binLen+n.binLen}}return{value:t.value.concat(n.value),binLen:t.binLen+n.binLen}}function c(t){const n={outputUpper:!1,b64Pad:"=",outputLen:-1},r=t||{},i="Output length must be a multiple of 8";if(n.outputUpper=r.outputUpper||!1,r.b64Pad&&(n.b64Pad=r.b64Pad),r.outputLen){if(r.outputLen%8!=0)throw new Error(i);n.outputLen=r.outputLen}else if(r.shakeLen){if(r.shakeLen%8!=0)throw new Error(i);n.outputLen=r.shakeLen}if("boolean"!=typeof n.outputUpper)throw new Error("Invalid outputUpper formatting option");if("string"!=typeof n.b64Pad)throw new Error("Invalid b64Pad formatting option");return n}function f(t,n,r,i){const e=t+" must include a value and format";if(!n){if(!i)throw new Error(e);return i}if(void 0===n.value||!n.format)throw new Error(e);return s(n.format,n.encoding||"UTF8",r)(n.value)}class a{constructor(t,n,r){const i=r||{};if(this.t=n,this.i=i.encoding||"UTF8",this.numRounds=i.numRounds||1,isNaN(this.numRounds)||this.numRounds!==parseInt(this.numRounds,10)||1>this.numRounds)throw new Error("numRounds must a integer >= 1");this.o=t,this.h=[],this.u=0,this.l=!1,this.A=0,this.p=!1,this.m=[],this.U=[]}update(t){let n,r=0;const i=this.R>>>5,s=this.C(t,this.h,this.u),e=s.binLen,o=s.value,h=e>>>5;for(n=0;n<h;n+=i)r+this.R<=e&&(this.v=this.H(o.slice(n,n+i),this.v),r+=this.R);return this.A+=r,this.h=o.slice(r>>>5),this.u=e%this.R,this.l=!0,this}getHash(t,n){let r,i,s=this.T;const o=c(n);if(this.F){if(-1===o.outputLen)throw new Error("Output length must be specified in options");s=o.outputLen}const h=e(t,s,this.g,o);if(this.p&&this.S)return h(this.S(o));for(i=this.L(this.h.slice(),this.u,this.A,this.B(this.v),s),r=1;r<this.numRounds;r+=1)this.F&&s%32!=0&&(i[i.length-1]&=16777215>>>24-s%32),i=this.L(i,s,0,this.K(this.o),s);return h(i)}setHMACKey(t,n,r){if(!this.k)throw new Error("Variant does not support HMAC");if(this.l)throw new Error("Cannot set MAC key after calling update");const i=s(n,(r||{}).encoding||"UTF8",this.g);this.M(i(t))}M(t){const n=this.R>>>3,r=n/4-1;let i;if(1!==this.numRounds)throw new Error(h);if(this.p)throw new Error("MAC key already set");for(n<t.binLen/8&&(t.value=this.L(t.value,t.binLen,0,this.K(this.o),this.T));t.value.length<=r;)t.value.push(0);for(i=0;i<=r;i+=1)this.m[i]=909522486^t.value[i],this.U[i]=1549556828^t.value[i];this.v=this.H(this.m,this.v),this.A=this.R,this.p=!0}getHMAC(t,n){const r=c(n);return e(t,this.T,this.g,r)(this.Y())}Y(){let t;if(!this.p)throw new Error("Cannot call getHMAC without first setting MAC key");const n=this.L(this.h.slice(),this.u,this.A,this.B(this.v),this.T);return t=this.H(this.U,this.K(this.o)),t=this.L(n,this.T,this.R,t,this.T),t}}class w{constructor(t,n){this.N=t,this.I=n}}function l(t,n){let r;return n>32?(r=64-n,new w(t.I<<n|t.N>>>r,t.N<<n|t.I>>>r)):0!==n?(r=32-n,new w(t.N<<n|t.I>>>r,t.I<<n|t.N>>>r)):t}function E(t,n){return new w(t.N^n.N,t.I^n.I)}const A=[new w(0,1),new w(0,32898),new w(2147483648,32906),new w(2147483648,2147516416),new w(0,32907),new w(0,2147483649),new w(2147483648,2147516545),new w(2147483648,32777),new w(0,138),new w(0,136),new w(0,2147516425),new w(0,2147483658),new w(0,2147516555),new w(2147483648,139),new w(2147483648,32905),new w(2147483648,32771),new w(2147483648,32770),new w(2147483648,128),new w(0,32778),new w(2147483648,2147483658),new w(2147483648,2147516545),new w(2147483648,32896),new w(0,2147483649),new w(2147483648,2147516424)],b=[[0,36,3,41,18],[1,44,10,45,2],[62,6,43,15,61],[28,55,25,21,56],[27,20,39,8,14]];function p(t){let n;const r=[];for(n=0;n<5;n+=1)r[n]=[new w(0,0),new w(0,0),new w(0,0),new w(0,0),new w(0,0)];return r}function d(t){let n;const r=[];for(n=0;n<5;n+=1)r[n]=t[n].slice();return r}function m(t,n){let r,i,s,e;const o=[],h=[];if(null!==t)for(i=0;i<t.length;i+=2)n[(i>>>1)%5][(i>>>1)/5|0]=E(n[(i>>>1)%5][(i>>>1)/5|0],new w(t[i+1],t[i]));for(r=0;r<24;r+=1){for(e=p(),i=0;i<5;i+=1)o[i]=(u=n[i][0],c=n[i][1],f=n[i][2],a=n[i][3],d=n[i][4],new w(u.N^c.N^f.N^a.N^d.N,u.I^c.I^f.I^a.I^d.I));for(i=0;i<5;i+=1)h[i]=E(o[(i+4)%5],l(o[(i+1)%5],1));for(i=0;i<5;i+=1)for(s=0;s<5;s+=1)n[i][s]=E(n[i][s],h[i]);for(i=0;i<5;i+=1)for(s=0;s<5;s+=1)e[s][(2*i+3*s)%5]=l(n[i][s],b[i][s]);for(i=0;i<5;i+=1)for(s=0;s<5;s+=1)n[i][s]=E(e[i][s],new w(~e[(i+1)%5][s].N&e[(i+2)%5][s].N,~e[(i+1)%5][s].I&e[(i+2)%5][s].I));n[0][0]=E(n[0][0],A[r])}var u,c,f,a,d;return n}function U(t){let n,r,i=0;const s=[0,0],e=[4294967295&t,t/o&2097151];for(n=6;n>=0;n--)r=e[n>>2]>>>8*n&255,0===r&&0===i||(s[i+1>>2]|=r<<8*(i+1),i+=1);return i=0!==i?i:1,s[0]|=i,{value:i+1>4?s:[s[0]],binLen:8+8*i}}function y(t){return u(U(t.binLen),t)}function R(t,n){let r,i=U(n);i=u(i,t);const s=n>>>2,e=(s-i.value.length%s)%s;for(r=0;r<e;r++)i.value.push(0);return i.value}class C extends a{constructor(t,n,r){let i=6,e=0;super(t,n,r);const o=r||{};if(1!==this.numRounds){if(o.kmacKey||o.hmacKey)throw new Error(h);if("CSHAKE128"===this.o||"CSHAKE256"===this.o)throw new Error("Cannot set numRounds for CSHAKE variants")}switch(this.g=1,this.C=s(this.t,this.i,this.g),this.H=m,this.B=d,this.K=p,this.v=p(),this.F=!1,t){case"SHA3-224":this.R=e=1152,this.T=224,this.k=!0,this.S=this.Y;break;case"SHA3-256":this.R=e=1088,this.T=256,this.k=!0,this.S=this.Y;break;case"SHA3-384":this.R=e=832,this.T=384,this.k=!0,this.S=this.Y;break;case"SHA3-512":this.R=e=576,this.T=512,this.k=!0,this.S=this.Y;break;case"SHAKE128":i=31,this.R=e=1344,this.T=-1,this.F=!0,this.k=!1,this.S=null;break;case"SHAKE256":i=31,this.R=e=1088,this.T=-1,this.F=!0,this.k=!1,this.S=null;break;case"KMAC128":i=4,this.R=e=1344,this.X(r),this.T=-1,this.F=!0,this.k=!1,this.S=this._;break;case"KMAC256":i=4,this.R=e=1088,this.X(r),this.T=-1,this.F=!0,this.k=!1,this.S=this._;break;case"CSHAKE128":this.R=e=1344,i=this.O(r),this.T=-1,this.F=!0,this.k=!1,this.S=null;break;case"CSHAKE256":this.R=e=1088,i=this.O(r),this.T=-1,this.F=!0,this.k=!1,this.S=null;break;default:throw new Error("Chosen SHA variant is not supported")}this.L=function(t,n,r,s,o){return function(t,n,r,i,s,e,o){let h,u,c=0;const f=[],a=s>>>5,w=n>>>5;for(h=0;h<w&&n>=s;h+=a)i=m(t.slice(h,h+a),i),n-=s;for(t=t.slice(h),n%=s;t.length<a;)t.push(0);for(h=n>>>3,t[h>>2]^=e<<h%4*8,t[a-1]^=2147483648,i=m(t,i);32*f.length<o&&(u=i[c%5][c/5|0],f.push(u.I),!(32*f.length>=o));)f.push(u.N),c+=1,0==64*c%s&&(m(null,i),c=0);return f}(t,n,0,s,e,i,o)},o.hmacKey&&this.M(f("hmacKey",o.hmacKey,this.g))}O(t,n){const r=function(t){const n=t||{};return{funcName:f("funcName",n.funcName,1,{value:[],binLen:0}),customization:f("Customization",n.customization,1,{value:[],binLen:0})}}(t||{});n&&(r.funcName=n);const i=u(y(r.funcName),y(r.customization));if(0!==r.customization.binLen||0!==r.funcName.binLen){const t=R(i,this.R>>>3);for(let n=0;n<t.length;n+=this.R>>>5)this.v=this.H(t.slice(n,n+(this.R>>>5)),this.v),this.A+=this.R;return 4}return 31}X(t){const n=function(t){const n=t||{};return{kmacKey:f("kmacKey",n.kmacKey,1),funcName:{value:[1128353099],binLen:32},customization:f("Customization",n.customization,1,{value:[],binLen:0})}}(t||{});this.O(t,n.funcName);const r=R(y(n.kmacKey),this.R>>>3);for(let t=0;t<r.length;t+=this.R>>>5)this.v=this.H(r.slice(t,t+(this.R>>>5)),this.v),this.A+=this.R;this.p=!0}_(t){const n=u({value:this.h.slice(),binLen:this.u},function(t){let n,r,i=0;const s=[0,0],e=[4294967295&t,t/o&2097151];for(n=6;n>=0;n--)r=e[n>>2]>>>8*n&255,0===r&&0===i||(s[i>>2]|=r<<8*i,i+=1);return i=0!==i?i:1,s[i>>2]|=i<<8*i,{value:i+1>4?s:[s[0]],binLen:8+8*i}}(t.outputLen));return this.L(n.value,n.binLen,this.A,this.B(this.v),t.outputLen)}}export{C as default};
