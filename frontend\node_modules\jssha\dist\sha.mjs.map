{"version": 3, "file": "sha.mjs", "sources": ["../src/converters.ts", "../src/common.ts", "../src/primitives_32.ts", "../src/sha1.ts", "../src/sha256.ts", "../src/primitives_64.ts", "../src/sha512.ts", "../src/sha3.ts", "../src/sha.ts"], "sourcesContent": ["import { packedValue, EncodingType, FormatType } from \"./custom_types\";\n/**\n * Return type for all the *2packed functions\n */\nconst b64Tab = \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/\";\n\nconst arraybuffer_error = \"ARRAYBUFFER not supported by this environment\";\nconst uint8array_error = \"UINT8ARRAY not supported by this environment\";\n\n/**\n * Convert a string to an array of words.\n *\n * There is a known bug with an odd number of existing bytes and using a UTF-16 encoding.  However, this function is\n * used such that the existing bytes are always a result of a previous UTF-16 str2packed call and therefore there \n * should never be an odd number of existing bytes.\n\n * @param str Unicode string to be converted to binary representation.\n * @param utfType The Unicode type to use to encode the source string.\n * @param existingPacked A packed int array of bytes to append the results to.\n * @param existingPackedLen The number of bits in `existingPacked`.\n * @param bigEndianMod Modifier for whether hash function is big or small endian.\n * @returns Hashmap of the packed values.\n */\nfunction str2packed(\n  str: string,\n  utfType: EncodingType,\n  existingPacked: number[] | undefined,\n  existingPackedLen: number | undefined,\n  bigEndianMod: -1 | 1\n): packedValue {\n  let codePnt,\n    codePntArr,\n    byteCnt = 0,\n    i,\n    j,\n    intOffset,\n    byteOffset,\n    shiftModifier,\n    transposeBytes;\n\n  existingPackedLen = existingPackedLen || 0;\n  const packed = existingPacked || [0],\n    existingByteLen = existingPackedLen >>> 3;\n\n  if (\"UTF8\" === utfType) {\n    shiftModifier = bigEndianMod === -1 ? 3 : 0;\n    for (i = 0; i < str.length; i += 1) {\n      codePnt = str.charCodeAt(i);\n      codePntArr = [];\n\n      if (0x80 > codePnt) {\n        codePntArr.push(codePnt);\n      } else if (0x800 > codePnt) {\n        codePntArr.push(0xc0 | (codePnt >>> 6));\n        codePntArr.push(0x80 | (codePnt & 0x3f));\n      } else if (0xd800 > codePnt || 0xe000 <= codePnt) {\n        codePntArr.push(0xe0 | (codePnt >>> 12), 0x80 | ((codePnt >>> 6) & 0x3f), 0x80 | (codePnt & 0x3f));\n      } else {\n        i += 1;\n        codePnt = 0x10000 + (((codePnt & 0x3ff) << 10) | (str.charCodeAt(i) & 0x3ff));\n        codePntArr.push(\n          0xf0 | (codePnt >>> 18),\n          0x80 | ((codePnt >>> 12) & 0x3f),\n          0x80 | ((codePnt >>> 6) & 0x3f),\n          0x80 | (codePnt & 0x3f)\n        );\n      }\n\n      for (j = 0; j < codePntArr.length; j += 1) {\n        byteOffset = byteCnt + existingByteLen;\n        intOffset = byteOffset >>> 2;\n        while (packed.length <= intOffset) {\n          packed.push(0);\n        }\n        /* Known bug kicks in here */\n        packed[intOffset] |= codePntArr[j] << (8 * (shiftModifier + bigEndianMod * (byteOffset % 4)));\n        byteCnt += 1;\n      }\n    }\n  } else {\n    /* UTF16BE or UTF16LE */\n    shiftModifier = bigEndianMod === -1 ? 2 : 0;\n    /* Internally strings are UTF-16BE so transpose bytes under two conditions:\n     * need LE and not switching endianness due to SHA-3\n     * need BE and switching endianness due to SHA-3 */\n    transposeBytes = (\"UTF16LE\" === utfType && bigEndianMod !== 1) || (\"UTF16LE\" !== utfType && bigEndianMod === 1);\n    for (i = 0; i < str.length; i += 1) {\n      codePnt = str.charCodeAt(i);\n      if (transposeBytes === true) {\n        j = codePnt & 0xff;\n        codePnt = (j << 8) | (codePnt >>> 8);\n      }\n\n      byteOffset = byteCnt + existingByteLen;\n      intOffset = byteOffset >>> 2;\n      while (packed.length <= intOffset) {\n        packed.push(0);\n      }\n      packed[intOffset] |= codePnt << (8 * (shiftModifier + bigEndianMod * (byteOffset % 4)));\n      byteCnt += 2;\n    }\n  }\n  return { value: packed, binLen: byteCnt * 8 + existingPackedLen };\n}\n\n/**\n * Convert a hex string to an array of words.\n *\n * @param str Hexadecimal string to be converted to binary representation.\n * @param existingPacked A packed int array of bytes to append the results to.\n * @param existingPackedLen The number of bits in `existingPacked` array.\n * @param bigEndianMod Modifier for whether hash function is big or small endian.\n * @returns Hashmap of the packed values.\n */\nfunction hex2packed(\n  str: string,\n  existingPacked: number[] | undefined,\n  existingPackedLen: number | undefined,\n  bigEndianMod: -1 | 1\n): packedValue {\n  let i, num, intOffset, byteOffset;\n\n  if (0 !== str.length % 2) {\n    throw new Error(\"String of HEX type must be in byte increments\");\n  }\n\n  existingPackedLen = existingPackedLen || 0;\n  const packed = existingPacked || [0],\n    existingByteLen = existingPackedLen >>> 3,\n    shiftModifier = bigEndianMod === -1 ? 3 : 0;\n\n  for (i = 0; i < str.length; i += 2) {\n    num = parseInt(str.substr(i, 2), 16);\n    if (!isNaN(num)) {\n      byteOffset = (i >>> 1) + existingByteLen;\n      intOffset = byteOffset >>> 2;\n      while (packed.length <= intOffset) {\n        packed.push(0);\n      }\n      packed[intOffset] |= num << (8 * (shiftModifier + bigEndianMod * (byteOffset % 4)));\n    } else {\n      throw new Error(\"String of HEX type contains invalid characters\");\n    }\n  }\n\n  return { value: packed, binLen: str.length * 4 + existingPackedLen };\n}\n\n/**\n * Convert a string of raw bytes to an array of words.\n *\n * @param str String of raw bytes to be converted to binary representation.\n * @param existingPacked A packed int array of bytes to append the results to.\n * @param existingPackedLen The number of bits in `existingPacked` array.\n * @param bigEndianMod Modifier for whether hash function is big or small endian.\n * @returns Hashmap of the packed values.\n */\nfunction bytes2packed(\n  str: string,\n  existingPacked: number[] | undefined,\n  existingPackedLen: number | undefined,\n  bigEndianMod: -1 | 1\n): packedValue {\n  let codePnt, i, intOffset, byteOffset;\n\n  existingPackedLen = existingPackedLen || 0;\n  const packed = existingPacked || [0],\n    existingByteLen = existingPackedLen >>> 3,\n    shiftModifier = bigEndianMod === -1 ? 3 : 0;\n\n  for (i = 0; i < str.length; i += 1) {\n    codePnt = str.charCodeAt(i);\n\n    byteOffset = i + existingByteLen;\n    intOffset = byteOffset >>> 2;\n    if (packed.length <= intOffset) {\n      packed.push(0);\n    }\n    packed[intOffset] |= codePnt << (8 * (shiftModifier + bigEndianMod * (byteOffset % 4)));\n  }\n\n  return { value: packed, binLen: str.length * 8 + existingPackedLen };\n}\n\n/**\n * Convert a base-64 string to an array of words.\n *\n * @param str Base64-encoded string to be converted to binary representation.\n * @param existingPacked A packed int array of bytes to append the results to.\n * @param existingPackedLen The number of bits in `existingPacked` array.\n * @param bigEndianMod Modifier for whether hash function is big or small endian.\n * @returns Hashmap of the packed values.\n */\nfunction b642packed(\n  str: string,\n  existingPacked: number[] | undefined,\n  existingPackedLen: number | undefined,\n  bigEndianMod: -1 | 1\n): packedValue {\n  let byteCnt = 0,\n    index,\n    i,\n    j,\n    tmpInt,\n    strPart,\n    intOffset,\n    byteOffset;\n\n  existingPackedLen = existingPackedLen || 0;\n  const packed = existingPacked || [0],\n    existingByteLen = existingPackedLen >>> 3,\n    shiftModifier = bigEndianMod === -1 ? 3 : 0,\n    firstEqual = str.indexOf(\"=\");\n\n  if (-1 === str.search(/^[a-zA-Z0-9=+/]+$/)) {\n    throw new Error(\"Invalid character in base-64 string\");\n  }\n\n  str = str.replace(/=/g, \"\");\n  if (-1 !== firstEqual && firstEqual < str.length) {\n    throw new Error(\"Invalid '=' found in base-64 string\");\n  }\n\n  for (i = 0; i < str.length; i += 4) {\n    strPart = str.substr(i, 4);\n    tmpInt = 0;\n\n    for (j = 0; j < strPart.length; j += 1) {\n      index = b64Tab.indexOf(strPart.charAt(j));\n      tmpInt |= index << (18 - 6 * j);\n    }\n\n    for (j = 0; j < strPart.length - 1; j += 1) {\n      byteOffset = byteCnt + existingByteLen;\n      intOffset = byteOffset >>> 2;\n      while (packed.length <= intOffset) {\n        packed.push(0);\n      }\n      packed[intOffset] |=\n        ((tmpInt >>> (16 - j * 8)) & 0xff) << (8 * (shiftModifier + bigEndianMod * (byteOffset % 4)));\n      byteCnt += 1;\n    }\n  }\n\n  return { value: packed, binLen: byteCnt * 8 + existingPackedLen };\n}\n\n/**\n * Convert an Uint8Array to an array of words.\n *\n * @param arr Uint8Array to be converted to binary representation.\n * @param existingPacked A packed int array of bytes to append the results to.\n * @param existingPackedLen The number of bits in `existingPacked` array.\n * @param bigEndianMod Modifier for whether hash function is big or small endian.\n * @returns Hashmap of the packed values.\n */\nfunction uint8array2packed(\n  arr: Uint8Array,\n  existingPacked: number[] | undefined,\n  existingPackedLen: number | undefined,\n  bigEndianMod: -1 | 1\n): packedValue {\n  let i, intOffset, byteOffset;\n\n  existingPackedLen = existingPackedLen || 0;\n  const packed = existingPacked || [0],\n    existingByteLen = existingPackedLen >>> 3,\n    shiftModifier = bigEndianMod === -1 ? 3 : 0;\n\n  for (i = 0; i < arr.length; i += 1) {\n    byteOffset = i + existingByteLen;\n    intOffset = byteOffset >>> 2;\n    if (packed.length <= intOffset) {\n      packed.push(0);\n    }\n    packed[intOffset] |= arr[i] << (8 * (shiftModifier + bigEndianMod * (byteOffset % 4)));\n  }\n\n  return { value: packed, binLen: arr.length * 8 + existingPackedLen };\n}\n\n/**\n * Convert an ArrayBuffer to an array of words\n *\n * @param arr ArrayBuffer to be converted to binary representation.\n * @param existingPacked A packed int array of bytes to append the results to.\n * @param existingPackedLen The number of bits in `existingPacked` array.\n * @param bigEndianMod Modifier for whether hash function is big or small endian.\n * @returns Hashmap of the packed values.\n */\nfunction arraybuffer2packed(\n  arr: ArrayBuffer,\n  existingPacked: number[] | undefined,\n  existingPackedLen: number | undefined,\n  bigEndianMod: -1 | 1\n): packedValue {\n  return uint8array2packed(new Uint8Array(arr), existingPacked, existingPackedLen, bigEndianMod);\n}\n\n/**\n * Function that takes an input format and UTF encoding and returns the appropriate function used to convert the input.\n *\n * @param format The format of the input to be converted\n * @param utfType The string encoding to use for TEXT inputs.\n * @param bigEndianMod Modifier for whether hash function is big or small endian\n * @returns Function that will convert an input to a packed int array.\n */\nexport function getStrConverter(\n  format: FormatType,\n  utfType: EncodingType,\n  bigEndianMod: -1 | 1\n  /* eslint-disable-next-line @typescript-eslint/no-explicit-any */\n): (input: any, existingBin?: number[], existingBinLen?: number) => packedValue {\n  /* Validate encoding */\n  switch (utfType) {\n    case \"UTF8\":\n    /* Fallthrough */\n    case \"UTF16BE\":\n    /* Fallthrough */\n    case \"UTF16LE\":\n      /* Fallthrough */\n      break;\n    default:\n      throw new Error(\"encoding must be UTF8, UTF16BE, or UTF16LE\");\n  }\n\n  /* Map inputFormat to the appropriate converter */\n  switch (format) {\n    case \"HEX\":\n      /**\n       * @param str String of hexadecimal bytes to be converted to binary representation.\n       * @param existingPacked A packed int array of bytes to append the results to.\n       * @param existingPackedLen The number of bits in `existingPacked` array.\n       * @returns Hashmap of the packed values.\n       */\n      return function (str: string, existingBin?: number[], existingBinLen?: number): packedValue {\n        return hex2packed(str, existingBin, existingBinLen, bigEndianMod);\n      };\n    case \"TEXT\":\n      /**\n       * @param str Unicode string to be converted to binary representation.\n       * @param existingPacked A packed int array of bytes to append the results to.\n       * @param existingPackedLen The number of bits in `existingPacked` array.\n       * @returns Hashmap of the packed values.\n       */\n      return function (str: string, existingBin?: number[], existingBinLen?: number): packedValue {\n        return str2packed(str, utfType, existingBin, existingBinLen, bigEndianMod);\n      };\n    case \"B64\":\n      /**\n       * @param str Base64-encoded string to be converted to binary representation.\n       * @param existingPacked A packed int array of bytes to append the results to.\n       * @param existingPackedLen The number of bits in `existingPacked` array.\n       * @returns Hashmap of the packed values.\n       */\n      return function (str: string, existingBin?: number[], existingBinLen?: number): packedValue {\n        return b642packed(str, existingBin, existingBinLen, bigEndianMod);\n      };\n    case \"BYTES\":\n      /**\n       * @param str String of raw bytes to be converted to binary representation.\n       * @param existingPacked A packed int array of bytes to append the results to.\n       * @param existingPackedLen The number of bits in `existingPacked` array.\n       * @returns Hashmap of the packed values.\n       */\n      return function (str: string, existingBin?: number[], existingBinLen?: number): packedValue {\n        return bytes2packed(str, existingBin, existingBinLen, bigEndianMod);\n      };\n    case \"ARRAYBUFFER\":\n      try {\n        new ArrayBuffer(0);\n      } catch (ignore) {\n        throw new Error(arraybuffer_error);\n      }\n      /**\n       * @param arr ArrayBuffer to be converted to binary representation.\n       * @param existingPacked A packed int array of bytes to append the results to.\n       * @param existingPackedLen The number of bits in `existingPacked` array.\n       * @returns Hashmap of the packed values.\n       */\n      return function (arr: ArrayBuffer, existingBin?: number[], existingBinLen?: number): packedValue {\n        return arraybuffer2packed(arr, existingBin, existingBinLen, bigEndianMod);\n      };\n    case \"UINT8ARRAY\":\n      try {\n        new Uint8Array(0);\n      } catch (ignore) {\n        throw new Error(uint8array_error);\n      }\n      /**\n       * @param arr Uint8Array to be converted to binary representation.\n       * @param existingPacked A packed int array of bytes to append the results to.\n       * @param existingPackedLen The number of bits in `existingPacked` array.\n       * @returns Hashmap of the packed values.\n       */\n      return function (arr: Uint8Array, existingBin?: number[], existingBinLen?: number): packedValue {\n        return uint8array2packed(arr, existingBin, existingBinLen, bigEndianMod);\n      };\n    default:\n      throw new Error(\"format must be HEX, TEXT, B64, BYTES, ARRAYBUFFER, or UINT8ARRAY\");\n  }\n}\n\n/**\n * Convert an array of words to a hexadecimal string.\n *\n * toString() won't work here because it removes preceding zeros (e.g. 0x00000001.toString === \"1\" rather than\n * \"00000001\" and 0.toString(16) === \"0\" rather than \"00\").\n *\n * @param packed Array of integers to be converted.\n * @param outputLength Length of output in bits.\n * @param bigEndianMod Modifier for whether hash function is big or small endian.\n * @param formatOpts Hashmap containing validated output formatting options.\n * @returns Hexadecimal representation of `packed`.\n */\nexport function packed2hex(\n  packed: number[],\n  outputLength: number,\n  bigEndianMod: -1 | 1,\n  formatOpts: { outputUpper: boolean; b64Pad: string }\n): string {\n  const hex_tab = \"0123456789abcdef\";\n  let str = \"\",\n    i,\n    srcByte;\n\n  const length = outputLength / 8,\n    shiftModifier = bigEndianMod === -1 ? 3 : 0;\n\n  for (i = 0; i < length; i += 1) {\n    /* The below is more than a byte but it gets taken care of later */\n    srcByte = packed[i >>> 2] >>> (8 * (shiftModifier + bigEndianMod * (i % 4)));\n    str += hex_tab.charAt((srcByte >>> 4) & 0xf) + hex_tab.charAt(srcByte & 0xf);\n  }\n\n  return formatOpts[\"outputUpper\"] ? str.toUpperCase() : str;\n}\n\n/**\n * Convert an array of words to a base-64 string.\n *\n * @param packed Array of integers to be converted.\n * @param outputLength Length of output in bits.\n * @param bigEndianMod Modifier for whether hash function is big or small endian.\n * @param formatOpts Hashmap containing validated output formatting options.\n * @returns Base64-encoded representation of `packed`.\n */\nexport function packed2b64(\n  packed: number[],\n  outputLength: number,\n  bigEndianMod: -1 | 1,\n  formatOpts: { outputUpper: boolean; b64Pad: string }\n): string {\n  let str = \"\",\n    i,\n    j,\n    triplet,\n    int1,\n    int2;\n\n  const length = outputLength / 8,\n    shiftModifier = bigEndianMod === -1 ? 3 : 0;\n\n  for (i = 0; i < length; i += 3) {\n    int1 = i + 1 < length ? packed[(i + 1) >>> 2] : 0;\n    int2 = i + 2 < length ? packed[(i + 2) >>> 2] : 0;\n    triplet =\n      (((packed[i >>> 2] >>> (8 * (shiftModifier + bigEndianMod * (i % 4)))) & 0xff) << 16) |\n      (((int1 >>> (8 * (shiftModifier + bigEndianMod * ((i + 1) % 4)))) & 0xff) << 8) |\n      ((int2 >>> (8 * (shiftModifier + bigEndianMod * ((i + 2) % 4)))) & 0xff);\n    for (j = 0; j < 4; j += 1) {\n      if (i * 8 + j * 6 <= outputLength) {\n        str += b64Tab.charAt((triplet >>> (6 * (3 - j))) & 0x3f);\n      } else {\n        str += formatOpts[\"b64Pad\"];\n      }\n    }\n  }\n  return str;\n}\n\n/**\n * Convert an array of words to raw bytes string.\n *\n * @param packed Array of integers to be converted.\n * @param outputLength Length of output in bits.\n * @param bigEndianMod Modifier for whether hash function is big or small endian.\n * @returns Raw bytes representation of `packed`.\n */\nexport function packed2bytes(packed: number[], outputLength: number, bigEndianMod: -1 | 1): string {\n  let str = \"\",\n    i,\n    srcByte;\n\n  const length = outputLength / 8,\n    shiftModifier = bigEndianMod === -1 ? 3 : 0;\n\n  for (i = 0; i < length; i += 1) {\n    srcByte = (packed[i >>> 2] >>> (8 * (shiftModifier + bigEndianMod * (i % 4)))) & 0xff;\n    str += String.fromCharCode(srcByte);\n  }\n\n  return str;\n}\n\n/**\n * Convert an array of words to an ArrayBuffer.\n *\n * @param packed Array of integers to be converted.\n * @param outputLength Length of output in bits.\n * @param bigEndianMod Modifier for whether hash function is big or small endian.\n * @returns An ArrayBuffer containing bytes from `packed.\n */\nexport function packed2arraybuffer(packed: number[], outputLength: number, bigEndianMod: -1 | 1): ArrayBuffer {\n  let i;\n  const length = outputLength / 8,\n    retVal = new ArrayBuffer(length),\n    arrView = new Uint8Array(retVal),\n    shiftModifier = bigEndianMod === -1 ? 3 : 0;\n\n  for (i = 0; i < length; i += 1) {\n    arrView[i] = (packed[i >>> 2] >>> (8 * (shiftModifier + bigEndianMod * (i % 4)))) & 0xff;\n  }\n\n  return retVal;\n}\n\n/**\n * Convert an array of words to an Uint8Array.\n *\n * @param packed Array of integers to be converted.\n * @param outputLength Length of output in bits.\n * @param bigEndianMod Modifier for whether hash function is big or small endian.\n * @returns An Uint8Array containing bytes from `packed.\n */\nexport function packed2uint8array(packed: number[], outputLength: number, bigEndianMod: -1 | 1): Uint8Array {\n  let i;\n  const length = outputLength / 8,\n    shiftModifier = bigEndianMod === -1 ? 3 : 0,\n    retVal = new Uint8Array(length);\n\n  for (i = 0; i < length; i += 1) {\n    retVal[i] = (packed[i >>> 2] >>> (8 * (shiftModifier + bigEndianMod * (i % 4)))) & 0xff;\n  }\n\n  return retVal;\n}\n\n/**\n * Function that takes an output format and associated parameters and returns a function that converts packed integers\n * to that format.\n *\n * @param format The desired output formatting.\n * @param outputBinLen Output length in bits.\n * @param bigEndianMod Modifier for whether hash function is big or small endian.\n * @param outputOptions Hashmap of output formatting options\n * @returns Function that will convert a packed integer array to desired format.\n */\nexport function getOutputConverter(\n  format: \"HEX\" | \"B64\" | \"BYTES\",\n  outputBinLen: number,\n  bigEndianMod: -1 | 1,\n  outputOptions: { outputUpper: boolean; b64Pad: string }\n): (binarray: number[]) => string;\nexport function getOutputConverter(\n  format: \"ARRAYBUFFER\",\n  outputBinLen: number,\n  bigEndianMod: -1 | 1,\n  outputOptions: { outputUpper: boolean; b64Pad: string }\n): (binarray: number[]) => ArrayBuffer;\nexport function getOutputConverter(\n  format: \"UINT8ARRAY\",\n  outputBinLen: number,\n  bigEndianMod: -1 | 1,\n  outputOptions: { outputUpper: boolean; b64Pad: string }\n): (binarray: number[]) => Uint8Array;\n/* eslint-disable-next-line @typescript-eslint/no-explicit-any */\nexport function getOutputConverter(format: any, outputBinLen: any, bigEndianMod: any, outputOptions: any): any {\n  switch (format) {\n    case \"HEX\":\n      return function (binarray: number[]): string {\n        return packed2hex(binarray, outputBinLen, bigEndianMod, outputOptions);\n      };\n    case \"B64\":\n      return function (binarray: number[]): string {\n        return packed2b64(binarray, outputBinLen, bigEndianMod, outputOptions);\n      };\n    case \"BYTES\":\n      return function (binarray: number[]): string {\n        return packed2bytes(binarray, outputBinLen, bigEndianMod);\n      };\n    case \"ARRAYBUFFER\":\n      try {\n        /* Need to test ArrayBuffer support */\n        new ArrayBuffer(0);\n      } catch (ignore) {\n        throw new Error(arraybuffer_error);\n      }\n      return function (binarray: number[]): ArrayBuffer {\n        return packed2arraybuffer(binarray, outputBinLen, bigEndianMod);\n      };\n    case \"UINT8ARRAY\":\n      try {\n        /* Need to test Uint8Array support */\n        new Uint8Array(0);\n      } catch (ignore) {\n        throw new Error(uint8array_error);\n      }\n      return function (binarray: number[]): Uint8Array {\n        return packed2uint8array(binarray, outputBinLen, bigEndianMod);\n      };\n    default:\n      throw new Error(\"format must be HEX, B64, BYTES, ARRAYBUFFER, or UINT8ARRAY\");\n  }\n}\n", "import { getStrConverter, getOutputConverter } from \"./converters\";\n\nimport {\n  FormatType,\n  EncodingType,\n  FixedLengthOptionsEncodingType,\n  FixedLengthOptionsNoEncodingType,\n  FormatNoTextType,\n  packedValue,\n  GenericInputType,\n} from \"./custom_types\";\n\nexport const TWO_PWR_32 = 4294967296;\n\n/* Constant used in SHA-2 families */\nexport const K_sha2 = [\n  0x428a2f98, 0x71374491, 0xb5c0fbcf, 0xe9b5dba5, 0x3956c25b, 0x59f111f1, 0x923f82a4, 0xab1c5ed5, 0xd807aa98,\n  0x12835b01, 0x243185be, 0x550c7dc3, 0x72be5d74, 0x80deb1fe, 0x9bdc06a7, 0xc19bf174, 0xe49b69c1, 0xefbe4786,\n  0x0fc19dc6, 0x240ca1cc, 0x2de92c6f, 0x4a7484aa, 0x5cb0a9dc, 0x76f988da, 0x983e5152, 0xa831c66d, 0xb00327c8,\n  0xbf597fc7, 0xc6e00bf3, 0xd5a79147, 0x06ca6351, 0x14292967, 0x27b70a85, 0x2e1b2138, 0x4d2c6dfc, 0x53380d13,\n  0x650a7354, 0x766a0abb, 0x81c2c92e, 0x92722c85, 0xa2bfe8a1, 0xa81a664b, 0xc24b8b70, 0xc76c51a3, 0xd192e819,\n  0xd6990624, 0xf40e3585, 0x106aa070, 0x19a4c116, 0x1e376c08, 0x2748774c, 0x34b0bcb5, 0x391c0cb3, 0x4ed8aa4a,\n  0x5b9cca4f, 0x682e6ff3, 0x748f82ee, 0x78a5636f, 0x84c87814, 0x8cc70208, 0x90befffa, 0xa4506ceb, 0xbef9a3f7,\n  0xc67178f2,\n];\n\n/* Constant used in SHA-2 families */\nexport const H_trunc = [0xc1059ed8, 0x367cd507, 0x3070dd17, 0xf70e5939, 0xffc00b31, 0x68581511, 0x64f98fa7, 0xbefa4fa4];\n\n/* Constant used in SHA-2 families */\nexport const H_full = [0x6a09e667, 0xbb67ae85, 0x3c6ef372, 0xa54ff53a, 0x510e527f, 0x9b05688c, 0x1f83d9ab, 0x5be0cd19];\n\nexport const sha_variant_error = \"Chosen SHA variant is not supported\";\nexport const mac_rounds_error = \"Cannot set numRounds with MAC\";\n\n/**\n * Concatenates 2 packed arrays. Clobbers array `a`.\n *\n * @param a First array to concatenate.\n * @param b Second array to concatenate.\n * @returns The concatentation of `a` + `b`.\n */\nexport function packedLEConcat(a: packedValue, b: packedValue): packedValue {\n  let i, arrOffset;\n  const aByteLen = a[\"binLen\"] >>> 3,\n    bByteLen = b[\"binLen\"] >>> 3,\n    leftShiftAmount = aByteLen << 3,\n    rightShiftAmount = (4 - aByteLen) << 3;\n\n  /* If a only contains \"full\" integers, we can just use concat which is so much easier */\n  if (aByteLen % 4 !== 0) {\n    for (i = 0; i < bByteLen; i += 4) {\n      arrOffset = (aByteLen + i) >>> 2;\n      /* Left shift chops off bits over 32-bits */\n      a[\"value\"][arrOffset] |= b[\"value\"][i >>> 2] << leftShiftAmount;\n      a[\"value\"].push(0);\n      a[\"value\"][arrOffset + 1] |= b[\"value\"][i >>> 2] >>> rightShiftAmount;\n    }\n\n    /* Since an unconditional push was performed above, we may have pushed an extra value if it could have been\n       encoded without it.  Check if popping an int off (reducing total length by 4 bytes) is still bigger than the\n       needed size. */\n    if ((a[\"value\"].length << 2) - 4 >= bByteLen + aByteLen) {\n      a[\"value\"].pop();\n    }\n\n    return { value: a[\"value\"], binLen: a[\"binLen\"] + b[\"binLen\"] };\n  } else {\n    return { value: a[\"value\"].concat(b[\"value\"]), binLen: a[\"binLen\"] + b[\"binLen\"] };\n  }\n}\n\n/**\n * Validate hash list containing output formatting options, ensuring presence of every option or adding the default\n * value.\n *\n * @param options Hashmap of output formatting options from user.\n * @returns Validated hashmap containing output formatting options.\n */\nexport function getOutputOpts(options?: {\n  outputUpper?: boolean;\n  b64Pad?: string;\n  shakeLen?: number;\n  outputLen?: number;\n}): { outputUpper: boolean; b64Pad: string; outputLen: number } {\n  const retVal = { outputUpper: false, b64Pad: \"=\", outputLen: -1 },\n    outputOptions: { outputUpper?: boolean; b64Pad?: string; shakeLen?: number; outputLen?: number } = options || {},\n    lenErrstr = \"Output length must be a multiple of 8\";\n\n  retVal[\"outputUpper\"] = outputOptions[\"outputUpper\"] || false;\n\n  if (outputOptions[\"b64Pad\"]) {\n    retVal[\"b64Pad\"] = outputOptions[\"b64Pad\"];\n  }\n\n  if (outputOptions[\"outputLen\"]) {\n    if (outputOptions[\"outputLen\"] % 8 !== 0) {\n      throw new Error(lenErrstr);\n    }\n    retVal[\"outputLen\"] = outputOptions[\"outputLen\"];\n  } else if (outputOptions[\"shakeLen\"]) {\n    if (outputOptions[\"shakeLen\"] % 8 !== 0) {\n      throw new Error(lenErrstr);\n    }\n    retVal[\"outputLen\"] = outputOptions[\"shakeLen\"];\n  }\n\n  if (\"boolean\" !== typeof retVal[\"outputUpper\"]) {\n    throw new Error(\"Invalid outputUpper formatting option\");\n  }\n\n  if (\"string\" !== typeof retVal[\"b64Pad\"]) {\n    throw new Error(\"Invalid b64Pad formatting option\");\n  }\n\n  return retVal;\n}\n\n/**\n * Parses an external constructor object and returns a packed number, if possible.\n *\n * @param key The human-friendly key name to prefix any errors with\n * @param value The input value object to parse\n * @param bigEndianMod Modifier for whether hash function is big or small endian.\n * @param fallback Fallback value if `value` is undefined.  If not present and `value` is undefined, an Error is thrown\n */\nexport function parseInputOption(\n  key: string,\n  value: GenericInputType | undefined,\n  bigEndianMod: -1 | 1,\n  fallback?: packedValue\n): packedValue {\n  const errStr = key + \" must include a value and format\";\n  if (!value) {\n    if (!fallback) {\n      throw new Error(errStr);\n    }\n    return fallback;\n  }\n\n  if (typeof value[\"value\"] === \"undefined\" || !value[\"format\"]) {\n    throw new Error(errStr);\n  }\n\n  return getStrConverter(\n    value[\"format\"],\n    // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n    // @ts-ignore - the value of encoding gets value checked by getStrConverter\n    value[\"encoding\"] || \"UTF8\",\n    bigEndianMod\n  )(value[\"value\"]);\n}\n\nexport abstract class jsSHABase<StateT, VariantT> {\n  /**\n   * @param variant The desired SHA variant.\n   * @param inputFormat The input format to be used in future `update` calls.\n   * @param options Hashmap of extra input options.\n   */\n  /* Needed inputs */\n  protected readonly shaVariant: VariantT;\n  protected readonly inputFormat: FormatType;\n  protected readonly utfType: EncodingType;\n  protected readonly numRounds: number;\n\n  /* State */\n  protected abstract intermediateState: StateT;\n  protected keyWithIPad: number[];\n  protected keyWithOPad: number[];\n  protected remainder: number[];\n  protected remainderLen: number;\n  protected updateCalled: boolean;\n  protected processedLen: number;\n  protected macKeySet: boolean;\n\n  /* Variant specifics */\n  protected abstract readonly variantBlockSize: number;\n  protected abstract readonly bigEndianMod: -1 | 1;\n  protected abstract readonly outputBinLen: number;\n  protected abstract readonly isVariableLen: boolean;\n  protected abstract readonly HMACSupported: boolean;\n\n  /* Functions */\n  /* eslint-disable-next-line @typescript-eslint/no-explicit-any */\n  protected abstract readonly converterFunc: (input: any, existingBin: number[], existingBinLen: number) => packedValue;\n  protected abstract readonly roundFunc: (block: number[], H: StateT) => StateT;\n  protected abstract readonly finalizeFunc: (\n    remainder: number[],\n    remainderBinLen: number,\n    processedBinLen: number,\n    H: StateT,\n    outputLen: number\n  ) => number[];\n  protected abstract readonly stateCloneFunc: (state: StateT) => StateT;\n  protected abstract readonly newStateFunc: (variant: VariantT) => StateT;\n  protected abstract readonly getMAC: ((options: { outputLen: number }) => number[]) | null;\n\n  protected constructor(variant: VariantT, inputFormat: \"TEXT\", options?: FixedLengthOptionsEncodingType);\n  protected constructor(variant: VariantT, inputFormat: FormatNoTextType, options?: FixedLengthOptionsNoEncodingType);\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  protected constructor(variant: any, inputFormat: any, options?: any) {\n    const inputOptions = options || {};\n    this.inputFormat = inputFormat;\n\n    this.utfType = inputOptions[\"encoding\"] || \"UTF8\";\n    this.numRounds = inputOptions[\"numRounds\"] || 1;\n\n    /* eslint-disable-next-line @typescript-eslint/ban-ts-comment */\n    // @ts-ignore - The spec actually says ToString is called on the first parseInt argument so it's OK to use it here\n    // to check if an arugment is an integer. This cheat would break if it's used to get the value of the argument.\n    if (isNaN(this.numRounds) || this.numRounds !== parseInt(this.numRounds, 10) || 1 > this.numRounds) {\n      throw new Error(\"numRounds must a integer >= 1\");\n    }\n\n    this.shaVariant = variant;\n    this.remainder = [];\n    this.remainderLen = 0;\n    this.updateCalled = false;\n    this.processedLen = 0;\n    this.macKeySet = false;\n    this.keyWithIPad = [];\n    this.keyWithOPad = [];\n  }\n\n  /**\n   * Hashes as many blocks as possible.  Stores the rest for either a future update or getHash call.\n   *\n   * @param srcString The input to be hashed.\n   * @returns A reference to the object.\n   */\n  update(srcString: string | ArrayBuffer | Uint8Array): this {\n    let i,\n      updateProcessedLen = 0;\n    const variantBlockIntInc = this.variantBlockSize >>> 5,\n      convertRet = this.converterFunc(srcString, this.remainder, this.remainderLen),\n      chunkBinLen = convertRet[\"binLen\"],\n      chunk = convertRet[\"value\"],\n      chunkIntLen = chunkBinLen >>> 5;\n\n    for (i = 0; i < chunkIntLen; i += variantBlockIntInc) {\n      if (updateProcessedLen + this.variantBlockSize <= chunkBinLen) {\n        this.intermediateState = this.roundFunc(chunk.slice(i, i + variantBlockIntInc), this.intermediateState);\n        updateProcessedLen += this.variantBlockSize;\n      }\n    }\n    this.processedLen += updateProcessedLen;\n    this.remainder = chunk.slice(updateProcessedLen >>> 5);\n    this.remainderLen = chunkBinLen % this.variantBlockSize;\n    this.updateCalled = true;\n\n    return this;\n  }\n\n  /**\n   * Returns the desired SHA hash of the input fed in via `update` calls.\n   *\n   * @param format The desired output formatting\n   * @param options Hashmap of output formatting options. `outputLen` must be specified for variable length hashes.\n   *   `outputLen` replaces the now deprecated `shakeLen` key.\n   * @returns The hash in the format specified.\n   */\n  getHash(format: \"HEX\", options?: { outputUpper?: boolean; outputLen?: number; shakeLen?: number }): string;\n  getHash(format: \"B64\", options?: { b64Pad?: string; outputLen?: number; shakeLen?: number }): string;\n  getHash(format: \"BYTES\", options?: { outputLen?: number; shakeLen?: number }): string;\n  getHash(format: \"UINT8ARRAY\", options?: { outputLen?: number; shakeLen?: number }): Uint8Array;\n  getHash(format: \"ARRAYBUFFER\", options?: { outputLen?: number; shakeLen?: number }): ArrayBuffer;\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  getHash(format: any, options?: any): any {\n    let i,\n      finalizedState,\n      outputBinLen = this.outputBinLen;\n\n    const outputOptions = getOutputOpts(options);\n\n    if (this.isVariableLen) {\n      if (outputOptions[\"outputLen\"] === -1) {\n        throw new Error(\"Output length must be specified in options\");\n      }\n      outputBinLen = outputOptions[\"outputLen\"];\n    }\n\n    const formatFunc = getOutputConverter(format, outputBinLen, this.bigEndianMod, outputOptions);\n    if (this.macKeySet && this.getMAC) {\n      return formatFunc(this.getMAC(outputOptions));\n    }\n\n    finalizedState = this.finalizeFunc(\n      this.remainder.slice(),\n      this.remainderLen,\n      this.processedLen,\n      this.stateCloneFunc(this.intermediateState),\n      outputBinLen\n    );\n    for (i = 1; i < this.numRounds; i += 1) {\n      /* Need to mask out bits that should be zero due to output not being a multiple of 32 */\n      if (this.isVariableLen && outputBinLen % 32 !== 0) {\n        finalizedState[finalizedState.length - 1] &= 0x00ffffff >>> (24 - (outputBinLen % 32));\n      }\n      finalizedState = this.finalizeFunc(\n        finalizedState,\n        outputBinLen,\n        0,\n        this.newStateFunc(this.shaVariant),\n        outputBinLen\n      );\n    }\n\n    return formatFunc(finalizedState);\n  }\n\n  /**\n   * Sets the HMAC key for an eventual `getHMAC` call.  Must be called immediately after jsSHA object instantiation.\n   *\n   * @param key The key used to calculate the HMAC\n   * @param inputFormat The format of key.\n   * @param options Hashmap of extra input options.\n   */\n  setHMACKey(key: string, inputFormat: \"TEXT\", options?: { encoding?: EncodingType }): void;\n  setHMACKey(key: string, inputFormat: \"B64\" | \"HEX\" | \"BYTES\"): void;\n  setHMACKey(key: ArrayBuffer, inputFormat: \"ARRAYBUFFER\"): void;\n  setHMACKey(key: Uint8Array, inputFormat: \"UINT8ARRAY\"): void;\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  setHMACKey(key: any, inputFormat: any, options?: any): void {\n    if (!this.HMACSupported) {\n      throw new Error(\"Variant does not support HMAC\");\n    }\n\n    if (this.updateCalled) {\n      throw new Error(\"Cannot set MAC key after calling update\");\n    }\n\n    const keyOptions = options || {},\n      keyConverterFunc = getStrConverter(inputFormat, keyOptions[\"encoding\"] || \"UTF8\", this.bigEndianMod);\n\n    this._setHMACKey(keyConverterFunc(key));\n  }\n\n  /**\n   * Internal function that sets the MAC key.\n   *\n   * @param key The packed MAC key to use\n   */\n  protected _setHMACKey(key: packedValue): void {\n    const blockByteSize = this.variantBlockSize >>> 3,\n      lastArrayIndex = blockByteSize / 4 - 1;\n    let i;\n    if (this.numRounds !== 1) {\n      throw new Error(mac_rounds_error);\n    }\n\n    if (this.macKeySet) {\n      throw new Error(\"MAC key already set\");\n    }\n\n    /* Figure out what to do with the key based on its size relative to\n     * the hash's block size */\n    if (blockByteSize < key[\"binLen\"] / 8) {\n      key[\"value\"] = this.finalizeFunc(\n        key[\"value\"],\n        key[\"binLen\"],\n        0,\n        this.newStateFunc(this.shaVariant),\n        this.outputBinLen\n      );\n    }\n    while (key[\"value\"].length <= lastArrayIndex) {\n      key[\"value\"].push(0);\n    }\n    /* Create ipad and opad */\n    for (i = 0; i <= lastArrayIndex; i += 1) {\n      this.keyWithIPad[i] = key[\"value\"][i] ^ 0x36363636;\n      this.keyWithOPad[i] = key[\"value\"][i] ^ 0x5c5c5c5c;\n    }\n\n    this.intermediateState = this.roundFunc(this.keyWithIPad, this.intermediateState);\n    this.processedLen = this.variantBlockSize;\n\n    this.macKeySet = true;\n  }\n\n  /**\n   * Returns the the HMAC in the specified format using the key given by a previous `setHMACKey` call.\n   *\n   * @param format The desired output formatting.\n   * @param options Hashmap of extra outputs options.\n   * @returns The HMAC in the format specified.\n   */\n  getHMAC(format: \"HEX\", options?: { outputUpper?: boolean }): string;\n  getHMAC(format: \"B64\", options?: { b64Pad?: string }): string;\n  getHMAC(format: \"BYTES\"): string;\n  getHMAC(format: \"UINT8ARRAY\"): Uint8Array;\n  getHMAC(format: \"ARRAYBUFFER\"): ArrayBuffer;\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  getHMAC(format: any, options?: any): any {\n    const outputOptions = getOutputOpts(options),\n      formatFunc = getOutputConverter(format, this.outputBinLen, this.bigEndianMod, outputOptions);\n\n    return formatFunc(this._getHMAC());\n  }\n\n  /**\n   * Internal function that returns the \"raw\" HMAC\n   */\n  protected _getHMAC(): number[] {\n    let finalizedState;\n\n    if (!this.macKeySet) {\n      throw new Error(\"Cannot call getHMAC without first setting MAC key\");\n    }\n\n    const firstHash = this.finalizeFunc(\n      this.remainder.slice(),\n      this.remainderLen,\n      this.processedLen,\n      this.stateCloneFunc(this.intermediateState),\n      this.outputBinLen\n    );\n    finalizedState = this.roundFunc(this.keyWithOPad, this.newStateFunc(this.shaVariant));\n    finalizedState = this.finalizeFunc(\n      firstHash,\n      this.outputBinLen,\n      this.variantBlockSize,\n      finalizedState,\n      this.outputBinLen\n    );\n\n    return finalizedState;\n  }\n}\n", "/*\n * Note 1: All the functions in this file guarantee only that the bottom 32-bits of the return value are correct.\n * JavaScript is flakey when it comes to bit operations and a '1' in the highest order bit of a 32-bit number causes\n * it to be interpreted as a negative number per two's complement.\n *\n * Note 2: Per the ECMAScript spec, all JavaScript operations mask the shift amount by 0x1F.  This results in weird\n * cases like 1 << 32 == 1 and 1 << 33 === 1 << 1 === 2\n */\n\n/**\n * The 32-bit implementation of circular rotate left.\n *\n * @param x The 32-bit integer argument.\n * @param n The number of bits to shift.\n * @returns `x` shifted left circularly by `n` bits\n */\nexport function rotl_32(x: number, n: number): number {\n  return (x << n) | (x >>> (32 - n));\n}\n\n/**\n * The 32-bit implementation of circular rotate right.\n *\n * @param x The 32-bit integer argument.\n * @param n The number of bits to shift.\n * @returns `x` shifted right circularly by `n` bits\n */\nfunction rotr_32(x: number, n: number): number {\n  return (x >>> n) | (x << (32 - n));\n}\n\n/**\n * The 32-bit implementation of shift right.\n *\n * @param x The 32-bit integer argument.\n * @param n The number of bits to shift.\n * @returns `x` shifted by `n` bits.\n */\nfunction shr_32(x: number, n: number): number {\n  return x >>> n;\n}\n\n/**\n * The 32-bit implementation of the NIST specified Parity function.\n *\n * @param x The first 32-bit integer argument.\n * @param y The second 32-bit integer argument.\n * @param z The third 32-bit integer argument.\n * @returns The NIST specified output of the function.\n */\nexport function parity_32(x: number, y: number, z: number): number {\n  return x ^ y ^ z;\n}\n\n/**\n * The 32-bit implementation of the NIST specified Ch function.\n *\n * @param x The first 32-bit integer argument.\n * @param y The second 32-bit integer argument.\n * @param z The third 32-bit integer argument.\n * @returns The NIST specified output of the function.\n */\nexport function ch_32(x: number, y: number, z: number): number {\n  return (x & y) ^ (~x & z);\n}\n\n/**\n * The 32-bit implementation of the NIST specified Maj function.\n *\n * @param x The first 32-bit integer argument.\n * @param y The second 32-bit integer argument.\n * @param z The third 32-bit integer argument.\n * @returns The NIST specified output of the function.\n */\nexport function maj_32(x: number, y: number, z: number): number {\n  return (x & y) ^ (x & z) ^ (y & z);\n}\n\n/**\n * The 32-bit implementation of the NIST specified Sigma0 function.\n *\n * @param x The 32-bit integer argument.\n * @returns The NIST specified output of the function.\n */\nexport function sigma0_32(x: number): number {\n  return rotr_32(x, 2) ^ rotr_32(x, 13) ^ rotr_32(x, 22);\n}\n\n/**\n * Add two 32-bit integers.\n *\n * This uses 16-bit operations internally to work around sign problems due to JavaScript's lack of uint32 support.\n *\n * @param a The first 32-bit integer argument to be added.\n * @param b The second 32-bit integer argument to be added.\n * @returns The sum of `a` + `b`.\n */\nexport function safeAdd_32_2(a: number, b: number): number {\n  const lsw = (a & 0xffff) + (b & 0xffff),\n    msw = (a >>> 16) + (b >>> 16) + (lsw >>> 16);\n\n  return ((msw & 0xffff) << 16) | (lsw & 0xffff);\n}\n\n/**\n * Add four 32-bit integers.\n *\n * This uses 16-bit operations internally to work around sign problems due to JavaScript's lack of uint32 support.\n *\n * @param a The first 32-bit integer argument to be added.\n * @param b The second 32-bit integer argument to be added.\n * @param c The third 32-bit integer argument to be added.\n * @param d The fourth 32-bit integer argument to be added.\n * @returns The sum of `a` + `b` + `c` + `d`.\n */\nexport function safeAdd_32_4(a: number, b: number, c: number, d: number): number {\n  const lsw = (a & 0xffff) + (b & 0xffff) + (c & 0xffff) + (d & 0xffff),\n    msw = (a >>> 16) + (b >>> 16) + (c >>> 16) + (d >>> 16) + (lsw >>> 16);\n\n  return ((msw & 0xffff) << 16) | (lsw & 0xffff);\n}\n\n/**\n * Add five 32-bit integers.\n *\n * This uses 16-bit operations internally to work around sign problems due to JavaScript's lack of uint32 support.\n *\n * @param a The first 32-bit integer argument to be added.\n * @param b The second 32-bit integer argument to be added.\n * @param c The third 32-bit integer argument to be added.\n * @param d The fourth 32-bit integer argument to be added.\n * @param e The fifth 32-bit integer argument to be added.\n * @returns The sum of `a` + `b` + `c` + `d` + `e`.\n */\nexport function safeAdd_32_5(a: number, b: number, c: number, d: number, e: number): number {\n  const lsw = (a & 0xffff) + (b & 0xffff) + (c & 0xffff) + (d & 0xffff) + (e & 0xffff),\n    msw = (a >>> 16) + (b >>> 16) + (c >>> 16) + (d >>> 16) + (e >>> 16) + (lsw >>> 16);\n\n  return ((msw & 0xffff) << 16) | (lsw & 0xffff);\n}\n\n/**\n * The 32-bit implementation of the NIST specified Gamma1 function.\n *\n * @param x The 32-bit integer argument.\n * @returns The NIST specified output of the function.\n */\nexport function gamma1_32(x: number): number {\n  return rotr_32(x, 17) ^ rotr_32(x, 19) ^ shr_32(x, 10);\n}\n\n/**\n * The 32-bit implementation of the NIST specified Gamma0 function.\n *\n * @param x The 32-bit integer argument.\n * @returns The NIST specified output of the function.\n */\nexport function gamma0_32(x: number): number {\n  return rotr_32(x, 7) ^ rotr_32(x, 18) ^ shr_32(x, 3);\n}\n\n/**\n * The 32-bit implementation of the NIST specified Sigma1 function.\n *\n * @param x The 32-bit integer argument.\n * @returns The NIST specified output of the function.\n */\nexport function sigma1_32(x: number): number {\n  return rotr_32(x, 6) ^ rotr_32(x, 11) ^ rotr_32(x, 25);\n}\n", "import { jsSHABase, TWO_PWR_32, sha_variant_error, parseInputOption } from \"./common\";\nimport {\n  packedValue,\n  FixedLengthOptionsEncodingType,\n  FixedLengthOptionsNoEncodingType,\n  FormatNoTextType,\n} from \"./custom_types\";\nimport { getStrConverter } from \"./converters\";\nimport { ch_32, parity_32, maj_32, rotl_32, safeAdd_32_2, safeAdd_32_5 } from \"./primitives_32\";\n\n/**\n * Gets the state values for the specified SHA variant.\n *\n * @param _variant: Unused\n * @returns The initial state values.\n */\nfunction getNewState(_variant: \"SHA-1\"): number[] {\n  return [0x67452301, 0xefcdab89, 0x98badcfe, 0x10325476, 0xc3d2e1f0];\n}\n\n/**\n * Performs a round of SHA-1 hashing over a 512-byte block.  This clobbers `H`.\n *\n * @param block The binary array representation of the block to hash.\n * @param H The intermediate H values from a previous round.\n * @returns The resulting H values.\n */\nfunction roundSHA1(block: number[], H: number[]): number[] {\n  let a, b, c, d, e, T, t;\n  const W: number[] = [];\n\n  a = H[0];\n  b = H[1];\n  c = H[2];\n  d = H[3];\n  e = H[4];\n\n  for (t = 0; t < 80; t += 1) {\n    if (t < 16) {\n      W[t] = block[t];\n    } else {\n      W[t] = rotl_32(W[t - 3] ^ W[t - 8] ^ W[t - 14] ^ W[t - 16], 1);\n    }\n\n    if (t < 20) {\n      T = safeAdd_32_5(rotl_32(a, 5), ch_32(b, c, d), e, 0x5a827999, W[t]);\n    } else if (t < 40) {\n      T = safeAdd_32_5(rotl_32(a, 5), parity_32(b, c, d), e, 0x6ed9eba1, W[t]);\n    } else if (t < 60) {\n      T = safeAdd_32_5(rotl_32(a, 5), maj_32(b, c, d), e, 0x8f1bbcdc, W[t]);\n    } else {\n      T = safeAdd_32_5(rotl_32(a, 5), parity_32(b, c, d), e, 0xca62c1d6, W[t]);\n    }\n\n    e = d;\n    d = c;\n    c = rotl_32(b, 30);\n    b = a;\n    a = T;\n  }\n\n  H[0] = safeAdd_32_2(a, H[0]);\n  H[1] = safeAdd_32_2(b, H[1]);\n  H[2] = safeAdd_32_2(c, H[2]);\n  H[3] = safeAdd_32_2(d, H[3]);\n  H[4] = safeAdd_32_2(e, H[4]);\n\n  return H;\n}\n\n/**\n * Finalizes the SHA-1 hash.  This clobbers `remainder` and `H`.\n *\n * @param remainder Any leftover unprocessed packed ints that still need to be processed.\n * @param remainderBinLen The number of bits in `remainder`.\n * @param processedBinLen The number of bits already processed.\n * @param H The intermediate H values from a previous round.\n * @returns The array of integers representing the SHA-1 hash of message.\n */\nfunction finalizeSHA1(remainder: number[], remainderBinLen: number, processedBinLen: number, H: number[]): number[] {\n  let i;\n\n  /* The 65 addition is a hack but it works.  The correct number is\n\t\tactually 72 (64 + 8) but the below math fails if\n\t\tremainderBinLen + 72 % 512 = 0. Since remainderBinLen % 8 = 0,\n\t\t\"shorting\" the addition is OK. */\n  const offset = (((remainderBinLen + 65) >>> 9) << 4) + 15,\n    totalLen = remainderBinLen + processedBinLen;\n  while (remainder.length <= offset) {\n    remainder.push(0);\n  }\n  /* Append '1' at the end of the binary string */\n  remainder[remainderBinLen >>> 5] |= 0x80 << (24 - (remainderBinLen % 32));\n\n  /* Append length of binary string in the position such that the new\n   * length is a multiple of 512.  Logic does not work for even multiples\n   * of 512 but there can never be even multiples of 512. JavaScript\n   * numbers are limited to 2^53 so it's \"safe\" to treat the totalLen as\n   * a 64-bit integer. */\n  remainder[offset] = totalLen & 0xffffffff;\n\n  /* Bitwise operators treat the operand as a 32-bit number so need to\n   * use hacky division and round to get access to upper 32-ish bits */\n  remainder[offset - 1] = (totalLen / TWO_PWR_32) | 0;\n\n  /* This will always be at least 1 full chunk */\n  for (i = 0; i < remainder.length; i += 16) {\n    H = roundSHA1(remainder.slice(i, i + 16), H);\n  }\n\n  return H;\n}\n\nexport default class jsSHA extends jsSHABase<number[], \"SHA-1\"> {\n  intermediateState: number[];\n  variantBlockSize: number;\n  bigEndianMod: -1 | 1;\n  outputBinLen: number;\n  isVariableLen: boolean;\n  HMACSupported: boolean;\n\n  /* eslint-disable-next-line @typescript-eslint/no-explicit-any */\n  converterFunc: (input: any, existingBin: number[], existingBinLen: number) => packedValue;\n  roundFunc: (block: number[], H: number[]) => number[];\n  finalizeFunc: (remainder: number[], remainderBinLen: number, processedBinLen: number, H: number[]) => number[];\n  stateCloneFunc: (state: number[]) => number[];\n  newStateFunc: (variant: \"SHA-1\") => number[];\n  getMAC: () => number[];\n\n  constructor(variant: \"SHA-1\", inputFormat: \"TEXT\", options?: FixedLengthOptionsEncodingType);\n  constructor(variant: \"SHA-1\", inputFormat: FormatNoTextType, options?: FixedLengthOptionsNoEncodingType);\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  constructor(variant: any, inputFormat: any, options?: any) {\n    if (\"SHA-1\" !== variant) {\n      throw new Error(sha_variant_error);\n    }\n    super(variant, inputFormat, options);\n    const resolvedOptions = options || {};\n\n    this.HMACSupported = true;\n    // eslint-disable-next-line @typescript-eslint/unbound-method\n    this.getMAC = this._getHMAC;\n    this.bigEndianMod = -1;\n    this.converterFunc = getStrConverter(this.inputFormat, this.utfType, this.bigEndianMod);\n    this.roundFunc = roundSHA1;\n    this.stateCloneFunc = function (state: number[]): number[] {\n      return state.slice();\n    };\n    this.newStateFunc = getNewState;\n    this.finalizeFunc = finalizeSHA1;\n\n    this.intermediateState = getNewState(variant);\n    this.variantBlockSize = 512;\n    this.outputBinLen = 160;\n    this.isVariableLen = false;\n\n    if (resolvedOptions[\"hmacKey\"]) {\n      this._setHMACKey(parseInputOption(\"hmacKey\", resolvedOptions[\"hmacKey\"], this.bigEndianMod));\n    }\n  }\n}\n", "import { jsS<PERSON><PERSON>ase, TWO_PWR_32, H_full, H_trunc, K_sha2, sha_variant_error, parseInputOption } from \"./common\";\nimport {\n  packedValue,\n  FixedLengthOptionsEncodingType,\n  FixedLengthOptionsNoEncodingType,\n  FormatNoTextType,\n} from \"./custom_types\";\nimport { getStrConverter } from \"./converters\";\nimport {\n  ch_32,\n  gamma0_32,\n  gamma1_32,\n  maj_32,\n  safeAdd_32_2,\n  safeAdd_32_4,\n  safeAdd_32_5,\n  sigma0_32,\n  sigma1_32,\n} from \"./primitives_32\";\n\ntype VariantType = \"SHA-224\" | \"SHA-256\";\n\n/**\n * Gets the state values for the specified SHA variant.\n *\n * @param variant: The SHA-256 family variant.\n * @returns The initial state values.\n */\nfunction getNewState256(variant: VariantType): number[] {\n  let retVal;\n\n  if (\"SHA-224\" == variant) {\n    retVal = H_trunc.slice();\n  } else {\n    /* \"SHA-256\" */\n    retVal = H_full.slice();\n  }\n  return retVal;\n}\n\n/**\n * Performs a round of SHA-256 hashing over a block. This clobbers `H`.\n *\n * @param block The binary array representation of the block to hash.\n * @param H The intermediate H values from a previous round.\n * @returns The resulting H values.\n */\nfunction roundSHA256(block: number[], H: number[]): number[] {\n  let a, b, c, d, e, f, g, h, T1, T2, t;\n\n  const W: number[] = [];\n\n  a = H[0];\n  b = H[1];\n  c = H[2];\n  d = H[3];\n  e = H[4];\n  f = H[5];\n  g = H[6];\n  h = H[7];\n\n  for (t = 0; t < 64; t += 1) {\n    if (t < 16) {\n      W[t] = block[t];\n    } else {\n      W[t] = safeAdd_32_4(gamma1_32(W[t - 2]), W[t - 7], gamma0_32(W[t - 15]), W[t - 16]);\n    }\n    T1 = safeAdd_32_5(h, sigma1_32(e), ch_32(e, f, g), K_sha2[t], W[t]);\n    T2 = safeAdd_32_2(sigma0_32(a), maj_32(a, b, c));\n    h = g;\n    g = f;\n    f = e;\n    e = safeAdd_32_2(d, T1);\n    d = c;\n    c = b;\n    b = a;\n    a = safeAdd_32_2(T1, T2);\n  }\n\n  H[0] = safeAdd_32_2(a, H[0]);\n  H[1] = safeAdd_32_2(b, H[1]);\n  H[2] = safeAdd_32_2(c, H[2]);\n  H[3] = safeAdd_32_2(d, H[3]);\n  H[4] = safeAdd_32_2(e, H[4]);\n  H[5] = safeAdd_32_2(f, H[5]);\n  H[6] = safeAdd_32_2(g, H[6]);\n  H[7] = safeAdd_32_2(h, H[7]);\n\n  return H;\n}\n\n/**\n * Finalizes the SHA-256 hash. This clobbers `remainder` and `H`.\n *\n * @param remainder Any leftover unprocessed packed ints that still need to be processed.\n * @param remainderBinLen The number of bits in `remainder`.\n * @param processedBinLen The number of bits already processed.\n * @param H The intermediate H values from a previous round.\n * @param variant The desired SHA-256 variant.\n * @returns The array of integers representing the SHA-2 hash of message.\n */\nfunction finalizeSHA256(\n  remainder: number[],\n  remainderBinLen: number,\n  processedBinLen: number,\n  H: number[],\n  variant: VariantType\n): number[] {\n  let i, retVal;\n\n  /* The 65 addition is a hack but it works.  The correct number is\n    actually 72 (64 + 8) but the below math fails if\n    remainderBinLen + 72 % 512 = 0. Since remainderBinLen % 8 = 0,\n    \"shorting\" the addition is OK. */\n  const offset = (((remainderBinLen + 65) >>> 9) << 4) + 15,\n    binaryStringInc = 16,\n    totalLen = remainderBinLen + processedBinLen;\n\n  while (remainder.length <= offset) {\n    remainder.push(0);\n  }\n  /* Append '1' at the end of the binary string */\n  remainder[remainderBinLen >>> 5] |= 0x80 << (24 - (remainderBinLen % 32));\n  /* Append length of binary string in the position such that the new\n   * length is correct. JavaScript numbers are limited to 2^53 so it's\n   * \"safe\" to treat the totalLen as a 64-bit integer. */\n\n  remainder[offset] = totalLen & 0xffffffff;\n  /* Bitwise operators treat the operand as a 32-bit number so need to\n   * use hacky division and round to get access to upper 32-ish bits */\n  remainder[offset - 1] = (totalLen / TWO_PWR_32) | 0;\n\n  /* This will always be at least 1 full chunk */\n  for (i = 0; i < remainder.length; i += binaryStringInc) {\n    H = roundSHA256(remainder.slice(i, i + binaryStringInc), H);\n  }\n\n  if (\"SHA-224\" === variant) {\n    retVal = [H[0], H[1], H[2], H[3], H[4], H[5], H[6]];\n  } else {\n    /* \"SHA-256 */\n    retVal = H;\n  }\n\n  return retVal;\n}\nexport default class jsSHA extends jsSHABase<number[], VariantType> {\n  intermediateState: number[];\n  variantBlockSize: number;\n  bigEndianMod: -1 | 1;\n  outputBinLen: number;\n  isVariableLen: boolean;\n  HMACSupported: boolean;\n\n  /* eslint-disable-next-line @typescript-eslint/no-explicit-any */\n  converterFunc: (input: any, existingBin: number[], existingBinLen: number) => packedValue;\n  roundFunc: (block: number[], H: number[]) => number[];\n  finalizeFunc: (remainder: number[], remainderBinLen: number, processedBinLen: number, H: number[]) => number[];\n  stateCloneFunc: (state: number[]) => number[];\n  newStateFunc: (variant: VariantType) => number[];\n  getMAC: () => number[];\n\n  constructor(variant: VariantType, inputFormat: \"TEXT\", options?: FixedLengthOptionsEncodingType);\n  constructor(variant: VariantType, inputFormat: FormatNoTextType, options?: FixedLengthOptionsNoEncodingType);\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  constructor(variant: any, inputFormat: any, options?: any) {\n    if (!(\"SHA-224\" === variant || \"SHA-256\" === variant)) {\n      throw new Error(sha_variant_error);\n    }\n    super(variant, inputFormat, options);\n    const resolvedOptions = options || {};\n\n    // eslint-disable-next-line @typescript-eslint/unbound-method\n    this.getMAC = this._getHMAC;\n    this.HMACSupported = true;\n    this.bigEndianMod = -1;\n    this.converterFunc = getStrConverter(this.inputFormat, this.utfType, this.bigEndianMod);\n    this.roundFunc = roundSHA256;\n    this.stateCloneFunc = function (state): number[] {\n      return state.slice();\n    };\n\n    this.newStateFunc = getNewState256;\n    this.finalizeFunc = function (remainder, remainderBinLen, processedBinLen, H): number[] {\n      return finalizeSHA256(remainder, remainderBinLen, processedBinLen, H, variant);\n    };\n\n    this.intermediateState = getNewState256(variant);\n    this.variantBlockSize = 512;\n    this.outputBinLen = \"SHA-224\" === variant ? 224 : 256;\n    this.isVariableLen = false;\n\n    if (resolvedOptions[\"hmacKey\"]) {\n      this._setHMACKey(parseInputOption(\"hmacKey\", resolvedOptions[\"hmacKey\"], this.bigEndianMod));\n    }\n  }\n}\n", "/*\n * Note 1: All the functions in this file guarantee only that the bottom 32-bits of the returned Int_64 are correct.\n * JavaScript is flakey when it comes to bit operations and a '1' in the highest order bit of a 32-bit number causes\n * it to be interpreted as a negative number per two's complement.\n *\n * Note 2: Per the ECMAScript spec, all JavaScript operations mask the shift amount by 0x1F.  This results in weird\n * cases like 1 << 32 == 1 and 1 << 33 === 1 << 1 === 2\n */\n\n/**\n * Int_64 is a object for 2 32-bit numbers emulating a 64-bit number.\n */\nexport class Int_64 {\n  /**\n   * @param msint_32 The most significant 32-bits of a 64-bit number.\n   * @param lsint_32 The least significant 32-bits of a 64-bit number.\n   */\n  readonly highOrder: number;\n  readonly lowOrder: number;\n  constructor(msint_32: number, lsint_32: number) {\n    this.highOrder = msint_32;\n    this.lowOrder = lsint_32;\n  }\n}\n\n/**\n * The 64-bit implementation of circular rotate left.\n *\n * This does not work for n >= 64 or n == 32 but those are never done.\n *\n * @param x The 64-bit integer argument.\n * @param n The number of bits to shift.\n * @returns `x` shifted left circularly by `n` bits.\n */\nexport function rotl_64(x: Int_64, n: number): Int_64 {\n  let tmp;\n  if (n > 32) {\n    tmp = 64 - n;\n    return new Int_64((x.lowOrder << n) | (x.highOrder >>> tmp), (x.highOrder << n) | (x.lowOrder >>> tmp));\n  } else if (0 !== n) {\n    tmp = 32 - n;\n    return new Int_64((x.highOrder << n) | (x.lowOrder >>> tmp), (x.lowOrder << n) | (x.highOrder >>> tmp));\n  } else {\n    return x;\n  }\n}\n\n/**\n * The 64-bit implementation of circular rotate right.\n *\n * This does not work for n >= 64, n == 32, or n == 0 but those are never done.\n *\n * @param x The 64-bit integer argument.\n * @param n The number of bits to shift.\n * @returns `x` shifted right circularly by `n` bits.\n */\nfunction rotr_64(x: Int_64, n: number): Int_64 {\n  let tmp;\n  if (n < 32) {\n    tmp = 32 - n;\n    return new Int_64((x.highOrder >>> n) | (x.lowOrder << tmp), (x.lowOrder >>> n) | (x.highOrder << tmp));\n  } else {\n    tmp = 64 - n;\n    return new Int_64((x.lowOrder >>> n) | (x.highOrder << tmp), (x.highOrder >>> n) | (x.lowOrder << tmp));\n  }\n}\n\n/**\n * The 64-bit implementation of shift right.\n *\n * This does not work for n >= 32 but is only called for n < 32.\n *\n * @param x The 64-bit integer argument.\n * @param n The number of bits to shift.\n * @returns `x` shifted right by `n` bits\n */\nfunction shr_64(x: Int_64, n: number): Int_64 {\n  return new Int_64(x.highOrder >>> n, (x.lowOrder >>> n) | (x.highOrder << (32 - n)));\n}\n\n/**\n * The 64-bit implementation of the NIST specified Ch function.\n *\n * @param x The first 64-bit integer argument.\n * @param y The second 64-bit integer argument.\n * @param z The third 64-bit integer argument.\n * @returns The NIST specified output of the function.\n */\nexport function ch_64(x: Int_64, y: Int_64, z: Int_64): Int_64 {\n  return new Int_64(\n    (x.highOrder & y.highOrder) ^ (~x.highOrder & z.highOrder),\n    (x.lowOrder & y.lowOrder) ^ (~x.lowOrder & z.lowOrder)\n  );\n}\n\n/**\n * The 64-bit implementation of the NIST specified Maj function.\n *\n * @param x The first 64-bit integer argument.\n * @param y The second 64-bit integer argument.\n * @param z The third 64-bit integer argument.\n * @returns The NIST specified output of the function.\n */\nexport function maj_64(x: Int_64, y: Int_64, z: Int_64): Int_64 {\n  return new Int_64(\n    (x.highOrder & y.highOrder) ^ (x.highOrder & z.highOrder) ^ (y.highOrder & z.highOrder),\n    (x.lowOrder & y.lowOrder) ^ (x.lowOrder & z.lowOrder) ^ (y.lowOrder & z.lowOrder)\n  );\n}\n\n/**\n * The 64-bit implementation of the NIST specified Sigma0 function.\n *\n * @param x The 64-bit integer argument.\n * @returns The NIST specified output of the function.\n */\nexport function sigma0_64(x: Int_64): Int_64 {\n  const rotr28 = rotr_64(x, 28),\n    rotr34 = rotr_64(x, 34),\n    rotr39 = rotr_64(x, 39);\n\n  return new Int_64(\n    rotr28.highOrder ^ rotr34.highOrder ^ rotr39.highOrder,\n    rotr28.lowOrder ^ rotr34.lowOrder ^ rotr39.lowOrder\n  );\n}\n\n/**\n * Add two 64-bit integers.\n *\n * @param x The first 64-bit integer argument to be added.\n * @param y The second 64-bit integer argument to be added.\n * @returns The sum of `x` + `y`.\n */\nexport function safeAdd_64_2(x: Int_64, y: Int_64): Int_64 {\n  let lsw, msw;\n\n  lsw = (x.lowOrder & 0xffff) + (y.lowOrder & 0xffff);\n  msw = (x.lowOrder >>> 16) + (y.lowOrder >>> 16) + (lsw >>> 16);\n  const lowOrder = ((msw & 0xffff) << 16) | (lsw & 0xffff);\n\n  lsw = (x.highOrder & 0xffff) + (y.highOrder & 0xffff) + (msw >>> 16);\n  msw = (x.highOrder >>> 16) + (y.highOrder >>> 16) + (lsw >>> 16);\n  const highOrder = ((msw & 0xffff) << 16) | (lsw & 0xffff);\n\n  return new Int_64(highOrder, lowOrder);\n}\n\n/**\n * Add four 64-bit integers.\n *\n * @param a The first 64-bit integer argument to be added.\n * @param b The second 64-bit integer argument to be added.\n * @param c The third 64-bit integer argument to be added.\n * @param d The fouth 64-bit integer argument to be added.\n * @returns The sum of `a` + `b` + `c` + `d`.\n */\nexport function safeAdd_64_4(a: Int_64, b: Int_64, c: Int_64, d: Int_64): Int_64 {\n  let lsw, msw;\n\n  lsw = (a.lowOrder & 0xffff) + (b.lowOrder & 0xffff) + (c.lowOrder & 0xffff) + (d.lowOrder & 0xffff);\n  msw = (a.lowOrder >>> 16) + (b.lowOrder >>> 16) + (c.lowOrder >>> 16) + (d.lowOrder >>> 16) + (lsw >>> 16);\n  const lowOrder = ((msw & 0xffff) << 16) | (lsw & 0xffff);\n\n  lsw =\n    (a.highOrder & 0xffff) + (b.highOrder & 0xffff) + (c.highOrder & 0xffff) + (d.highOrder & 0xffff) + (msw >>> 16);\n  msw = (a.highOrder >>> 16) + (b.highOrder >>> 16) + (c.highOrder >>> 16) + (d.highOrder >>> 16) + (lsw >>> 16);\n  const highOrder = ((msw & 0xffff) << 16) | (lsw & 0xffff);\n\n  return new Int_64(highOrder, lowOrder);\n}\n\n/**\n * Add five 64-bit integers.\n *\n * @param a The first 64-bit integer argument to be added.\n * @param b The second 64-bit integer argument to be added.\n * @param c The third 64-bit integer argument to be added.\n * @param d The fouth 64-bit integer argument to be added.\n * @param e The fifth 64-bit integer argument to be added.\n * @returns The sum of `a` + `b` + `c` + `d` + `e`.\n */\nexport function safeAdd_64_5(a: Int_64, b: Int_64, c: Int_64, d: Int_64, e: Int_64): Int_64 {\n  let lsw, msw;\n\n  lsw =\n    (a.lowOrder & 0xffff) +\n    (b.lowOrder & 0xffff) +\n    (c.lowOrder & 0xffff) +\n    (d.lowOrder & 0xffff) +\n    (e.lowOrder & 0xffff);\n  msw =\n    (a.lowOrder >>> 16) +\n    (b.lowOrder >>> 16) +\n    (c.lowOrder >>> 16) +\n    (d.lowOrder >>> 16) +\n    (e.lowOrder >>> 16) +\n    (lsw >>> 16);\n  const lowOrder = ((msw & 0xffff) << 16) | (lsw & 0xffff);\n\n  lsw =\n    (a.highOrder & 0xffff) +\n    (b.highOrder & 0xffff) +\n    (c.highOrder & 0xffff) +\n    (d.highOrder & 0xffff) +\n    (e.highOrder & 0xffff) +\n    (msw >>> 16);\n  msw =\n    (a.highOrder >>> 16) +\n    (b.highOrder >>> 16) +\n    (c.highOrder >>> 16) +\n    (d.highOrder >>> 16) +\n    (e.highOrder >>> 16) +\n    (lsw >>> 16);\n  const highOrder = ((msw & 0xffff) << 16) | (lsw & 0xffff);\n\n  return new Int_64(highOrder, lowOrder);\n}\n\n/**\n * XORs two given arguments.\n *\n * @param a The first argument to be XORed.\n * @param b The second argument to be XORed.\n * @returns The The XOR `a` and `b`\n */\nexport function xor_64_2(a: Int_64, b: Int_64): Int_64 {\n  return new Int_64(a.highOrder ^ b.highOrder, a.lowOrder ^ b.lowOrder);\n}\n\n/**\n * XORs five given arguments.\n *\n * @param a The first argument to be XORed.\n * @param b The second argument to be XORed.\n * @param c The third argument to be XORed.\n * @param d The fourth argument to be XORed.\n * @param e The fifth argument to be XORed.\n * @returns The XOR of `a`, `b`, `c`, `d`, and `e`.\n */\nexport function xor_64_5(a: Int_64, b: Int_64, c: Int_64, d: Int_64, e: Int_64): Int_64 {\n  return new Int_64(\n    a.highOrder ^ b.highOrder ^ c.highOrder ^ d.highOrder ^ e.highOrder,\n    a.lowOrder ^ b.lowOrder ^ c.lowOrder ^ d.lowOrder ^ e.lowOrder\n  );\n}\n\n/**\n * The 64-bit implementation of the NIST specified Gamma1 function.\n *\n * @param x The 64-bit integer argument.\n * @returns The NIST specified output of the function.\n */\nexport function gamma1_64(x: Int_64): Int_64 {\n  const rotr19 = rotr_64(x, 19),\n    rotr61 = rotr_64(x, 61),\n    shr6 = shr_64(x, 6);\n\n  return new Int_64(\n    rotr19.highOrder ^ rotr61.highOrder ^ shr6.highOrder,\n    rotr19.lowOrder ^ rotr61.lowOrder ^ shr6.lowOrder\n  );\n}\n\n/**\n * The 64-bit implementation of the NIST specified Gamma0 function.\n *\n * @param x The 64-bit integer argument.\n * @returns The NIST specified output of the function.\n */\nexport function gamma0_64(x: Int_64): Int_64 {\n  const rotr1 = rotr_64(x, 1),\n    rotr8 = rotr_64(x, 8),\n    shr7 = shr_64(x, 7);\n\n  return new Int_64(\n    rotr1.highOrder ^ rotr8.highOrder ^ shr7.highOrder,\n    rotr1.lowOrder ^ rotr8.lowOrder ^ shr7.lowOrder\n  );\n}\n\n/**\n * The 64-bit implementation of the NIST specified Sigma1 function.\n *\n * @param x The 64-bit integer argument.\n * @returns The NIST specified output of the function.\n */\nexport function sigma1_64(x: Int_64): Int_64 {\n  const rotr14 = rotr_64(x, 14),\n    rotr18 = rotr_64(x, 18),\n    rotr41 = rotr_64(x, 41);\n\n  return new Int_64(\n    rotr14.highOrder ^ rotr18.highOrder ^ rotr41.highOrder,\n    rotr14.lowOrder ^ rotr18.lowOrder ^ rotr41.lowOrder\n  );\n}\n", "import { js<PERSON><PERSON><PERSON><PERSON>, TWO_PWR_32, H_trunc, H_full, K_sha2, sha_variant_error, parseInputOption } from \"./common\";\nimport {\n  packedValue,\n  FixedLengthOptionsEncodingType,\n  FixedLengthOptionsNoEncodingType,\n  FormatNoTextType,\n} from \"./custom_types\";\nimport { getStrConverter } from \"./converters\";\nimport {\n  ch_64,\n  gamma0_64,\n  gamma1_64,\n  Int_64,\n  maj_64,\n  safeAdd_64_2,\n  safeAdd_64_4,\n  safeAdd_64_5,\n  sigma0_64,\n  sigma1_64,\n} from \"./primitives_64\";\n\ntype VariantType = \"SHA-384\" | \"SHA-512\";\n\nconst K_sha512 = [\n  new Int_64(K_sha2[0], 0xd728ae22),\n  new Int_64(K_sha2[1], 0x23ef65cd),\n  new Int_64(K_sha2[2], 0xec4d3b2f),\n  new Int_64(K_sha2[3], 0x8189dbbc),\n  new Int_64(K_sha2[4], 0xf348b538),\n  new Int_64(K_sha2[5], 0xb605d019),\n  new Int_64(K_sha2[6], 0xaf194f9b),\n  new Int_64(K_sha2[7], 0xda6d8118),\n  new Int_64(K_sha2[8], 0xa3030242),\n  new Int_64(K_sha2[9], 0x45706fbe),\n  new Int_64(K_sha2[10], 0x4ee4b28c),\n  new Int_64(K_sha2[11], 0xd5ffb4e2),\n  new Int_64(K_sha2[12], 0xf27b896f),\n  new Int_64(K_sha2[13], 0x3b1696b1),\n  new Int_64(K_sha2[14], 0x25c71235),\n  new Int_64(K_sha2[15], 0xcf692694),\n  new Int_64(K_sha2[16], 0x9ef14ad2),\n  new Int_64(K_sha2[17], 0x384f25e3),\n  new Int_64(K_sha2[18], 0x8b8cd5b5),\n  new Int_64(K_sha2[19], 0x77ac9c65),\n  new Int_64(K_sha2[20], 0x592b0275),\n  new Int_64(K_sha2[21], 0x6ea6e483),\n  new Int_64(K_sha2[22], 0xbd41fbd4),\n  new Int_64(K_sha2[23], 0x831153b5),\n  new Int_64(K_sha2[24], 0xee66dfab),\n  new Int_64(K_sha2[25], 0x2db43210),\n  new Int_64(K_sha2[26], 0x98fb213f),\n  new Int_64(K_sha2[27], 0xbeef0ee4),\n  new Int_64(K_sha2[28], 0x3da88fc2),\n  new Int_64(K_sha2[29], 0x930aa725),\n  new Int_64(K_sha2[30], 0xe003826f),\n  new Int_64(K_sha2[31], 0x0a0e6e70),\n  new Int_64(K_sha2[32], 0x46d22ffc),\n  new Int_64(K_sha2[33], 0x5c26c926),\n  new Int_64(K_sha2[34], 0x5ac42aed),\n  new Int_64(K_sha2[35], 0x9d95b3df),\n  new Int_64(K_sha2[36], 0x8baf63de),\n  new Int_64(K_sha2[37], 0x3c77b2a8),\n  new Int_64(K_sha2[38], 0x47edaee6),\n  new Int_64(K_sha2[39], 0x1482353b),\n  new Int_64(K_sha2[40], 0x4cf10364),\n  new Int_64(K_sha2[41], 0xbc423001),\n  new Int_64(K_sha2[42], 0xd0f89791),\n  new Int_64(K_sha2[43], 0x0654be30),\n  new Int_64(K_sha2[44], 0xd6ef5218),\n  new Int_64(K_sha2[45], 0x5565a910),\n  new Int_64(K_sha2[46], 0x5771202a),\n  new Int_64(K_sha2[47], 0x32bbd1b8),\n  new Int_64(K_sha2[48], 0xb8d2d0c8),\n  new Int_64(K_sha2[49], 0x5141ab53),\n  new Int_64(K_sha2[50], 0xdf8eeb99),\n  new Int_64(K_sha2[51], 0xe19b48a8),\n  new Int_64(K_sha2[52], 0xc5c95a63),\n  new Int_64(K_sha2[53], 0xe3418acb),\n  new Int_64(K_sha2[54], 0x7763e373),\n  new Int_64(K_sha2[55], 0xd6b2b8a3),\n  new Int_64(K_sha2[56], 0x5defb2fc),\n  new Int_64(K_sha2[57], 0x43172f60),\n  new Int_64(K_sha2[58], 0xa1f0ab72),\n  new Int_64(K_sha2[59], 0x1a6439ec),\n  new Int_64(K_sha2[60], 0x23631e28),\n  new Int_64(K_sha2[61], 0xde82bde9),\n  new Int_64(K_sha2[62], 0xb2c67915),\n  new Int_64(K_sha2[63], 0xe372532b),\n  new Int_64(0xca273ece, 0xea26619c),\n  new Int_64(0xd186b8c7, 0x21c0c207),\n  new Int_64(0xeada7dd6, 0xcde0eb1e),\n  new Int_64(0xf57d4f7f, 0xee6ed178),\n  new Int_64(0x06f067aa, 0x72176fba),\n  new Int_64(0x0a637dc5, 0xa2c898a6),\n  new Int_64(0x113f9804, 0xbef90dae),\n  new Int_64(0x1b710b35, 0x131c471b),\n  new Int_64(0x28db77f5, 0x23047d84),\n  new Int_64(0x32caab7b, 0x40c72493),\n  new Int_64(0x3c9ebe0a, 0x15c9bebc),\n  new Int_64(0x431d67c4, 0x9c100d4c),\n  new Int_64(0x4cc5d4be, 0xcb3e42b6),\n  new Int_64(0x597f299c, 0xfc657e2a),\n  new Int_64(0x5fcb6fab, 0x3ad6faec),\n  new Int_64(0x6c44198c, 0x4a475817),\n];\n\n/**\n * Gets the state values for the specified SHA variant.\n *\n * @param variant: The SHA-512 family variant.\n * @returns The initial state values.\n */\nfunction getNewState512(variant: VariantType): Int_64[] {\n  if (\"SHA-384\" === variant) {\n    return [\n      new Int_64(0xcbbb9d5d, H_trunc[0]),\n      new Int_64(0x0629a292a, H_trunc[1]),\n      new Int_64(0x9159015a, H_trunc[2]),\n      new Int_64(0x0152fecd8, H_trunc[3]),\n      new Int_64(0x67332667, H_trunc[4]),\n      new Int_64(0x98eb44a87, H_trunc[5]),\n      new Int_64(0xdb0c2e0d, H_trunc[6]),\n      new Int_64(0x047b5481d, H_trunc[7]),\n    ];\n  } else {\n    /* SHA-512 */\n    return [\n      new Int_64(H_full[0], 0xf3bcc908),\n      new Int_64(H_full[1], 0x84caa73b),\n      new Int_64(H_full[2], 0xfe94f82b),\n      new Int_64(H_full[3], 0x5f1d36f1),\n      new Int_64(H_full[4], 0xade682d1),\n      new Int_64(H_full[5], 0x2b3e6c1f),\n      new Int_64(H_full[6], 0xfb41bd6b),\n      new Int_64(H_full[7], 0x137e2179),\n    ];\n  }\n}\n\n/**\n * Performs a round of SHA-512 hashing over a block. This clobbers `H`.\n *\n * @param block The binary array representation of the block to hash.\n * @param H The intermediate H values from a previous round.\n * @returns The resulting H values.\n */\nfunction roundSHA512(block: number[], H: Int_64[]): Int_64[] {\n  let a, b, c, d, e, f, g, h, T1, T2, t, offset;\n\n  const W: Int_64[] = [];\n\n  a = H[0];\n  b = H[1];\n  c = H[2];\n  d = H[3];\n  e = H[4];\n  f = H[5];\n  g = H[6];\n  h = H[7];\n\n  for (t = 0; t < 80; t += 1) {\n    if (t < 16) {\n      offset = t * 2;\n      W[t] = new Int_64(block[offset], block[offset + 1]);\n    } else {\n      W[t] = safeAdd_64_4(gamma1_64(W[t - 2]), W[t - 7], gamma0_64(W[t - 15]), W[t - 16]);\n    }\n    T1 = safeAdd_64_5(h, sigma1_64(e), ch_64(e, f, g), K_sha512[t], W[t]);\n    T2 = safeAdd_64_2(sigma0_64(a), maj_64(a, b, c));\n    h = g;\n    g = f;\n    f = e;\n    e = safeAdd_64_2(d, T1);\n    d = c;\n    c = b;\n    b = a;\n    a = safeAdd_64_2(T1, T2);\n  }\n\n  H[0] = safeAdd_64_2(a, H[0]);\n  H[1] = safeAdd_64_2(b, H[1]);\n  H[2] = safeAdd_64_2(c, H[2]);\n  H[3] = safeAdd_64_2(d, H[3]);\n  H[4] = safeAdd_64_2(e, H[4]);\n  H[5] = safeAdd_64_2(f, H[5]);\n  H[6] = safeAdd_64_2(g, H[6]);\n  H[7] = safeAdd_64_2(h, H[7]);\n\n  return H;\n}\n\n/**\n * Finalizes the SHA-512 hash. This clobbers `remainder` and `H`.\n *\n * @param remainder Any leftover unprocessed packed ints that still need to be processed.\n * @param remainderBinLen The number of bits in `remainder`.\n * @param processedBinLen The number of bits already processed.\n * @param H The intermediate H values from a previous round.\n * @param variant The desired SHA-512 variant.\n * @returns The array of integers representing the SHA-512 hash of message.\n */\nfunction finalizeSHA512(\n  remainder: number[],\n  remainderBinLen: number,\n  processedBinLen: number,\n  H: Int_64[],\n  variant: VariantType\n): number[] {\n  let i, retVal;\n\n  /* The 129 addition is a hack but it works.  The correct number is\n    actually 136 (128 + 8) but the below math fails if\n    remainderBinLen + 136 % 1024 = 0. Since remainderBinLen % 8 = 0,\n    \"shorting\" the addition is OK. */\n  const offset = (((remainderBinLen + 129) >>> 10) << 5) + 31,\n    binaryStringInc = 32,\n    totalLen = remainderBinLen + processedBinLen;\n\n  while (remainder.length <= offset) {\n    remainder.push(0);\n  }\n  /* Append '1' at the end of the binary string */\n  remainder[remainderBinLen >>> 5] |= 0x80 << (24 - (remainderBinLen % 32));\n  /* Append length of binary string in the position such that the new\n   * length is correct. JavaScript numbers are limited to 2^53 so it's\n   * \"safe\" to treat the totalLen as a 64-bit integer. */\n\n  remainder[offset] = totalLen & 0xffffffff;\n  /* Bitwise operators treat the operand as a 32-bit number so need to\n   * use hacky division and round to get access to upper 32-ish bits */\n  remainder[offset - 1] = (totalLen / TWO_PWR_32) | 0;\n\n  /* This will always be at least 1 full chunk */\n  for (i = 0; i < remainder.length; i += binaryStringInc) {\n    H = roundSHA512(remainder.slice(i, i + binaryStringInc), H);\n  }\n\n  if (\"SHA-384\" === variant) {\n    H = (H as unknown) as Int_64[];\n    retVal = [\n      H[0].highOrder,\n      H[0].lowOrder,\n      H[1].highOrder,\n      H[1].lowOrder,\n      H[2].highOrder,\n      H[2].lowOrder,\n      H[3].highOrder,\n      H[3].lowOrder,\n      H[4].highOrder,\n      H[4].lowOrder,\n      H[5].highOrder,\n      H[5].lowOrder,\n    ];\n  } else {\n    /* SHA-512 */\n    retVal = [\n      H[0].highOrder,\n      H[0].lowOrder,\n      H[1].highOrder,\n      H[1].lowOrder,\n      H[2].highOrder,\n      H[2].lowOrder,\n      H[3].highOrder,\n      H[3].lowOrder,\n      H[4].highOrder,\n      H[4].lowOrder,\n      H[5].highOrder,\n      H[5].lowOrder,\n      H[6].highOrder,\n      H[6].lowOrder,\n      H[7].highOrder,\n      H[7].lowOrder,\n    ];\n  }\n  return retVal;\n}\n\nexport default class jsSHA extends jsSHABase<Int_64[], VariantType> {\n  intermediateState: Int_64[];\n  variantBlockSize: number;\n  bigEndianMod: -1 | 1;\n  outputBinLen: number;\n  isVariableLen: boolean;\n  HMACSupported: boolean;\n\n  /* eslint-disable-next-line @typescript-eslint/no-explicit-any */\n  converterFunc: (input: any, existingBin: number[], existingBinLen: number) => packedValue;\n  roundFunc: (block: number[], H: Int_64[]) => Int_64[];\n  finalizeFunc: (remainder: number[], remainderBinLen: number, processedBinLen: number, H: Int_64[]) => number[];\n  stateCloneFunc: (state: Int_64[]) => Int_64[];\n  newStateFunc: (variant: VariantType) => Int_64[];\n  getMAC: () => number[];\n\n  constructor(variant: VariantType, inputFormat: \"TEXT\", options?: FixedLengthOptionsEncodingType);\n  constructor(variant: VariantType, inputFormat: FormatNoTextType, options?: FixedLengthOptionsNoEncodingType);\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  constructor(variant: any, inputFormat: any, options?: any) {\n    if (!(\"SHA-384\" === variant || \"SHA-512\" === variant)) {\n      throw new Error(sha_variant_error);\n    }\n    super(variant, inputFormat, options);\n    const resolvedOptions = options || {};\n\n    // eslint-disable-next-line @typescript-eslint/unbound-method\n    this.getMAC = this._getHMAC;\n    this.HMACSupported = true;\n    this.bigEndianMod = -1;\n    this.converterFunc = getStrConverter(this.inputFormat, this.utfType, this.bigEndianMod);\n    this.roundFunc = roundSHA512;\n    this.stateCloneFunc = function (state): Int_64[] {\n      return state.slice();\n    };\n    this.newStateFunc = getNewState512;\n    this.finalizeFunc = function (remainder, remainderBinLen, processedBinLen, H): number[] {\n      return finalizeSHA512(remainder, remainderBinLen, processedBinLen, H, variant);\n    };\n\n    this.intermediateState = getNewState512(variant);\n    this.variantBlockSize = 1024;\n    this.outputBinLen = \"SHA-384\" === variant ? 384 : 512;\n    this.isVariableLen = false;\n\n    if (resolvedOptions[\"hmacKey\"]) {\n      this._setHMACKey(parseInputOption(\"hmacKey\", resolvedOptions[\"hmacKey\"], this.bigEndianMod));\n    }\n  }\n}\n", "import { jsSHABase, packedLEConcat, sha_variant_error, mac_rounds_error, TWO_PWR_32, parseInputOption } from \"./common\";\nimport {\n  packedValue,\n  CSHAKEOptionsNoEncodingType,\n  CSHAKEOptionsEncodingType,\n  SHAKEOptionsNoEncodingType,\n  SHAKEOptionsEncodingType,\n  KMACOptionsNoEncodingType,\n  KMACOptionsEncodingType,\n  FixedLengthOptionsEncodingType,\n  FixedLengthOptionsNoEncodingType,\n  FormatNoTextType,\n  ResolvedCSHAKEOptionsNoEncodingType,\n  ResolvedKMACOptionsNoEncodingType,\n} from \"./custom_types\";\nimport { getStrConverter } from \"./converters\";\nimport { Int_64, rotl_64, xor_64_2, xor_64_5 } from \"./primitives_64\";\n\ntype FixedLengthVariantType = \"SHA3-224\" | \"SHA3-256\" | \"SHA3-384\" | \"SHA3-512\" | \"SHAKE128\" | \"SHAKE256\";\n\ntype VariantType = FixedLengthVariantType | \"SHAKE128\" | \"SHAKE256\" | \"CSHAKE128\" | \"CSHAKE256\" | \"KMAC128\" | \"KMAC256\";\n\nconst rc_sha3 = [\n  new Int_64(0x00000000, 0x00000001),\n  new Int_64(0x00000000, 0x00008082),\n  new Int_64(0x80000000, 0x0000808a),\n  new Int_64(0x80000000, 0x80008000),\n  new Int_64(0x00000000, 0x0000808b),\n  new Int_64(0x00000000, 0x80000001),\n  new Int_64(0x80000000, 0x80008081),\n  new Int_64(0x80000000, 0x00008009),\n  new Int_64(0x00000000, 0x0000008a),\n  new Int_64(0x00000000, 0x00000088),\n  new Int_64(0x00000000, 0x80008009),\n  new Int_64(0x00000000, 0x8000000a),\n  new Int_64(0x00000000, 0x8000808b),\n  new Int_64(0x80000000, 0x0000008b),\n  new Int_64(0x80000000, 0x00008089),\n  new Int_64(0x80000000, 0x00008003),\n  new Int_64(0x80000000, 0x00008002),\n  new Int_64(0x80000000, 0x00000080),\n  new Int_64(0x00000000, 0x0000800a),\n  new Int_64(0x80000000, 0x8000000a),\n  new Int_64(0x80000000, 0x80008081),\n  new Int_64(0x80000000, 0x00008080),\n  new Int_64(0x00000000, 0x80000001),\n  new Int_64(0x80000000, 0x80008008),\n];\n\nconst r_sha3 = [\n  [0, 36, 3, 41, 18],\n  [1, 44, 10, 45, 2],\n  [62, 6, 43, 15, 61],\n  [28, 55, 25, 21, 56],\n  [27, 20, 39, 8, 14],\n];\n\n/**\n * Gets the state values for the specified SHA-3 variant.\n *\n * @param _variant Unused for this family.\n * @returns The initial state values.\n */\nfunction getNewState(_variant: VariantType): Int_64[][] {\n  let i;\n  const retVal = [];\n\n  for (i = 0; i < 5; i += 1) {\n    retVal[i] = [new Int_64(0, 0), new Int_64(0, 0), new Int_64(0, 0), new Int_64(0, 0), new Int_64(0, 0)];\n  }\n\n  return retVal;\n}\n\n/**\n * Returns a clone of the given SHA3 state.\n *\n * @param state The state to be cloned.\n * @returns The cloned state.\n */\nfunction cloneSHA3State(state: Int_64[][]): Int_64[][] {\n  let i;\n  const clone = [];\n  for (i = 0; i < 5; i += 1) {\n    clone[i] = state[i].slice();\n  }\n\n  return clone;\n}\n\n/**\n * Performs a round of SHA-3 hashing over a block. This clobbers `state`.\n *\n * @param block The binary array representation of the block to hash.\n * @param state Hash state from a previous round.\n * @returns The resulting state value.\n */\nfunction roundSHA3(block: number[] | null, state: Int_64[][]): Int_64[][] {\n  let round, x, y, B;\n  const C = [],\n    D = [];\n\n  if (null !== block) {\n    for (x = 0; x < block.length; x += 2) {\n      state[(x >>> 1) % 5][((x >>> 1) / 5) | 0] = xor_64_2(\n        state[(x >>> 1) % 5][((x >>> 1) / 5) | 0],\n        new Int_64(block[x + 1], block[x])\n      );\n    }\n  }\n\n  for (round = 0; round < 24; round += 1) {\n    /* Any SHA-3 variant name will do here */\n    B = getNewState(\"SHA3-384\");\n\n    /* Perform theta step */\n    for (x = 0; x < 5; x += 1) {\n      C[x] = xor_64_5(state[x][0], state[x][1], state[x][2], state[x][3], state[x][4]);\n    }\n    for (x = 0; x < 5; x += 1) {\n      D[x] = xor_64_2(C[(x + 4) % 5], rotl_64(C[(x + 1) % 5], 1));\n    }\n    for (x = 0; x < 5; x += 1) {\n      for (y = 0; y < 5; y += 1) {\n        state[x][y] = xor_64_2(state[x][y], D[x]);\n      }\n    }\n\n    /* Perform combined ro and pi steps */\n    for (x = 0; x < 5; x += 1) {\n      for (y = 0; y < 5; y += 1) {\n        B[y][(2 * x + 3 * y) % 5] = rotl_64(state[x][y], r_sha3[x][y]);\n      }\n    }\n\n    /* Perform chi step */\n    for (x = 0; x < 5; x += 1) {\n      for (y = 0; y < 5; y += 1) {\n        state[x][y] = xor_64_2(\n          B[x][y],\n          new Int_64(\n            ~B[(x + 1) % 5][y].highOrder & B[(x + 2) % 5][y].highOrder,\n            ~B[(x + 1) % 5][y].lowOrder & B[(x + 2) % 5][y].lowOrder\n          )\n        );\n      }\n    }\n\n    /* Perform iota step */\n    state[0][0] = xor_64_2(state[0][0], rc_sha3[round]);\n  }\n\n  return state;\n}\n\n/**\n * Finalizes the SHA-3 hash. This clobbers `remainder` and `state`.\n *\n * @param remainder Any leftover unprocessed packed ints that still need to be processed.\n * @param remainderBinLen The number of bits in `remainder`.\n * @param _processedBinLen Unused for this family.\n * @param state The state from a previous round.\n * @param blockSize The block size/rate of the variant in bits\n * @param delimiter The delimiter value for the variant\n * @param outputLen The output length for the variant in bits\n * @returns The array of integers representing the SHA-3 hash of message.\n */\nfunction finalizeSHA3(\n  remainder: number[],\n  remainderBinLen: number,\n  _processedBinLen: number,\n  state: Int_64[][],\n  blockSize: number,\n  delimiter: number,\n  outputLen: number\n): number[] {\n  let i,\n    state_offset = 0,\n    temp;\n  const retVal = [],\n    binaryStringInc = blockSize >>> 5,\n    remainderIntLen = remainderBinLen >>> 5;\n\n  /* Process as many blocks as possible, some may be here for multiple rounds\n\t\twith SHAKE\n\t*/\n  for (i = 0; i < remainderIntLen && remainderBinLen >= blockSize; i += binaryStringInc) {\n    state = roundSHA3(remainder.slice(i, i + binaryStringInc), state);\n    remainderBinLen -= blockSize;\n  }\n\n  remainder = remainder.slice(i);\n  remainderBinLen = remainderBinLen % blockSize;\n\n  /* Pad out the remainder to a full block */\n  while (remainder.length < binaryStringInc) {\n    remainder.push(0);\n  }\n\n  /* Find the next \"empty\" byte for the 0x80 and append it via an xor */\n  i = remainderBinLen >>> 3;\n  remainder[i >> 2] ^= delimiter << (8 * (i % 4));\n\n  remainder[binaryStringInc - 1] ^= 0x80000000;\n  state = roundSHA3(remainder, state);\n\n  while (retVal.length * 32 < outputLen) {\n    temp = state[state_offset % 5][(state_offset / 5) | 0];\n    retVal.push(temp.lowOrder);\n    if (retVal.length * 32 >= outputLen) {\n      break;\n    }\n    retVal.push(temp.highOrder);\n    state_offset += 1;\n\n    if (0 === (state_offset * 64) % blockSize) {\n      roundSHA3(null, state);\n      state_offset = 0;\n    }\n  }\n\n  return retVal;\n}\n\n/**\n * Performs NIST left_encode function returned with no extra garbage bits. `x` is limited to <= 9007199254740991.\n *\n * @param x 32-bit number to to encode.\n * @returns The NIST specified output of the function.\n */\nfunction left_encode(x: number): packedValue {\n  let byteOffset,\n    byte,\n    numEncodedBytes = 0;\n  /* JavaScript numbers max out at 0x1FFFFFFFFFFFFF (7 bytes) so this will return a maximum of 7 + 1 = 8 bytes */\n  const retVal = [0, 0],\n    x_64 = [x & 0xffffffff, (x / TWO_PWR_32) & 0x1fffff];\n\n  for (byteOffset = 6; byteOffset >= 0; byteOffset--) {\n    /* This will surprisingly work for large shifts because JavaScript masks the shift amount by 0x1F */\n    byte = (x_64[byteOffset >> 2] >>> (8 * byteOffset)) & 0xff;\n\n    /* Starting from the most significant byte of a 64-bit number, start recording the first non-0 byte and then\n       every byte thereafter */\n    if (byte !== 0 || numEncodedBytes !== 0) {\n      retVal[(numEncodedBytes + 1) >> 2] |= byte << ((numEncodedBytes + 1) * 8);\n      numEncodedBytes += 1;\n    }\n  }\n  numEncodedBytes = numEncodedBytes !== 0 ? numEncodedBytes : 1;\n  retVal[0] |= numEncodedBytes;\n\n  return { value: numEncodedBytes + 1 > 4 ? retVal : [retVal[0]], binLen: 8 + numEncodedBytes * 8 };\n}\n\n/**\n * Performs NIST right_encode function returned with no extra garbage bits. `x` is limited to <= 9007199254740991.\n *\n * @param x 32-bit number to to encode.\n * @returns The NIST specified output of the function.\n */\nfunction right_encode(x: number): packedValue {\n  let byteOffset,\n    byte,\n    numEncodedBytes = 0;\n  /* JavaScript numbers max out at 0x1FFFFFFFFFFFFF (7 bytes) so this will return a maximum of 7 + 1 = 8 bytes */\n  const retVal = [0, 0],\n    x_64 = [x & 0xffffffff, (x / TWO_PWR_32) & 0x1fffff];\n\n  for (byteOffset = 6; byteOffset >= 0; byteOffset--) {\n    /* This will surprisingly work for large shifts because JavaScript masks the shift amount by 0x1F */\n    byte = (x_64[byteOffset >> 2] >>> (8 * byteOffset)) & 0xff;\n\n    /* Starting from the most significant byte of a 64-bit number, start recording the first non-0 byte and then\n       every byte thereafter */\n    if (byte !== 0 || numEncodedBytes !== 0) {\n      retVal[numEncodedBytes >> 2] |= byte << (numEncodedBytes * 8);\n      numEncodedBytes += 1;\n    }\n  }\n  numEncodedBytes = numEncodedBytes !== 0 ? numEncodedBytes : 1;\n  retVal[numEncodedBytes >> 2] |= numEncodedBytes << (numEncodedBytes * 8);\n\n  return { value: numEncodedBytes + 1 > 4 ? retVal : [retVal[0]], binLen: 8 + numEncodedBytes * 8 };\n}\n\n/**\n * Performs NIST encode_string function.\n *\n * @param input Packed array of integers.\n * @returns NIST encode_string output.\n */\nfunction encode_string(input: packedValue): packedValue {\n  return packedLEConcat(left_encode(input[\"binLen\"]), input);\n}\n\n/**\n * Performs NIST byte_pad function.\n *\n * @param packed Packed array of integers.\n * @param outputByteLen Desired length of the output in bytes, assumed to be a multiple of 4.\n * @returns NIST byte_pad output.\n */\nfunction byte_pad(packed: packedValue, outputByteLen: number): number[] {\n  let encodedLen = left_encode(outputByteLen),\n    i;\n\n  encodedLen = packedLEConcat(encodedLen, packed);\n  const outputIntLen = outputByteLen >>> 2,\n    intsToAppend = (outputIntLen - (encodedLen[\"value\"].length % outputIntLen)) % outputIntLen;\n\n  for (i = 0; i < intsToAppend; i++) {\n    encodedLen[\"value\"].push(0);\n  }\n\n  return encodedLen[\"value\"];\n}\n\n/**\n * Parses/validate constructor options for a CSHAKE variant\n *\n * @param options Option given to constructor\n */\nfunction resolveCSHAKEOptions(options: CSHAKEOptionsNoEncodingType): ResolvedCSHAKEOptionsNoEncodingType {\n  const resolvedOptions = options || {};\n\n  return {\n    funcName: parseInputOption(\"funcName\", resolvedOptions[\"funcName\"], 1, { value: [], binLen: 0 }),\n    customization: parseInputOption(\"Customization\", resolvedOptions[\"customization\"], 1, { value: [], binLen: 0 }),\n  };\n}\n\n/**\n * Parses/validate constructor options for a KMAC variant\n *\n * @param options Option given to constructor\n */\nfunction resolveKMACOptions(options: KMACOptionsNoEncodingType): ResolvedKMACOptionsNoEncodingType {\n  const resolvedOptions = options || {};\n\n  return {\n    kmacKey: parseInputOption(\"kmacKey\", resolvedOptions[\"kmacKey\"], 1),\n    /* This is little-endian packed \"KMAC\" */\n    funcName: { value: [0x43414d4b], binLen: 32 },\n    customization: parseInputOption(\"Customization\", resolvedOptions[\"customization\"], 1, { value: [], binLen: 0 }),\n  };\n}\n\nexport default class jsSHA extends jsSHABase<Int_64[][], VariantType> {\n  intermediateState: Int_64[][];\n  variantBlockSize: number;\n  bigEndianMod: -1 | 1;\n  outputBinLen: number;\n  isVariableLen: boolean;\n  HMACSupported: boolean;\n\n  /* eslint-disable-next-line @typescript-eslint/no-explicit-any */\n  converterFunc: (input: any, existingBin: number[], existingBinLen: number) => packedValue;\n  roundFunc: (block: number[], H: Int_64[][]) => Int_64[][];\n  finalizeFunc: (\n    remainder: number[],\n    remainderBinLen: number,\n    processedBinLen: number,\n    H: Int_64[][],\n    outputLen: number\n  ) => number[];\n  stateCloneFunc: (state: Int_64[][]) => Int_64[][];\n  newStateFunc: (variant: VariantType) => Int_64[][];\n  getMAC: ((options: { outputLen: number }) => number[]) | null;\n\n  constructor(variant: FixedLengthVariantType, inputFormat: \"TEXT\", options?: FixedLengthOptionsEncodingType);\n  constructor(\n    variant: FixedLengthVariantType,\n    inputFormat: FormatNoTextType,\n    options?: FixedLengthOptionsNoEncodingType\n  );\n  constructor(variant: \"SHAKE128\" | \"SHAKE256\", inputFormat: \"TEXT\", options?: SHAKEOptionsEncodingType);\n  constructor(variant: \"SHAKE128\" | \"SHAKE256\", inputFormat: FormatNoTextType, options?: SHAKEOptionsNoEncodingType);\n  constructor(variant: \"CSHAKE128\" | \"CSHAKE256\", inputFormat: \"TEXT\", options?: CSHAKEOptionsEncodingType);\n  constructor(variant: \"CSHAKE128\" | \"CSHAKE256\", inputFormat: FormatNoTextType, options?: CSHAKEOptionsNoEncodingType);\n  constructor(variant: \"KMAC128\" | \"KMAC256\", inputFormat: \"TEXT\", options: KMACOptionsEncodingType);\n  constructor(variant: \"KMAC128\" | \"KMAC256\", inputFormat: FormatNoTextType, options: KMACOptionsNoEncodingType);\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  constructor(variant: any, inputFormat: any, options?: any) {\n    let delimiter = 0x06,\n      variantBlockSize = 0;\n    super(variant, inputFormat, options);\n    const resolvedOptions = options || {};\n\n    /* In other variants, this was done after variable initialization but need to do it earlier here becaue we want to\n       avoid KMAC initialization */\n    if (this.numRounds !== 1) {\n      if (resolvedOptions[\"kmacKey\"] || resolvedOptions[\"hmacKey\"]) {\n        throw new Error(mac_rounds_error);\n      } else if (this.shaVariant === \"CSHAKE128\" || this.shaVariant === \"CSHAKE256\") {\n        throw new Error(\"Cannot set numRounds for CSHAKE variants\");\n      }\n    }\n\n    this.bigEndianMod = 1;\n    this.converterFunc = getStrConverter(this.inputFormat, this.utfType, this.bigEndianMod);\n    this.roundFunc = roundSHA3;\n    this.stateCloneFunc = cloneSHA3State;\n    this.newStateFunc = getNewState;\n    this.intermediateState = getNewState(variant);\n\n    this.isVariableLen = false;\n    switch (variant) {\n      case \"SHA3-224\":\n        this.variantBlockSize = variantBlockSize = 1152;\n        this.outputBinLen = 224;\n        this.HMACSupported = true;\n        // eslint-disable-next-line @typescript-eslint/unbound-method\n        this.getMAC = this._getHMAC;\n        break;\n      case \"SHA3-256\":\n        this.variantBlockSize = variantBlockSize = 1088;\n        this.outputBinLen = 256;\n        this.HMACSupported = true;\n        // eslint-disable-next-line @typescript-eslint/unbound-method\n        this.getMAC = this._getHMAC;\n        break;\n      case \"SHA3-384\":\n        this.variantBlockSize = variantBlockSize = 832;\n        this.outputBinLen = 384;\n        this.HMACSupported = true;\n        // eslint-disable-next-line @typescript-eslint/unbound-method\n        this.getMAC = this._getHMAC;\n        break;\n      case \"SHA3-512\":\n        this.variantBlockSize = variantBlockSize = 576;\n        this.outputBinLen = 512;\n        this.HMACSupported = true;\n        // eslint-disable-next-line @typescript-eslint/unbound-method\n        this.getMAC = this._getHMAC;\n        break;\n      case \"SHAKE128\":\n        delimiter = 0x1f;\n        this.variantBlockSize = variantBlockSize = 1344;\n        /* This will be set in getHash */\n        this.outputBinLen = -1;\n        this.isVariableLen = true;\n        this.HMACSupported = false;\n        this.getMAC = null;\n        break;\n      case \"SHAKE256\":\n        delimiter = 0x1f;\n        this.variantBlockSize = variantBlockSize = 1088;\n        /* This will be set in getHash */\n        this.outputBinLen = -1;\n        this.isVariableLen = true;\n        this.HMACSupported = false;\n        this.getMAC = null;\n        break;\n      case \"KMAC128\":\n        delimiter = 0x4;\n        this.variantBlockSize = variantBlockSize = 1344;\n        this._initializeKMAC(options);\n        /* This will be set in getHash */\n        this.outputBinLen = -1;\n        this.isVariableLen = true;\n        this.HMACSupported = false;\n        // eslint-disable-next-line @typescript-eslint/unbound-method\n        this.getMAC = this._getKMAC;\n        break;\n      case \"KMAC256\":\n        delimiter = 0x4;\n        this.variantBlockSize = variantBlockSize = 1088;\n        this._initializeKMAC(options);\n        /* This will be set in getHash */\n        this.outputBinLen = -1;\n        this.isVariableLen = true;\n        this.HMACSupported = false;\n        // eslint-disable-next-line @typescript-eslint/unbound-method\n        this.getMAC = this._getKMAC;\n        break;\n      case \"CSHAKE128\":\n        this.variantBlockSize = variantBlockSize = 1344;\n        delimiter = this._initializeCSHAKE(options);\n        /* This will be set in getHash */\n        this.outputBinLen = -1;\n        this.isVariableLen = true;\n        this.HMACSupported = false;\n        this.getMAC = null;\n        break;\n      case \"CSHAKE256\":\n        this.variantBlockSize = variantBlockSize = 1088;\n        delimiter = this._initializeCSHAKE(options);\n        /* This will be set in getHash */\n        this.outputBinLen = -1;\n        this.isVariableLen = true;\n        this.HMACSupported = false;\n        this.getMAC = null;\n        break;\n      default:\n        throw new Error(sha_variant_error);\n    }\n\n    /* This needs to be down here as CSHAKE can change its delimiter */\n    this.finalizeFunc = function (remainder, remainderBinLen, processedBinLen, state, outputBinLen): number[] {\n      return finalizeSHA3(\n        remainder,\n        remainderBinLen,\n        processedBinLen,\n        state,\n        variantBlockSize,\n        delimiter,\n        outputBinLen\n      );\n    };\n\n    if (resolvedOptions[\"hmacKey\"]) {\n      this._setHMACKey(parseInputOption(\"hmacKey\", resolvedOptions[\"hmacKey\"], this.bigEndianMod));\n    }\n  }\n\n  /**\n   * Initialize CSHAKE variants.\n   *\n   * @param options Options containing CSHAKE params.\n   * @param funcNameOverride Overrides any \"funcName\" present in `options` (used with KMAC)\n   * @returns The delimiter to be used\n   */\n  protected _initializeCSHAKE(options?: CSHAKEOptionsNoEncodingType, funcNameOverride?: packedValue): number {\n    const resolvedOptions = resolveCSHAKEOptions(options || {});\n    if (funcNameOverride) {\n      resolvedOptions[\"funcName\"] = funcNameOverride;\n    }\n    const packedParams = packedLEConcat(\n      encode_string(resolvedOptions[\"funcName\"]),\n      encode_string(resolvedOptions[\"customization\"])\n    );\n\n    /* CSHAKE is defined to be a call to SHAKE iff both the customization and function-name string are both empty.  This\n       can be accomplished by processing nothing in this step. */\n    if (resolvedOptions[\"customization\"][\"binLen\"] !== 0 || resolvedOptions[\"funcName\"][\"binLen\"] !== 0) {\n      const byte_pad_out = byte_pad(packedParams, this.variantBlockSize >>> 3);\n      for (let i = 0; i < byte_pad_out.length; i += this.variantBlockSize >>> 5) {\n        this.intermediateState = this.roundFunc(\n          byte_pad_out.slice(i, i + (this.variantBlockSize >>> 5)),\n          this.intermediateState\n        );\n        this.processedLen += this.variantBlockSize;\n      }\n      return 0x04;\n    } else {\n      return 0x1f;\n    }\n  }\n\n  /**\n   * Initialize KMAC variants.\n   *\n   * @param options Options containing KMAC params.\n   */\n  protected _initializeKMAC(options: KMACOptionsNoEncodingType): void {\n    const resolvedOptions = resolveKMACOptions(options || {});\n\n    this._initializeCSHAKE(options, resolvedOptions[\"funcName\"]);\n    const byte_pad_out = byte_pad(encode_string(resolvedOptions[\"kmacKey\"]), this.variantBlockSize >>> 3);\n    for (let i = 0; i < byte_pad_out.length; i += this.variantBlockSize >>> 5) {\n      this.intermediateState = this.roundFunc(\n        byte_pad_out.slice(i, i + (this.variantBlockSize >>> 5)),\n        this.intermediateState\n      );\n      this.processedLen += this.variantBlockSize;\n    }\n    this.macKeySet = true;\n  }\n\n  /**\n   * Returns the the KMAC in the specified format.\n   *\n   * @param options Hashmap of extra outputs options. `outputLen` must be specified.\n   * @returns The KMAC in the format specified.\n   */\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  protected _getKMAC(options: { outputLen: number }): number[] {\n    const concatedRemainder = packedLEConcat(\n      { value: this.remainder.slice(), binLen: this.remainderLen },\n      right_encode(options[\"outputLen\"])\n    );\n\n    return this.finalizeFunc(\n      concatedRemainder[\"value\"],\n      concatedRemainder[\"binLen\"],\n      this.processedLen,\n      this.stateCloneFunc(this.intermediateState),\n      options[\"outputLen\"]\n    );\n  }\n}\n", "import { sha_variant_error } from \"./common\";\nimport {\n  CSHAKEOptionsEncodingType,\n  CSHAKEOptionsNoEncodingType,\n  SHAKEOptionsEncodingType,\n  SHAKEOptionsNoEncodingType,\n  EncodingType,\n  FixedLengthOptionsEncodingType,\n  FixedLengthOptionsNoEncodingType,\n  FormatNoTextType,\n  KMACOptionsNoEncodingType,\n  KMACOptionsEncodingType,\n} from \"./custom_types\";\nimport jsSHA1 from \"./sha1\";\nimport jsSHA256 from \"./sha256\";\nimport jsSHA512 from \"./sha512\";\nimport jsSHA3 from \"./sha3\";\n\ntype FixedLengthVariantType =\n  | \"SHA-1\"\n  | \"SHA-224\"\n  | \"SHA-256\"\n  | \"SHA-384\"\n  | \"SHA-512\"\n  | \"SHA3-224\"\n  | \"SHA3-256\"\n  | \"SHA3-384\"\n  | \"SHA3-512\";\n\nexport default class jsSHA {\n  private readonly shaObj: jsSHA1 | jsSHA256 | jsSHA512 | jsSHA3;\n  /**\n   * @param variant The desired SHA variant (SHA-1, SHA-224, SHA-256, SHA-384, SHA-512, SHA3-224, SHA3-256, SHA3-256,\n   *   SHA3-384, SHA3-512, SHAKE128, SHAKE256, CSHAKE128, CSHAKE256, KMAC128, or KMAC256) as a string.\n   * @param inputFormat The input format to be used in future `update` calls (TEXT, HEX, B64, BYTES, ARRAYBUFFER,\n   *   or UINT8ARRAY) as a string.\n   * @param options Options in the form of { encoding?: \"UTF8\" | \"UTF16BE\" | \"UTF16LE\"; numRounds?: number }.\n   *   `encoding` is for only TEXT input (defaults to UTF8) and `numRounds` defaults to 1.\n   *   `numRounds` is not valid for any of the MAC or CSHAKE variants.\n   *   * If the variant supports HMAC, `options` may have an additional `hmacKey` key which must be in the form of\n   *     {value: <INPUT>, format: <FORMAT>, encoding?: \"UTF8\" | \"UTF16BE\" | \"UTF16LE\"} where <FORMAT> takes the same\n   *     values as `inputFormat` and <INPUT> can be a `string | ArrayBuffer | Uint8Array` depending on <FORMAT>.\n   *     Supplying this key switches to HMAC calculation and replaces the now deprecated call to `setHMACKey`.\n   *   * If the variant is CSHAKE128 or CSHAKE256, `options` may have two additional keys, `customization` and `funcName`,\n   *     which are the NIST customization and function-name strings.  Both must be in the same form as `hmacKey`.\n   *   * If the variant is KMAC128 or KMAC256, `options` can include the `customization` key from CSHAKE variants and\n   *     *must* have a `kmacKey` key that takes the same form as the `customization` key.\n   */\n  constructor(variant: FixedLengthVariantType, inputFormat: \"TEXT\", options?: FixedLengthOptionsEncodingType);\n  constructor(\n    variant: FixedLengthVariantType,\n    inputFormat: FormatNoTextType,\n    options?: FixedLengthOptionsNoEncodingType\n  );\n  constructor(variant: \"SHAKE128\" | \"SHAKE256\", inputFormat: \"TEXT\", options?: SHAKEOptionsEncodingType);\n  constructor(variant: \"SHAKE128\" | \"SHAKE256\", inputFormat: FormatNoTextType, options?: SHAKEOptionsNoEncodingType);\n  constructor(variant: \"CSHAKE128\" | \"CSHAKE256\", inputFormat: \"TEXT\", options?: CSHAKEOptionsEncodingType);\n  constructor(variant: \"CSHAKE128\" | \"CSHAKE256\", inputFormat: FormatNoTextType, options?: CSHAKEOptionsNoEncodingType);\n  constructor(variant: \"KMAC128\" | \"KMAC256\", inputFormat: \"TEXT\", options: KMACOptionsEncodingType);\n  constructor(variant: \"KMAC128\" | \"KMAC256\", inputFormat: FormatNoTextType, options: KMACOptionsNoEncodingType);\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  constructor(variant: any, inputFormat: any, options?: any) {\n    if (\"SHA-1\" == variant) {\n      this.shaObj = new jsSHA1(variant, inputFormat, options);\n    } else if (\"SHA-224\" == variant || \"SHA-256\" == variant) {\n      this.shaObj = new jsSHA256(variant, inputFormat, options);\n    } else if (\"SHA-384\" == variant || \"SHA-512\" == variant) {\n      this.shaObj = new jsSHA512(variant, inputFormat, options);\n    } else if (\n      \"SHA3-224\" == variant ||\n      \"SHA3-256\" == variant ||\n      \"SHA3-384\" == variant ||\n      \"SHA3-512\" == variant ||\n      \"SHAKE128\" == variant ||\n      \"SHAKE256\" == variant ||\n      \"CSHAKE128\" == variant ||\n      \"CSHAKE256\" == variant ||\n      \"KMAC128\" == variant ||\n      \"KMAC256\" == variant\n    ) {\n      this.shaObj = new jsSHA3(variant, inputFormat, options);\n    } else {\n      throw new Error(sha_variant_error);\n    }\n  }\n\n  /**\n   * Takes `input` and hashes as many blocks as possible. Stores the rest for either a future `update` or `getHash` call.\n   *\n   * @param input The input to be hashed.\n   * @returns A reference to the object.\n   */\n  update(input: string | ArrayBuffer | Uint8Array): this {\n    this.shaObj.update(input);\n\n    return this;\n  }\n\n  /**\n   * Returns the desired SHA or MAC (if a HMAC/KMAC key was specified) hash of the input fed in via `update` calls.\n   *\n   * @param format The desired output formatting (B64, HEX, BYTES, ARRAYBUFFER, or UINT8ARRAY) as a string.\n   * @param options Options in the form of { outputUpper?: boolean; b64Pad?: string; outputLen?: number;  }.\n   *   `outputLen` is required for variable length output variants (this option was previously called `shakeLen` which\n   *    is now deprecated).\n   *   `outputUpper` is only for HEX output (defaults to false) and b64pad is only for B64 output (defaults to \"=\").\n   * @returns The hash in the format specified.\n   */\n  getHash(format: \"HEX\", options?: { outputUpper?: boolean; outputLen?: number; shakeLen?: number }): string;\n  getHash(format: \"B64\", options?: { b64Pad?: string; outputLen?: number; shakeLen?: number }): string;\n  getHash(format: \"BYTES\", options?: { outputLen?: number; shakeLen?: number }): string;\n  getHash(format: \"UINT8ARRAY\", options?: { outputLen?: number; shakeLen?: number }): Uint8Array;\n  getHash(format: \"ARRAYBUFFER\", options?: { outputLen?: number; shakeLen?: number }): ArrayBuffer;\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  getHash(format: any, options?: any): any {\n    return this.shaObj.getHash(format, options);\n  }\n\n  /**\n   * Sets the HMAC key for an eventual `getHMAC` call.  Must be called immediately after jsSHA object instantiation.\n   * Now deprecated in favor of setting the `hmacKey` at object instantiation.\n   *\n   * @param key The key used to calculate the HMAC\n   * @param inputFormat The format of key (HEX, TEXT, B64, BYTES, ARRAYBUFFER, or UINT8ARRAY) as a string.\n   * @param options Options in the form of { encoding?: \"UTF8\" | \"UTF16BE\" | \"UTF16LE }.  `encoding` is only for TEXT\n   *   and defaults to UTF8.\n   */\n  setHMACKey(key: string, inputFormat: \"TEXT\", options?: { encoding?: EncodingType }): void;\n  setHMACKey(key: string, inputFormat: \"B64\" | \"HEX\" | \"BYTES\"): void;\n  setHMACKey(key: ArrayBuffer, inputFormat: \"ARRAYBUFFER\"): void;\n  setHMACKey(key: Uint8Array, inputFormat: \"UINT8ARRAY\"): void;\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  setHMACKey(key: any, inputFormat: any, options?: any): void {\n    this.shaObj.setHMACKey(key, inputFormat, options);\n  }\n\n  /**\n   * Returns the the HMAC in the specified format using the key given by a previous `setHMACKey` call. Now deprecated\n   * in favor of just calling `getHash`.\n   *\n   * @param format The desired output formatting (B64, HEX, BYTES, ARRAYBUFFER, or UINT8ARRAY) as a string.\n   * @param options Options in the form of { outputUpper?: boolean; b64Pad?: string }. `outputUpper` is only for HEX\n   *   output (defaults to false) and `b64pad` is only for B64 output (defaults to \"=\").\n   * @returns The HMAC in the format specified.\n   */\n  getHMAC(format: \"HEX\", options?: { outputUpper?: boolean }): string;\n  getHMAC(format: \"B64\", options?: { b64Pad?: string }): string;\n  getHMAC(format: \"BYTES\"): string;\n  getHMAC(format: \"UINT8ARRAY\"): Uint8Array;\n  getHMAC(format: \"ARRAYBUFFER\"): ArrayBuffer;\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  getHMAC(format: any, options?: any): any {\n    return this.shaObj.getHMAC(format, options);\n  }\n}\n"], "names": ["b64Tab", "arraybuffer_error", "uint8array_error", "uint8array2packed", "arr", "existingPacked", "existingPackedLen", "bigEndianMod", "i", "intOffset", "byteOffset", "packed", "existingByteLen", "shiftModifier", "length", "push", "value", "binLen", "getStrConverter", "format", "utfType", "Error", "str", "existingBin", "existingBinLen", "num", "parseInt", "substr", "isNaN", "hex2packed", "codePnt", "codePntArr", "j", "transposeBytes", "byteCnt", "charCodeAt", "str2packed", "index", "tmpInt", "strPart", "firstEqual", "indexOf", "search", "replace", "char<PERSON>t", "b642packed", "bytes2packed", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ignore", "Uint8Array", "arraybuffer2packed", "getOutputConverter", "outputBinLen", "outputOptions", "binarray", "outputLength", "formatOpts", "hex_tab", "srcByte", "toUpperCase", "packed2hex", "triplet", "int1", "int2", "packed2b64", "String", "fromCharCode", "packed2bytes", "retVal", "a<PERSON><PERSON><PERSON><PERSON>", "packed2arraybuffer", "packed2uint8array", "TWO_PWR_32", "K_sha2", "H_trunc", "H_full", "sha_variant_error", "mac_rounds_error", "packedLEConcat", "a", "b", "arrOffset", "aByteLen", "bByteLen", "leftShiftAmount", "rightShiftAmount", "pop", "concat", "getOutputOpts", "options", "outputUpper", "b64Pad", "outputLen", "lenErrstr", "parseInputOption", "key", "fallback", "errStr", "jsSHABase", "constructor", "variant", "inputFormat", "inputOptions", "this", "numRounds", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "remainder", "remainderLen", "updateCalled", "processedLen", "macKeySet", "keyWithIPad", "keyWithOPad", "update", "srcString", "updateProcessedLen", "variantBlockIntInc", "variantBlockSize", "convertRet", "converterFunc", "chunkBinLen", "chunk", "chunkIntLen", "intermediateState", "roundFunc", "slice", "getHash", "finalizedState", "isVariableLen", "formatFunc", "getMAC", "finalizeFunc", "stateCloneFunc", "newStateFunc", "setHMACKey", "HMACSupported", "keyConverterFunc", "_setHMACKey", "blockByteSize", "lastArrayIndex", "getHMAC", "_getHMAC", "firstHash", "rotl_32", "x", "n", "rotr_32", "shr_32", "parity_32", "y", "z", "ch_32", "maj_32", "sigma0_32", "safeAdd_32_2", "lsw", "safeAdd_32_4", "c", "d", "safeAdd_32_5", "e", "gamma0_32", "sigma1_32", "getNewState", "_variant", "roundSHA1", "block", "H", "T", "t", "W", "finalizeSHA1", "remainderBinLen", "processedBinLen", "offset", "totalLen", "super", "resolvedOptions", "state", "getNewState256", "roundSHA256", "f", "g", "h", "T1", "T2", "finalizeSHA256", "Int_64", "msint_32", "lsint_32", "highOrder", "lowOrder", "rotl_64", "tmp", "rotr_64", "shr_64", "maj_64", "sigma0_64", "rotr28", "rotr34", "rotr39", "safeAdd_64_2", "msw", "safeAdd_64_4", "safeAdd_64_5", "xor_64_2", "gamma1_64", "rotr19", "rotr61", "shr6", "gamma0_64", "rotr1", "rotr8", "shr7", "sigma1_64", "rotr14", "rotr18", "rotr41", "K_sha512", "getNewState512", "roundSHA512", "finalizeSHA512", "rc_sha3", "r_sha3", "cloneSHA3State", "clone", "roundSHA3", "round", "B", "C", "D", "left_encode", "byte", "numEncodedBytes", "x_64", "encode_string", "input", "byte_pad", "outputByteLen", "encodedLen", "outputIntLen", "intsToAppend", "delimiter", "_initializeKMAC", "_getKMAC", "_initializeCSHAKE", "_processedBinLen", "blockSize", "temp", "state_offset", "binaryStringInc", "remainderIntLen", "finalizeSHA3", "funcNameOverride", "funcName", "customization", "resolveCSHAKEOptions", "packedParams", "byte_pad_out", "kmacKey", "resolveKMACOptions", "concated<PERSON><PERSON><PERSON>", "right_encode", "jsSHA", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jsSHA1", "jsSHA256", "jsSHA512", "jsSHA3"], "mappings": ";;;;;;;;AAIA,MAAMA,EAAS,mEAETC,EAAoB,gDACpBC,EAAmB,+CAyPzB,SAASC,EACPC,EACAC,EACAC,EACAC,GAEA,IAAIC,EAAGC,EAAWC,EAGlB,MAAMC,EAASN,GAAkB,CAAC,GAChCO,GAFFN,EAAoBA,GAAqB,KAEC,EACxCO,GAAkC,IAAlBN,EAAsB,EAAI,EAE5C,IAAKC,EAAI,EAAGA,EAAIJ,EAAIU,OAAQN,GAAK,EAC/BE,EAAaF,EAAII,EACjBH,EAAYC,IAAe,EACvBC,EAAOG,QAAUL,GACnBE,EAAOI,KAAK,GAEdJ,EAAOF,IAAcL,EAAII,IAAO,GAAKK,EAAgBN,GAAgBG,EAAa,IAGpF,MAAO,CAAEM,MAAOL,EAAQM,OAAqB,EAAbb,EAAIU,OAAaR,EACnD,UA4BgBY,EACdC,EACAC,EACAb,GAIA,OAAQa,GACN,IAAK,OAEL,IAAK,UAEL,IAAK,UAEH,MACF,QACE,MAAM,IAAIC,MAAM,8CAIpB,OAAQF,GACN,IAAK,MAOH,OAAO,SAAUG,EAAaC,EAAwBC,GACpD,OA9NR,SACEF,EACAjB,EACAC,EACAC,GAEA,IAAIC,EAAGiB,EAAKhB,EAAWC,EAEvB,GAAI,GAAMY,EAAIR,OAAS,EACrB,MAAM,IAAIO,MAAM,iDAIlB,MAAMV,EAASN,GAAkB,CAAC,GAChCO,GAFFN,EAAoBA,GAAqB,KAEC,EACxCO,GAAkC,IAAlBN,EAAsB,EAAI,EAE5C,IAAKC,EAAI,EAAGA,EAAIc,EAAIR,OAAQN,GAAK,EAAG,CAElC,GADAiB,EAAMC,SAASJ,EAAIK,OAAOnB,EAAG,GAAI,IAC5BoB,MAAMH,GAQT,MAAM,IAAIJ,MAAM,kDALhB,IAFAX,GAAcF,IAAM,GAAKI,EACzBH,EAAYC,IAAe,EACpBC,EAAOG,QAAUL,GACtBE,EAAOI,KAAK,GAEdJ,EAAOF,IAAcgB,GAAQ,GAAKZ,EAAgBN,GAAgBG,EAAa,GAIlF,CAED,MAAO,CAAEM,MAAOL,EAAQM,OAAqB,EAAbK,EAAIR,OAAaR,EACnD,CA8LeuB,CAAWP,EAAKC,EAAaC,EAAgBjB,EACtD,EACF,IAAK,OAOH,OAAO,SAAUe,EAAaC,EAAwBC,GACpD,OAnUR,SACEF,EACAF,EACAf,EACAC,EACAC,GAEA,IAAIuB,EACFC,EAEAvB,EACAwB,EACAvB,EACAC,EACAG,EACAoB,EANAC,EAAU,EASZ,MAAMvB,EAASN,GAAkB,CAAC,GAChCO,GAFFN,EAAoBA,GAAqB,KAEC,EAE1C,GAAI,SAAWc,EAEb,IADAP,GAAkC,IAAlBN,EAAsB,EAAI,EACrCC,EAAI,EAAGA,EAAIc,EAAIR,OAAQN,GAAK,EAsB/B,IArBAsB,EAAUR,EAAIa,WAAW3B,GACzBuB,EAAa,GAET,IAAOD,EACTC,EAAWhB,KAAKe,GACP,KAAQA,GACjBC,EAAWhB,KAAK,IAAQe,IAAY,GACpCC,EAAWhB,KAAK,IAAkB,GAAVe,IACf,MAASA,GAAW,OAAUA,EACvCC,EAAWhB,KAAK,IAAQe,IAAY,GAAK,IAASA,IAAY,EAAK,GAAO,IAAkB,GAAVA,IAElFtB,GAAK,EACLsB,EAAU,QAAuB,KAAVA,IAAoB,GAA2B,KAApBR,EAAIa,WAAW3B,IACjEuB,EAAWhB,KACT,IAAQe,IAAY,GACpB,IAASA,IAAY,GAAM,GAC3B,IAASA,IAAY,EAAK,GAC1B,IAAkB,GAAVA,IAIPE,EAAI,EAAGA,EAAID,EAAWjB,OAAQkB,GAAK,EAAG,CAGzC,IAFAtB,EAAawB,EAAUtB,EACvBH,EAAYC,IAAe,EACpBC,EAAOG,QAAUL,GACtBE,EAAOI,KAAK,GAGdJ,EAAOF,IAAcsB,EAAWC,IAAO,GAAKnB,EAAgBN,GAAgBG,EAAa,IACzFwB,GAAW,CACZ,MASH,IALArB,GAAkC,IAAlBN,EAAsB,EAAI,EAI1C0B,EAAkB,YAAcb,GAA4B,IAAjBb,GAAwB,YAAca,GAA4B,IAAjBb,EACvFC,EAAI,EAAGA,EAAIc,EAAIR,OAAQN,GAAK,EAAG,CASlC,IARAsB,EAAUR,EAAIa,WAAW3B,IACF,IAAnByB,IACFD,EAAc,IAAVF,EACJA,EAAWE,GAAK,EAAMF,IAAY,GAGpCpB,EAAawB,EAAUtB,EACvBH,EAAYC,IAAe,EACpBC,EAAOG,QAAUL,GACtBE,EAAOI,KAAK,GAEdJ,EAAOF,IAAcqB,GAAY,GAAKjB,EAAgBN,GAAgBG,EAAa,IACnFwB,GAAW,CACZ,CAEH,MAAO,CAAElB,MAAOL,EAAQM,OAAkB,EAAViB,EAAc5B,EAChD,CAmPe8B,CAAWd,EAAKF,EAASG,EAAaC,EAAgBjB,EAC/D,EACF,IAAK,MAOH,OAAO,SAAUe,EAAaC,EAAwBC,GACpD,OAnKR,SACEF,EACAjB,EACAC,EACAC,GAEA,IACE8B,EACA7B,EACAwB,EACAM,EACAC,EACA9B,EACAC,EAPEwB,EAAU,EAUd,MAAMvB,EAASN,GAAkB,CAAC,GAChCO,GAFFN,EAAoBA,GAAqB,KAEC,EACxCO,GAAkC,IAAlBN,EAAsB,EAAI,EAC1CiC,EAAalB,EAAImB,QAAQ,KAE3B,IAAK,IAAMnB,EAAIoB,OAAO,qBACpB,MAAM,IAAIrB,MAAM,uCAIlB,GADAC,EAAMA,EAAIqB,QAAQ,KAAM,KACnB,IAAMH,GAAcA,EAAalB,EAAIR,OACxC,MAAM,IAAIO,MAAM,uCAGlB,IAAKb,EAAI,EAAGA,EAAIc,EAAIR,OAAQN,GAAK,EAAG,CAIlC,IAHA+B,EAAUjB,EAAIK,OAAOnB,EAAG,GACxB8B,EAAS,EAEJN,EAAI,EAAGA,EAAIO,EAAQzB,OAAQkB,GAAK,EACnCK,EAAQrC,EAAOyC,QAAQF,EAAQK,OAAOZ,IACtCM,GAAUD,GAAU,GAAK,EAAIL,EAG/B,IAAKA,EAAI,EAAGA,EAAIO,EAAQzB,OAAS,EAAGkB,GAAK,EAAG,CAG1C,IAFAtB,EAAawB,EAAUtB,EACvBH,EAAYC,IAAe,EACpBC,EAAOG,QAAUL,GACtBE,EAAOI,KAAK,GAEdJ,EAAOF,KACH6B,IAAY,GAAS,EAAJN,EAAU,MAAU,GAAKnB,EAAgBN,GAAgBG,EAAa,IAC3FwB,GAAW,CACZ,CACF,CAED,MAAO,CAAElB,MAAOL,EAAQM,OAAkB,EAAViB,EAAc5B,EAChD,CA+GeuC,CAAWvB,EAAKC,EAAaC,EAAgBjB,EACtD,EACF,IAAK,QAOH,OAAO,SAAUe,EAAaC,EAAwBC,GACpD,OAjNR,SACEF,EACAjB,EACAC,EACAC,GAEA,IAAIuB,EAAStB,EAAGC,EAAWC,EAG3B,MAAMC,EAASN,GAAkB,CAAC,GAChCO,GAFFN,EAAoBA,GAAqB,KAEC,EACxCO,GAAkC,IAAlBN,EAAsB,EAAI,EAE5C,IAAKC,EAAI,EAAGA,EAAIc,EAAIR,OAAQN,GAAK,EAC/BsB,EAAUR,EAAIa,WAAW3B,GAEzBE,EAAaF,EAAII,EACjBH,EAAYC,IAAe,EACvBC,EAAOG,QAAUL,GACnBE,EAAOI,KAAK,GAEdJ,EAAOF,IAAcqB,GAAY,GAAKjB,EAAgBN,GAAgBG,EAAa,IAGrF,MAAO,CAAEM,MAAOL,EAAQM,OAAqB,EAAbK,EAAIR,OAAaR,EACnD,CAwLewC,CAAaxB,EAAKC,EAAaC,EAAgBjB,EACxD,EACF,IAAK,cACH,IACE,IAAIwC,YAAY,EACjB,CAAC,MAAOC,GACP,MAAM,IAAI3B,MAAMpB,EACjB,CAOD,OAAO,SAAUG,EAAkBmB,EAAwBC,GACzD,OA3FR,SACEpB,EACAC,EACAC,EACAC,GAEA,OAAOJ,EAAkB,IAAI8C,WAAW7C,GAAMC,EAAgBC,EAAmBC,EACnF,CAoFe2C,CAAmB9C,EAAKmB,EAAaC,EAAgBjB,EAC9D,EACF,IAAK,aACH,IACE,IAAI0C,WAAW,EAChB,CAAC,MAAOD,GACP,MAAM,IAAI3B,MAAMnB,EACjB,CAOD,OAAO,SAAUE,EAAiBmB,EAAwBC,GACxD,OAAOrB,EAAkBC,EAAKmB,EAAaC,EAAgBjB,EAC7D,EACF,QACE,MAAM,IAAIc,MAAM,oEAEtB,CAgLM,SAAU8B,EAAmBhC,EAAaiC,EAAmB7C,EAAmB8C,GACpF,OAAQlC,GACN,IAAK,MACH,OAAO,SAAUmC,GACf,OAtKF,SACJ3C,EACA4C,EACAhD,EACAiD,GAEA,MAAMC,EAAU,mBAChB,IACEjD,EACAkD,EAFEpC,EAAM,GAIV,MAAMR,EAASyC,EAAe,EAC5B1C,GAAkC,IAAlBN,EAAsB,EAAI,EAE5C,IAAKC,EAAI,EAAGA,EAAIM,EAAQN,GAAK,EAE3BkD,EAAU/C,EAAOH,IAAM,KAAQ,GAAKK,EAAgBN,GAAgBC,EAAI,IACxEc,GAAOmC,EAAQb,OAAQc,IAAY,EAAK,IAAOD,EAAQb,OAAiB,GAAVc,GAGhE,OAAOF,EAAwB,YAAIlC,EAAIqC,cAAgBrC,CACzD,CAiJesC,CAAWN,EAAUF,EAAc7C,EAAc8C,EAC1D,EACF,IAAK,MACH,OAAO,SAAUC,GACf,OA1IF,SACJ3C,EACA4C,EACAhD,EACAiD,GAEA,IACEhD,EACAwB,EACA6B,EACAC,EACAC,EALEzC,EAAM,GAOV,MAAMR,EAASyC,EAAe,EAC5B1C,GAAkC,IAAlBN,EAAsB,EAAI,EAE5C,IAAKC,EAAI,EAAGA,EAAIM,EAAQN,GAAK,EAO3B,IANAsD,EAAOtD,EAAI,EAAIM,EAASH,EAAQH,EAAI,IAAO,GAAK,EAChDuD,EAAOvD,EAAI,EAAIM,EAASH,EAAQH,EAAI,IAAO,GAAK,EAChDqD,GACKlD,EAAOH,IAAM,KAAQ,GAAKK,EAAgBN,GAAgBC,EAAI,IAAQ,MAAS,IAC/EsD,IAAU,GAAKjD,EAAgBN,IAAiBC,EAAI,GAAK,IAAQ,MAAS,EAC3EuD,IAAU,GAAKlD,EAAgBN,IAAiBC,EAAI,GAAK,IAAQ,IAChEwB,EAAI,EAAGA,EAAI,EAAGA,GAAK,EAEpBV,GADM,EAAJd,EAAY,EAAJwB,GAASuB,EACZvD,EAAO4C,OAAQiB,IAAa,GAAK,EAAI7B,GAAO,IAE5CwB,EAAmB,OAIhC,OAAOlC,CACT,CA0Ge0C,CAAWV,EAAUF,EAAc7C,EAAc8C,EAC1D,EACF,IAAK,QACH,OAAO,SAAUC,GACf,gBApGqB3C,EAAkB4C,EAAsBhD,GACnE,IACEC,EACAkD,EAFEpC,EAAM,GAIV,MAAMR,EAASyC,EAAe,EAC5B1C,GAAkC,IAAlBN,EAAsB,EAAI,EAE5C,IAAKC,EAAI,EAAGA,EAAIM,EAAQN,GAAK,EAC3BkD,EAAW/C,EAAOH,IAAM,KAAQ,GAAKK,EAAgBN,GAAgBC,EAAI,IAAQ,IACjFc,GAAO2C,OAAOC,aAAaR,GAG7B,OAAOpC,CACT,CAsFe6C,CAAab,EAAUF,EAAc7C,EAC9C,EACF,IAAK,cACH,IAEE,IAAIwC,YAAY,EACjB,CAAC,MAAOC,GACP,MAAM,IAAI3B,MAAMpB,EACjB,CACD,OAAO,SAAUqD,GACf,gBAtF2B3C,EAAkB4C,EAAsBhD,GACzE,IAAIC,EACJ,MAAMM,EAASyC,EAAe,EAC5Ba,EAAS,IAAIrB,YAAYjC,GACzBuD,EAAU,IAAIpB,WAAWmB,GACzBvD,GAAkC,IAAlBN,EAAsB,EAAI,EAE5C,IAAKC,EAAI,EAAGA,EAAIM,EAAQN,GAAK,EAC3B6D,EAAQ7D,GAAMG,EAAOH,IAAM,KAAQ,GAAKK,EAAgBN,GAAgBC,EAAI,IAAQ,IAGtF,OAAO4D,CACT,CA0EeE,CAAmBhB,EAAUF,EAAc7C,EACpD,EACF,IAAK,aACH,IAEE,IAAI0C,WAAW,EAChB,CAAC,MAAOD,GACP,MAAM,IAAI3B,MAAMnB,EACjB,CACD,OAAO,SAAUoD,GACf,gBA1E0B3C,EAAkB4C,EAAsBhD,GACxE,IAAIC,EACJ,MAAMM,EAASyC,EAAe,EAC5B1C,GAAkC,IAAlBN,EAAsB,EAAI,EAC1C6D,EAAS,IAAInB,WAAWnC,GAE1B,IAAKN,EAAI,EAAGA,EAAIM,EAAQN,GAAK,EAC3B4D,EAAO5D,GAAMG,EAAOH,IAAM,KAAQ,GAAKK,EAAgBN,GAAgBC,EAAI,IAAQ,IAGrF,OAAO4D,CACT,CA+DeG,CAAkBjB,EAAUF,EAAc7C,EACnD,EACF,QACE,MAAM,IAAIc,MAAM,8DAEtB,CC1lBO,MAAMmD,EAAa,WAGbC,EAAS,CACpB,WAAY,WAAY,WAAY,WAAY,UAAY,WAAY,WAAY,WAAY,WAChG,UAAY,UAAY,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,WAChG,UAAY,UAAY,UAAY,WAAY,WAAY,WAAY,WAAY,WAAY,WAChG,WAAY,WAAY,WAAY,UAAY,UAAY,UAAY,UAAY,WAAY,WAChG,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,WAChG,WAAY,WAAY,UAAY,UAAY,UAAY,UAAY,UAAY,UAAY,WAChG,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,WAChG,YAIWC,EAAU,CAAC,WAAY,UAAY,UAAY,WAAY,WAAY,WAAY,WAAY,YAG/FC,EAAS,CAAC,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,UAAY,YAE9FC,EAAoB,sCACpBC,EAAmB,gCAShB,SAAAC,EAAeC,EAAgBC,GAC7C,IAAIxE,EAAGyE,EACP,MAAMC,EAAWH,EAAU,SAAM,EAC/BI,EAAWH,EAAU,SAAM,EAC3BI,EAAkBF,GAAY,EAC9BG,EAAoB,EAAIH,GAAa,EAGvC,GAAIA,EAAW,GAAM,EAAG,CACtB,IAAK1E,EAAI,EAAGA,EAAI2E,EAAU3E,GAAK,EAC7ByE,EAAaC,EAAW1E,IAAO,EAE/BuE,EAAS,MAAEE,IAAcD,EAAS,MAAExE,IAAM,IAAM4E,EAChDL,EAAS,MAAEhE,KAAK,GAChBgE,EAAS,MAAEE,EAAY,IAAMD,EAAS,MAAExE,IAAM,KAAO6E,EAUvD,OAJKN,EAAS,MAAEjE,QAAU,GAAK,GAAKqE,EAAWD,GAC7CH,EAAS,MAAEO,MAGN,CAAEtE,MAAO+D,EAAS,MAAG9D,OAAQ8D,EAAU,OAAIC,EAAU,OAC7D,CACC,MAAO,CAAEhE,MAAO+D,EAAS,MAAEQ,OAAOP,EAAS,OAAI/D,OAAQ8D,EAAU,OAAIC,EAAU,OAEnF,CASM,SAAUQ,EAAcC,GAM5B,MAAMrB,EAAS,CAAEsB,aAAa,EAAOC,OAAQ,IAAKC,WAAY,GAC5DvC,EAAmGoC,GAAW,CAAA,EAC9GI,EAAY,wCAQd,GANAzB,EAAoB,YAAIf,EAA2B,cAAK,EAEpDA,EAAsB,SACxBe,EAAe,OAAIf,EAAsB,QAGvCA,EAAyB,UAAG,CAC9B,GAAIA,EAAyB,UAAI,GAAM,EACrC,MAAM,IAAIhC,MAAMwE,GAElBzB,EAAkB,UAAIf,EAAyB,SAChD,MAAM,GAAIA,EAAwB,SAAG,CACpC,GAAIA,EAAwB,SAAI,GAAM,EACpC,MAAM,IAAIhC,MAAMwE,GAElBzB,EAAkB,UAAIf,EAAwB,QAC/C,CAED,GAAI,kBAAqBe,EAAoB,YAC3C,MAAM,IAAI/C,MAAM,yCAGlB,GAAI,iBAAoB+C,EAAe,OACrC,MAAM,IAAI/C,MAAM,oCAGlB,OAAO+C,CACT,CAUM,SAAU0B,EACdC,EACA/E,EACAT,EACAyF,GAEA,MAAMC,EAASF,EAAM,mCACrB,IAAK/E,EAAO,CACV,IAAKgF,EACH,MAAM,IAAI3E,MAAM4E,GAElB,OAAOD,CACR,CAED,QAA8B,IAAnBhF,EAAa,QAAsBA,EAAc,OAC1D,MAAM,IAAIK,MAAM4E,GAGlB,OAAO/E,EACLF,EAAc,OAGdA,EAAgB,UAAK,OACrBT,EALKW,CAMLF,EAAa,MACjB,OAEsBkF,EA+CpB,WAAAC,CAAsBC,EAAcC,EAAkBZ,GACpD,MAAMa,EAAeb,GAAW,GAShC,GARAc,KAAKF,EAAcA,EAEnBE,KAAKnF,EAAUkF,EAAuB,UAAK,OAC3CC,KAAKC,UAAYF,EAAwB,WAAK,EAK1C1E,MAAM2E,KAAKC,YAAcD,KAAKC,YAAc9E,SAAS6E,KAAKC,UAAW,KAAO,EAAID,KAAKC,UACvF,MAAM,IAAInF,MAAM,iCAGlBkF,KAAKE,EAAaL,EAClBG,KAAKG,EAAY,GACjBH,KAAKI,EAAe,EACpBJ,KAAKK,GAAe,EACpBL,KAAKM,EAAe,EACpBN,KAAKO,GAAY,EACjBP,KAAKQ,EAAc,GACnBR,KAAKS,EAAc,EACpB,CAQD,MAAAC,CAAOC,GACL,IAAI1G,EACF2G,EAAqB,EACvB,MAAMC,EAAqBb,KAAKc,IAAqB,EACnDC,EAAaf,KAAKgB,EAAcL,EAAWX,KAAKG,EAAWH,KAAKI,GAChEa,EAAcF,EAAmB,OACjCG,EAAQH,EAAkB,MAC1BI,EAAcF,IAAgB,EAEhC,IAAKhH,EAAI,EAAGA,EAAIkH,EAAalH,GAAK4G,EAC5BD,EAAqBZ,KAAKc,GAAoBG,IAChDjB,KAAKoB,EAAoBpB,KAAKqB,EAAUH,EAAMI,MAAMrH,EAAGA,EAAI4G,GAAqBb,KAAKoB,GACrFR,GAAsBZ,KAAKc,GAQ/B,OALAd,KAAKM,GAAgBM,EACrBZ,KAAKG,EAAYe,EAAMI,MAAMV,IAAuB,GACpDZ,KAAKI,EAAea,EAAcjB,KAAKc,EACvCd,KAAKK,GAAe,EAEbL,IACR,CAgBD,OAAAuB,CAAQ3G,EAAasE,GACnB,IAAIjF,EACFuH,EACA3E,EAAemD,KAAKnD,EAEtB,MAAMC,EAAgBmC,EAAcC,GAEpC,GAAIc,KAAKyB,EAAe,CACtB,IAAoC,IAAhC3E,EAAyB,UAC3B,MAAM,IAAIhC,MAAM,8CAElB+B,EAAeC,EAAyB,SACzC,CAED,MAAM4E,EAAa9E,EAAmBhC,EAAQiC,EAAcmD,KAAKhG,EAAc8C,GAC/E,GAAIkD,KAAKO,GAAaP,KAAK2B,EACzB,OAAOD,EAAW1B,KAAK2B,EAAO7E,IAUhC,IAPA0E,EAAiBxB,KAAK4B,EACpB5B,KAAKG,EAAUmB,QACftB,KAAKI,EACLJ,KAAKM,EACLN,KAAK6B,EAAe7B,KAAKoB,GACzBvE,GAEG5C,EAAI,EAAGA,EAAI+F,KAAKC,UAAWhG,GAAK,EAE/B+F,KAAKyB,GAAiB5E,EAAe,IAAO,IAC9C2E,EAAeA,EAAejH,OAAS,IAAM,WAAgB,GAAMsC,EAAe,IAEpF2E,EAAiBxB,KAAK4B,EACpBJ,EACA3E,EACA,EACAmD,KAAK8B,EAAa9B,KAAKE,GACvBrD,GAIJ,OAAO6E,EAAWF,EACnB,CAcD,UAAAO,CAAWvC,EAAUM,EAAkBZ,GACrC,IAAKc,KAAKgC,EACR,MAAM,IAAIlH,MAAM,iCAGlB,GAAIkF,KAAKK,EACP,MAAM,IAAIvF,MAAM,2CAGlB,MACEmH,EAAmBtH,EAAgBmF,GADlBZ,GAAW,IACyC,UAAK,OAAQc,KAAKhG,GAEzFgG,KAAKkC,EAAYD,EAAiBzC,GACnC,CAOS,CAAA0C,CAAY1C,GACpB,MAAM2C,EAAgBnC,KAAKc,IAAqB,EAC9CsB,EAAiBD,EAAgB,EAAI,EACvC,IAAIlI,EACJ,GAAuB,IAAnB+F,KAAKC,UACP,MAAM,IAAInF,MAAMwD,GAGlB,GAAI0B,KAAKO,EACP,MAAM,IAAIzF,MAAM,uBAclB,IATIqH,EAAgB3C,EAAY,OAAI,IAClCA,EAAW,MAAIQ,KAAK4B,EAClBpC,EAAW,MACXA,EAAY,OACZ,EACAQ,KAAK8B,EAAa9B,KAAKE,GACvBF,KAAKnD,IAGF2C,EAAW,MAAEjF,QAAU6H,GAC5B5C,EAAW,MAAEhF,KAAK,GAGpB,IAAKP,EAAI,EAAGA,GAAKmI,EAAgBnI,GAAK,EACpC+F,KAAKQ,EAAYvG,GAAuB,UAAlBuF,EAAW,MAAEvF,GACnC+F,KAAKS,EAAYxG,GAAuB,WAAlBuF,EAAW,MAAEvF,GAGrC+F,KAAKoB,EAAoBpB,KAAKqB,EAAUrB,KAAKQ,EAAaR,KAAKoB,GAC/DpB,KAAKM,EAAeN,KAAKc,EAEzBd,KAAKO,GAAY,CAClB,CAeD,OAAA8B,CAAQzH,EAAasE,GACnB,MAAMpC,EAAgBmC,EAAcC,GAGpC,OAFetC,EAAmBhC,EAAQoF,KAAKnD,EAAcmD,KAAKhG,EAAc8C,EAEzE4E,CAAW1B,KAAKsC,IACxB,CAKS,CAAAA,GACR,IAAId,EAEJ,IAAKxB,KAAKO,EACR,MAAM,IAAIzF,MAAM,qDAGlB,MAAMyH,EAAYvC,KAAK4B,EACrB5B,KAAKG,EAAUmB,QACftB,KAAKI,EACLJ,KAAKM,EACLN,KAAK6B,EAAe7B,KAAKoB,GACzBpB,KAAKnD,GAWP,OATA2E,EAAiBxB,KAAKqB,EAAUrB,KAAKS,EAAaT,KAAK8B,EAAa9B,KAAKE,IACzEsB,EAAiBxB,KAAK4B,EACpBW,EACAvC,KAAKnD,EACLmD,KAAKc,EACLU,EACAxB,KAAKnD,GAGA2E,CACR,EC3Za,SAAAgB,EAAQC,EAAWC,GACjC,OAAQD,GAAKC,EAAMD,IAAO,GAAKC,CACjC,CASA,SAASC,EAAQF,EAAWC,GAC1B,OAAQD,IAAMC,EAAMD,GAAM,GAAKC,CACjC,CASA,SAASE,EAAOH,EAAWC,GACzB,OAAOD,IAAMC,CACf,UAUgBG,EAAUJ,EAAWK,EAAWC,GAC9C,OAAON,EAAIK,EAAIC,CACjB,UAUgBC,EAAMP,EAAWK,EAAWC,GAC1C,OAAQN,EAAIK,GAAOL,EAAIM,CACzB,UAUgBE,EAAOR,EAAWK,EAAWC,GAC3C,OAAQN,EAAIK,EAAML,EAAIM,EAAMD,EAAIC,CAClC,CAQM,SAAUG,EAAUT,GACxB,OAAOE,EAAQF,EAAG,GAAKE,EAAQF,EAAG,IAAME,EAAQF,EAAG,GACrD,CAWgB,SAAAU,EAAa3E,EAAWC,GACtC,MAAM2E,GAAW,MAAJ5E,IAAmB,MAAJC,GAG5B,OAAe,OAFND,IAAM,KAAOC,IAAM,KAAO2E,IAAQ,MAEjB,GAAa,MAANA,CACnC,CAaM,SAAUC,EAAa7E,EAAWC,EAAW6E,EAAWC,GAC5D,MAAMH,GAAW,MAAJ5E,IAAmB,MAAJC,IAAmB,MAAJ6E,IAAmB,MAAJC,GAG1D,OAAe,OAFN/E,IAAM,KAAOC,IAAM,KAAO6E,IAAM,KAAOC,IAAM,KAAOH,IAAQ,MAE3C,GAAa,MAANA,CACnC,CAcM,SAAUI,EAAahF,EAAWC,EAAW6E,EAAWC,EAAWE,GACvE,MAAML,GAAW,MAAJ5E,IAAmB,MAAJC,IAAmB,MAAJ6E,IAAmB,MAAJC,IAAmB,MAAJE,GAGzE,OAAe,OAFNjF,IAAM,KAAOC,IAAM,KAAO6E,IAAM,KAAOC,IAAM,KAAOE,IAAM,KAAOL,IAAQ,MAExD,GAAa,MAANA,CACnC,CAkBM,SAAUM,EAAUjB,GACxB,OAAOE,EAAQF,EAAG,GAAKE,EAAQF,EAAG,IAAMG,EAAOH,EAAG,EACpD,CAQM,SAAUkB,EAAUlB,GACxB,OAAOE,EAAQF,EAAG,GAAKE,EAAQF,EAAG,IAAME,EAAQF,EAAG,GACrD,CCzJA,SAASmB,EAAYC,GACnB,MAAO,CAAC,WAAY,WAAY,WAAY,UAAY,WAC1D,CASA,SAASC,EAAUC,EAAiBC,GAClC,IAAIxF,EAAGC,EAAG6E,EAAGC,EAAGE,EAAGQ,EAAGC,EACtB,MAAMC,EAAc,GAQpB,IANA3F,EAAIwF,EAAE,GACNvF,EAAIuF,EAAE,GACNV,EAAIU,EAAE,GACNT,EAAIS,EAAE,GACNP,EAAIO,EAAE,GAEDE,EAAI,EAAGA,EAAI,GAAIA,GAAK,EAErBC,EAAED,GADAA,EAAI,GACCH,EAAMG,GAEN1B,EAAQ2B,EAAED,EAAI,GAAKC,EAAED,EAAI,GAAKC,EAAED,EAAI,IAAMC,EAAED,EAAI,IAAK,GAI5DD,EADEC,EAAI,GACFV,EAAahB,EAAQhE,EAAG,GAAIwE,EAAMvE,EAAG6E,EAAGC,GAAIE,EAAG,WAAYU,EAAED,IACxDA,EAAI,GACTV,EAAahB,EAAQhE,EAAG,GAAIqE,EAAUpE,EAAG6E,EAAGC,GAAIE,EAAG,WAAYU,EAAED,IAC5DA,EAAI,GACTV,EAAahB,EAAQhE,EAAG,GAAIyE,EAAOxE,EAAG6E,EAAGC,GAAIE,EAAG,WAAYU,EAAED,IAE9DV,EAAahB,EAAQhE,EAAG,GAAIqE,EAAUpE,EAAG6E,EAAGC,GAAIE,EAAG,WAAYU,EAAED,IAGvET,EAAIF,EACJA,EAAID,EACJA,EAAId,EAAQ/D,EAAG,IACfA,EAAID,EACJA,EAAIyF,EASN,OANAD,EAAE,GAAKb,EAAa3E,EAAGwF,EAAE,IACzBA,EAAE,GAAKb,EAAa1E,EAAGuF,EAAE,IACzBA,EAAE,GAAKb,EAAaG,EAAGU,EAAE,IACzBA,EAAE,GAAKb,EAAaI,EAAGS,EAAE,IACzBA,EAAE,GAAKb,EAAaM,EAAGO,EAAE,IAElBA,CACT,CAWA,SAASI,EAAajE,EAAqBkE,EAAyBC,EAAyBN,GAC3F,IAAI/J,EAMJ,MAAMsK,EAAiD,IAArCF,EAAkB,KAAQ,GAAM,GAChDG,EAAWH,EAAkBC,EAC/B,KAAOnE,EAAU5F,QAAUgK,GACzBpE,EAAU3F,KAAK,GAiBjB,IAdA2F,EAAUkE,IAAoB,IAAM,KAAS,GAAMA,EAAkB,GAOrElE,EAAUoE,GAAqB,WAAXC,EAIpBrE,EAAUoE,EAAS,GAAMC,EAAWvG,EAAc,EAG7ChE,EAAI,EAAGA,EAAIkG,EAAU5F,OAAQN,GAAK,GACrC+J,EAAIF,EAAU3D,EAAUmB,MAAMrH,EAAGA,EAAI,IAAK+J,GAG5C,OAAOA,CACT,OAEqB,cAAcrE,EAmBjC,WAAAC,CAAYC,EAAcC,EAAkBZ,GAC1C,GAAI,UAAYW,EACd,MAAM,IAAI/E,MAAMuD,GAElBoG,MAAM5E,EAASC,EAAaZ,GAC5B,MAAMwF,EAAkBxF,GAAW,GAEnCc,KAAKgC,GAAgB,EAErBhC,KAAK2B,EAAS3B,KAAKsC,EACnBtC,KAAKhG,GAAgB,EACrBgG,KAAKgB,EAAgBrG,EAAgBqF,KAAKF,EAAaE,KAAKnF,EAASmF,KAAKhG,GAC1EgG,KAAKqB,EAAYyC,EACjB9D,KAAK6B,EAAiB,SAAU8C,GAC9B,OAAOA,EAAMrD,OACf,EACAtB,KAAK8B,EAAe8B,EACpB5D,KAAK4B,EAAewC,EAEpBpE,KAAKoB,EAtIA,CAAC,WAAY,WAAY,WAAY,UAAY,YAuItDpB,KAAKc,EAAmB,IACxBd,KAAKnD,EAAe,IACpBmD,KAAKyB,GAAgB,EAEjBiD,EAAyB,SAC3B1E,KAAKkC,EAAY3C,EAAiB,UAAWmF,EAAyB,QAAG1E,KAAKhG,GAEjF,GCnIH,SAAS4K,EAAe/E,GACtB,IAAIhC,EAQJ,OALEA,EADE,WAAagC,EACN1B,EAAQmD,QAGRlD,EAAOkD,QAEXzD,CACT,CASA,SAASgH,EAAYd,EAAiBC,GACpC,IAAIxF,EAAGC,EAAG6E,EAAGC,EAAGE,EAAGqB,EAAGC,EAAGC,EAAGC,EAAIC,EAAIhB,EAEpC,MAAMC,EAAc,GAWpB,IATA3F,EAAIwF,EAAE,GACNvF,EAAIuF,EAAE,GACNV,EAAIU,EAAE,GACNT,EAAIS,EAAE,GACNP,EAAIO,EAAE,GACNc,EAAId,EAAE,GACNe,EAAIf,EAAE,GACNgB,EAAIhB,EAAE,GAEDE,EAAI,EAAGA,EAAI,GAAIA,GAAK,EAErBC,EAAED,GADAA,EAAI,GACCH,EAAMG,GAENb,EFmFJV,EADiBF,EElFU0B,EAAED,EAAI,GFmFtB,IAAMvB,EAAQF,EAAG,IAAMG,EAAOH,EAAG,IEnFN0B,EAAED,EAAI,GAAIR,EAAUS,EAAED,EAAI,KAAMC,EAAED,EAAI,KAEjFe,EAAKzB,EAAawB,EAAGrB,EAAUF,GAAIT,EAAMS,EAAGqB,EAAGC,GAAI7G,EAAOgG,GAAIC,EAAED,IAChEgB,EAAK/B,EAAaD,EAAU1E,GAAIyE,EAAOzE,EAAGC,EAAG6E,IAC7C0B,EAAID,EACJA,EAAID,EACJA,EAAIrB,EACJA,EAAIN,EAAaI,EAAG0B,GACpB1B,EAAID,EACJA,EAAI7E,EACJA,EAAID,EACJA,EAAI2E,EAAa8B,EAAIC,GFuEnB,IAAoBzC,EE3DxB,OATAuB,EAAE,GAAKb,EAAa3E,EAAGwF,EAAE,IACzBA,EAAE,GAAKb,EAAa1E,EAAGuF,EAAE,IACzBA,EAAE,GAAKb,EAAaG,EAAGU,EAAE,IACzBA,EAAE,GAAKb,EAAaI,EAAGS,EAAE,IACzBA,EAAE,GAAKb,EAAaM,EAAGO,EAAE,IACzBA,EAAE,GAAKb,EAAa2B,EAAGd,EAAE,IACzBA,EAAE,GAAKb,EAAa4B,EAAGf,EAAE,IACzBA,EAAE,GAAKb,EAAa6B,EAAGhB,EAAE,IAElBA,CACT,OAyDqB,cAAcrE,EAmBjC,WAAAC,CAAYC,EAAcC,EAAkBZ,GAC1C,GAAM,YAAcW,GAAW,YAAcA,EAC3C,MAAM,IAAI/E,MAAMuD,GAElBoG,MAAM5E,EAASC,EAAaZ,GAC5B,MAAMwF,EAAkBxF,GAAW,GAGnCc,KAAK2B,EAAS3B,KAAKsC,EACnBtC,KAAKgC,GAAgB,EACrBhC,KAAKhG,GAAgB,EACrBgG,KAAKgB,EAAgBrG,EAAgBqF,KAAKF,EAAaE,KAAKnF,EAASmF,KAAKhG,GAC1EgG,KAAKqB,EAAYwD,EACjB7E,KAAK6B,EAAiB,SAAU8C,GAC9B,OAAOA,EAAMrD,OACf,EAEAtB,KAAK8B,EAAe8C,EACpB5E,KAAK4B,EAAe,SAAUzB,EAAWkE,EAAiBC,EAAiBN,GACzE,OAnFN,SACE7D,EACAkE,EACAC,EACAN,EACAnE,GAEA,IAAI5F,EAAG4D,EAMP,MAAM0G,EAAiD,IAArCF,EAAkB,KAAQ,GAAM,GAEhDG,EAAWH,EAAkBC,EAE/B,KAAOnE,EAAU5F,QAAUgK,GACzBpE,EAAU3F,KAAK,GAcjB,IAXA2F,EAAUkE,IAAoB,IAAM,KAAS,GAAMA,EAAkB,GAKrElE,EAAUoE,GAAqB,WAAXC,EAGpBrE,EAAUoE,EAAS,GAAMC,EAAWvG,EAAc,EAG7ChE,EAAI,EAAGA,EAAIkG,EAAU5F,OAAQN,GAlBd,GAmBlB+J,EAAIa,EAAY1E,EAAUmB,MAAMrH,EAAGA,EAnBjB,IAmBuC+J,GAU3D,OANEnG,EADE,YAAcgC,EACP,CAACmE,EAAE,GAAIA,EAAE,GAAIA,EAAE,GAAIA,EAAE,GAAIA,EAAE,GAAIA,EAAE,GAAIA,EAAE,IAGvCA,EAGJnG,CACT,CAuCasH,CAAehF,EAAWkE,EAAiBC,EAAiBN,EAAGnE,EACxE,EAEAG,KAAKoB,EAAoBwD,EAAe/E,GACxCG,KAAKc,EAAmB,IACxBd,KAAKnD,EAAe,YAAcgD,EAAU,IAAM,IAClDG,KAAKyB,GAAgB,EAEjBiD,EAAyB,SAC3B1E,KAAKkC,EAAY3C,EAAiB,UAAWmF,EAAyB,QAAG1E,KAAKhG,GAEjF,SCvLUoL,EAOX,WAAAxF,CAAYyF,EAAkBC,GAC5BtF,KAAKuF,EAAYF,EACjBrF,KAAKwF,EAAWF,CACjB,EAYa,SAAAG,EAAQhD,EAAWC,GACjC,IAAIgD,EACJ,OAAIhD,EAAI,IACNgD,EAAM,GAAKhD,EACJ,IAAI0C,EAAQ3C,EAAE+C,GAAY9C,EAAMD,EAAE8C,IAAcG,EAAOjD,EAAE8C,GAAa7C,EAAMD,EAAE+C,IAAaE,IACzF,IAAMhD,GACfgD,EAAM,GAAKhD,EACJ,IAAI0C,EAAQ3C,EAAE8C,GAAa7C,EAAMD,EAAE+C,IAAaE,EAAOjD,EAAE+C,GAAY9C,EAAMD,EAAE8C,IAAcG,IAE3FjD,CAEX,CAWA,SAASkD,EAAQlD,EAAWC,GAC1B,IAAIgD,EACJ,OAAIhD,EAAI,IACNgD,EAAM,GAAKhD,EACJ,IAAI0C,EAAQ3C,EAAE8C,IAAc7C,EAAMD,EAAE+C,GAAYE,EAAOjD,EAAE+C,IAAa9C,EAAMD,EAAE8C,GAAaG,KAElGA,EAAM,GAAKhD,EACJ,IAAI0C,EAAQ3C,EAAE+C,IAAa9C,EAAMD,EAAE8C,GAAaG,EAAOjD,EAAE8C,IAAc7C,EAAMD,EAAE+C,GAAYE,GAEtG,CAWA,SAASE,EAAOnD,EAAWC,GACzB,OAAO,IAAI0C,EAAO3C,EAAE8C,IAAc7C,EAAID,EAAE+C,IAAa9C,EAAMD,EAAE8C,GAAc,GAAK7C,EAClF,UAyBgBmD,EAAOpD,EAAWK,EAAWC,GAC3C,OAAO,IAAIqC,EACR3C,EAAE8C,EAAYzC,EAAEyC,EAAc9C,EAAE8C,EAAYxC,EAAEwC,EAAczC,EAAEyC,EAAYxC,EAAEwC,EAC5E9C,EAAE+C,EAAW1C,EAAE0C,EAAa/C,EAAE+C,EAAWzC,EAAEyC,EAAa1C,EAAE0C,EAAWzC,EAAEyC,EAE5E,CAQM,SAAUM,EAAUrD,GACxB,MAAMsD,EAASJ,EAAQlD,EAAG,IACxBuD,EAASL,EAAQlD,EAAG,IACpBwD,EAASN,EAAQlD,EAAG,IAEtB,OAAO,IAAI2C,EACTW,EAAOR,EAAYS,EAAOT,EAAYU,EAAOV,EAC7CQ,EAAOP,EAAWQ,EAAOR,EAAWS,EAAOT,EAE/C,CASgB,SAAAU,EAAazD,EAAWK,GACtC,IAAIM,EAAK+C,EAET/C,GAAoB,MAAbX,EAAE+C,IAAmC,MAAb1C,EAAE0C,GACjCW,GAAO1D,EAAE+C,IAAa,KAAO1C,EAAE0C,IAAa,KAAOpC,IAAQ,IAC3D,MAAMoC,GAAmB,MAANW,IAAiB,GAAa,MAAN/C,EAE3CA,GAAqB,MAAdX,EAAE8C,IAAqC,MAAdzC,EAAEyC,IAAuBY,IAAQ,IACjEA,GAAO1D,EAAE8C,IAAc,KAAOzC,EAAEyC,IAAc,KAAOnC,IAAQ,IAG7D,OAAO,IAAIgC,GAFe,MAANe,IAAiB,GAAa,MAAN/C,EAEfoC,EAC/B,CAWM,SAAUY,EAAa5H,EAAWC,EAAW6E,EAAWC,GAC5D,IAAIH,EAAK+C,EAET/C,GAAoB,MAAb5E,EAAEgH,IAAmC,MAAb/G,EAAE+G,IAAmC,MAAblC,EAAEkC,IAAmC,MAAbjC,EAAEiC,GACjFW,GAAO3H,EAAEgH,IAAa,KAAO/G,EAAE+G,IAAa,KAAOlC,EAAEkC,IAAa,KAAOjC,EAAEiC,IAAa,KAAOpC,IAAQ,IACvG,MAAMoC,GAAmB,MAANW,IAAiB,GAAa,MAAN/C,EAE3CA,GACiB,MAAd5E,EAAE+G,IAAqC,MAAd9G,EAAE8G,IAAqC,MAAdjC,EAAEiC,IAAqC,MAAdhC,EAAEgC,IAAuBY,IAAQ,IAC/GA,GAAO3H,EAAE+G,IAAc,KAAO9G,EAAE8G,IAAc,KAAOjC,EAAEiC,IAAc,KAAOhC,EAAEgC,IAAc,KAAOnC,IAAQ,IAG3G,OAAO,IAAIgC,GAFe,MAANe,IAAiB,GAAa,MAAN/C,EAEfoC,EAC/B,CAYM,SAAUa,EAAa7H,EAAWC,EAAW6E,EAAWC,EAAWE,GACvE,IAAIL,EAAK+C,EAET/C,GACgB,MAAb5E,EAAEgH,IACW,MAAb/G,EAAE+G,IACW,MAAblC,EAAEkC,IACW,MAAbjC,EAAEiC,IACW,MAAb/B,EAAE+B,GACLW,GACG3H,EAAEgH,IAAa,KACf/G,EAAE+G,IAAa,KACflC,EAAEkC,IAAa,KACfjC,EAAEiC,IAAa,KACf/B,EAAE+B,IAAa,KACfpC,IAAQ,IACX,MAAMoC,GAAmB,MAANW,IAAiB,GAAa,MAAN/C,EAE3CA,GACiB,MAAd5E,EAAE+G,IACY,MAAd9G,EAAE8G,IACY,MAAdjC,EAAEiC,IACY,MAAdhC,EAAEgC,IACY,MAAd9B,EAAE8B,IACFY,IAAQ,IACXA,GACG3H,EAAE+G,IAAc,KAChB9G,EAAE8G,IAAc,KAChBjC,EAAEiC,IAAc,KAChBhC,EAAEgC,IAAc,KAChB9B,EAAE8B,IAAc,KAChBnC,IAAQ,IAGX,OAAO,IAAIgC,GAFe,MAANe,IAAiB,GAAa,MAAN/C,EAEfoC,EAC/B,CASgB,SAAAc,EAAS9H,EAAWC,GAClC,OAAO,IAAI2G,EAAO5G,EAAE+G,EAAY9G,EAAE8G,EAAW/G,EAAEgH,EAAW/G,EAAE+G,EAC9D,CAyBM,SAAUe,EAAU9D,GACxB,MAAM+D,EAASb,EAAQlD,EAAG,IACxBgE,EAASd,EAAQlD,EAAG,IACpBiE,EAAOd,EAAOnD,EAAG,GAEnB,OAAO,IAAI2C,EACToB,EAAOjB,EAAYkB,EAAOlB,EAAYmB,EAAKnB,EAC3CiB,EAAOhB,EAAWiB,EAAOjB,EAAWkB,EAAKlB,EAE7C,CAQM,SAAUmB,EAAUlE,GACxB,MAAMmE,EAAQjB,EAAQlD,EAAG,GACvBoE,EAAQlB,EAAQlD,EAAG,GACnBqE,EAAOlB,EAAOnD,EAAG,GAEnB,OAAO,IAAI2C,EACTwB,EAAMrB,EAAYsB,EAAMtB,EAAYuB,EAAKvB,EACzCqB,EAAMpB,EAAWqB,EAAMrB,EAAWsB,EAAKtB,EAE3C,CAQM,SAAUuB,EAAUtE,GACxB,MAAMuE,EAASrB,EAAQlD,EAAG,IACxBwE,EAAStB,EAAQlD,EAAG,IACpByE,EAASvB,EAAQlD,EAAG,IAEtB,OAAO,IAAI2C,EACT4B,EAAOzB,EAAY0B,EAAO1B,EAAY2B,EAAO3B,EAC7CyB,EAAOxB,EAAWyB,EAAOzB,EAAW0B,EAAO1B,EAE/C,CCjRA,MAAM2B,EAAW,CACf,IAAI/B,EAAOlH,EAAO,GAAI,YACtB,IAAIkH,EAAOlH,EAAO,GAAI,WACtB,IAAIkH,EAAOlH,EAAO,GAAI,YACtB,IAAIkH,EAAOlH,EAAO,GAAI,YACtB,IAAIkH,EAAOlH,EAAO,GAAI,YACtB,IAAIkH,EAAOlH,EAAO,GAAI,YACtB,IAAIkH,EAAOlH,EAAO,GAAI,YACtB,IAAIkH,EAAOlH,EAAO,GAAI,YACtB,IAAIkH,EAAOlH,EAAO,GAAI,YACtB,IAAIkH,EAAOlH,EAAO,GAAI,YACtB,IAAIkH,EAAOlH,EAAO,IAAK,YACvB,IAAIkH,EAAOlH,EAAO,IAAK,YACvB,IAAIkH,EAAOlH,EAAO,IAAK,YACvB,IAAIkH,EAAOlH,EAAO,IAAK,WACvB,IAAIkH,EAAOlH,EAAO,IAAK,WACvB,IAAIkH,EAAOlH,EAAO,IAAK,YACvB,IAAIkH,EAAOlH,EAAO,IAAK,YACvB,IAAIkH,EAAOlH,EAAO,IAAK,WACvB,IAAIkH,EAAOlH,EAAO,IAAK,YACvB,IAAIkH,EAAOlH,EAAO,IAAK,YACvB,IAAIkH,EAAOlH,EAAO,IAAK,YACvB,IAAIkH,EAAOlH,EAAO,IAAK,YACvB,IAAIkH,EAAOlH,EAAO,IAAK,YACvB,IAAIkH,EAAOlH,EAAO,IAAK,YACvB,IAAIkH,EAAOlH,EAAO,IAAK,YACvB,IAAIkH,EAAOlH,EAAO,IAAK,WACvB,IAAIkH,EAAOlH,EAAO,IAAK,YACvB,IAAIkH,EAAOlH,EAAO,IAAK,YACvB,IAAIkH,EAAOlH,EAAO,IAAK,YACvB,IAAIkH,EAAOlH,EAAO,IAAK,YACvB,IAAIkH,EAAOlH,EAAO,IAAK,YACvB,IAAIkH,EAAOlH,EAAO,IAAK,WACvB,IAAIkH,EAAOlH,EAAO,IAAK,YACvB,IAAIkH,EAAOlH,EAAO,IAAK,YACvB,IAAIkH,EAAOlH,EAAO,IAAK,YACvB,IAAIkH,EAAOlH,EAAO,IAAK,YACvB,IAAIkH,EAAOlH,EAAO,IAAK,YACvB,IAAIkH,EAAOlH,EAAO,IAAK,YACvB,IAAIkH,EAAOlH,EAAO,IAAK,YACvB,IAAIkH,EAAOlH,EAAO,IAAK,WACvB,IAAIkH,EAAOlH,EAAO,IAAK,YACvB,IAAIkH,EAAOlH,EAAO,IAAK,YACvB,IAAIkH,EAAOlH,EAAO,IAAK,YACvB,IAAIkH,EAAOlH,EAAO,IAAK,WACvB,IAAIkH,EAAOlH,EAAO,IAAK,YACvB,IAAIkH,EAAOlH,EAAO,IAAK,YACvB,IAAIkH,EAAOlH,EAAO,IAAK,YACvB,IAAIkH,EAAOlH,EAAO,IAAK,WACvB,IAAIkH,EAAOlH,EAAO,IAAK,YACvB,IAAIkH,EAAOlH,EAAO,IAAK,YACvB,IAAIkH,EAAOlH,EAAO,IAAK,YACvB,IAAIkH,EAAOlH,EAAO,IAAK,YACvB,IAAIkH,EAAOlH,EAAO,IAAK,YACvB,IAAIkH,EAAOlH,EAAO,IAAK,YACvB,IAAIkH,EAAOlH,EAAO,IAAK,YACvB,IAAIkH,EAAOlH,EAAO,IAAK,YACvB,IAAIkH,EAAOlH,EAAO,IAAK,YACvB,IAAIkH,EAAOlH,EAAO,IAAK,YACvB,IAAIkH,EAAOlH,EAAO,IAAK,YACvB,IAAIkH,EAAOlH,EAAO,IAAK,WACvB,IAAIkH,EAAOlH,EAAO,IAAK,WACvB,IAAIkH,EAAOlH,EAAO,IAAK,YACvB,IAAIkH,EAAOlH,EAAO,IAAK,YACvB,IAAIkH,EAAOlH,EAAO,IAAK,YACvB,IAAIkH,EAAO,WAAY,YACvB,IAAIA,EAAO,WAAY,WACvB,IAAIA,EAAO,WAAY,YACvB,IAAIA,EAAO,WAAY,YACvB,IAAIA,EAAO,UAAY,YACvB,IAAIA,EAAO,UAAY,YACvB,IAAIA,EAAO,UAAY,YACvB,IAAIA,EAAO,UAAY,WACvB,IAAIA,EAAO,UAAY,WACvB,IAAIA,EAAO,UAAY,YACvB,IAAIA,EAAO,WAAY,WACvB,IAAIA,EAAO,WAAY,YACvB,IAAIA,EAAO,WAAY,YACvB,IAAIA,EAAO,WAAY,YACvB,IAAIA,EAAO,WAAY,WACvB,IAAIA,EAAO,WAAY,aASzB,SAASgC,EAAevH,GACtB,MAAI,YAAcA,EACT,CACL,IAAIuF,EAAO,WAAYjH,EAAQ,IAC/B,IAAIiH,EAAO,WAAajH,EAAQ,IAChC,IAAIiH,EAAO,WAAYjH,EAAQ,IAC/B,IAAIiH,EAAO,UAAajH,EAAQ,IAChC,IAAIiH,EAAO,WAAYjH,EAAQ,IAC/B,IAAIiH,EAAO,YAAajH,EAAQ,IAChC,IAAIiH,EAAO,WAAYjH,EAAQ,IAC/B,IAAIiH,EAAO,WAAajH,EAAQ,KAI3B,CACL,IAAIiH,EAAOhH,EAAO,GAAI,YACtB,IAAIgH,EAAOhH,EAAO,GAAI,YACtB,IAAIgH,EAAOhH,EAAO,GAAI,YACtB,IAAIgH,EAAOhH,EAAO,GAAI,YACtB,IAAIgH,EAAOhH,EAAO,GAAI,YACtB,IAAIgH,EAAOhH,EAAO,GAAI,WACtB,IAAIgH,EAAOhH,EAAO,GAAI,YACtB,IAAIgH,EAAOhH,EAAO,GAAI,WAG5B,CASA,SAASiJ,EAAYtD,EAAiBC,GACpC,IAAIxF,EAAGC,EAAG6E,EAAGC,EAAGE,EAAGqB,EAAGC,EAAGC,EAAGC,EAAIC,EAAIhB,EAAGK,EAEvC,MAAMJ,EAAc,GAWpB,IATA3F,EAAIwF,EAAE,GACNvF,EAAIuF,EAAE,GACNV,EAAIU,EAAE,GACNT,EAAIS,EAAE,GACNP,EAAIO,EAAE,GACNc,EAAId,EAAE,GACNe,EAAIf,EAAE,GACNgB,EAAIhB,EAAE,GAEDE,EAAI,EAAGA,EAAI,GAAIA,GAAK,EACnBA,EAAI,IACNK,EAAa,EAAJL,EACTC,EAAED,GAAK,IAAIkB,EAAOrB,EAAMQ,GAASR,EAAMQ,EAAS,KAEhDJ,EAAED,GAAKkC,EAAaG,EAAUpC,EAAED,EAAI,IAAKC,EAAED,EAAI,GAAIyC,EAAUxC,EAAED,EAAI,KAAMC,EAAED,EAAI,KAEjFe,EAAKoB,EAAarB,EAAG+B,EAAUtD,ID/EFX,EC+EegC,ED/EJ/B,EC+EOgC,ED9E1C,IAAIK,GADS3C,EC+EuBgB,GD7EtC8B,EAAYzC,EAAEyC,GAAe9C,EAAE8C,EAAYxC,EAAEwC,EAC/C9C,EAAE+C,EAAW1C,EAAE0C,GAAc/C,EAAE+C,EAAWzC,EAAEyC,IC4EM2B,EAASjD,GAAIC,EAAED,IAClEgB,EAAKgB,EAAaJ,EAAUtH,GAAIqH,EAAOrH,EAAGC,EAAG6E,IAC7C0B,EAAID,EACJA,EAAID,EACJA,EAAIrB,EACJA,EAAIyC,EAAa3C,EAAG0B,GACpB1B,EAAID,EACJA,EAAI7E,EACJA,EAAID,EACJA,EAAI0H,EAAajB,EAAIC,ODxFHzC,EAAWK,EAAWC,ECoG1C,OATAiB,EAAE,GAAKkC,EAAa1H,EAAGwF,EAAE,IACzBA,EAAE,GAAKkC,EAAazH,EAAGuF,EAAE,IACzBA,EAAE,GAAKkC,EAAa5C,EAAGU,EAAE,IACzBA,EAAE,GAAKkC,EAAa3C,EAAGS,EAAE,IACzBA,EAAE,GAAKkC,EAAazC,EAAGO,EAAE,IACzBA,EAAE,GAAKkC,EAAapB,EAAGd,EAAE,IACzBA,EAAE,GAAKkC,EAAanB,EAAGf,EAAE,IACzBA,EAAE,GAAKkC,EAAalB,EAAGhB,EAAE,IAElBA,CACT,OAwFqB,cAAcrE,EAmBjC,WAAAC,CAAYC,EAAcC,EAAkBZ,GAC1C,GAAM,YAAcW,GAAW,YAAcA,EAC3C,MAAM,IAAI/E,MAAMuD,GAElBoG,MAAM5E,EAASC,EAAaZ,GAC5B,MAAMwF,EAAkBxF,GAAW,GAGnCc,KAAK2B,EAAS3B,KAAKsC,EACnBtC,KAAKgC,GAAgB,EACrBhC,KAAKhG,GAAgB,EACrBgG,KAAKgB,EAAgBrG,EAAgBqF,KAAKF,EAAaE,KAAKnF,EAASmF,KAAKhG,GAC1EgG,KAAKqB,EAAYgG,EACjBrH,KAAK6B,EAAiB,SAAU8C,GAC9B,OAAOA,EAAMrD,OACf,EACAtB,KAAK8B,EAAesF,EACpBpH,KAAK4B,EAAe,SAAUzB,EAAWkE,EAAiBC,EAAiBN,GACzE,OAjHN,SACE7D,EACAkE,EACAC,EACAN,EACAnE,GAEA,IAAI5F,EAAG4D,EAMP,MAAM0G,EAAmD,IAAvCF,EAAkB,MAAS,IAAO,GAElDG,EAAWH,EAAkBC,EAE/B,KAAOnE,EAAU5F,QAAUgK,GACzBpE,EAAU3F,KAAK,GAcjB,IAXA2F,EAAUkE,IAAoB,IAAM,KAAS,GAAMA,EAAkB,GAKrElE,EAAUoE,GAAqB,WAAXC,EAGpBrE,EAAUoE,EAAS,GAAMC,EAAWvG,EAAc,EAG7ChE,EAAI,EAAGA,EAAIkG,EAAU5F,OAAQN,GAlBd,GAmBlB+J,EAAIqD,EAAYlH,EAAUmB,MAAMrH,EAAGA,EAnBjB,IAmBuC+J,GAwC3D,OAnCEnG,EAFE,YAAcgC,EAEP,CACPmE,EAAE,GAAGuB,EACLvB,EAAE,GAAGwB,EACLxB,EAAE,GAAGuB,EACLvB,EAAE,GAAGwB,EACLxB,EAAE,GAAGuB,EACLvB,EAAE,GAAGwB,EACLxB,EAAE,GAAGuB,EACLvB,EAAE,GAAGwB,EACLxB,EAAE,GAAGuB,EACLvB,EAAE,GAAGwB,EACLxB,EAAE,GAAGuB,EACLvB,EAAE,GAAGwB,GAIE,CACPxB,EAAE,GAAGuB,EACLvB,EAAE,GAAGwB,EACLxB,EAAE,GAAGuB,EACLvB,EAAE,GAAGwB,EACLxB,EAAE,GAAGuB,EACLvB,EAAE,GAAGwB,EACLxB,EAAE,GAAGuB,EACLvB,EAAE,GAAGwB,EACLxB,EAAE,GAAGuB,EACLvB,EAAE,GAAGwB,EACLxB,EAAE,GAAGuB,EACLvB,EAAE,GAAGwB,EACLxB,EAAE,GAAGuB,EACLvB,EAAE,GAAGwB,EACLxB,EAAE,GAAGuB,EACLvB,EAAE,GAAGwB,GAGF3H,CACT,CAuCayJ,CAAenH,EAAWkE,EAAiBC,EAAiBN,EAAGnE,EACxE,EAEAG,KAAKoB,EAAoBgG,EAAevH,GACxCG,KAAKc,EAAmB,KACxBd,KAAKnD,EAAe,YAAcgD,EAAU,IAAM,IAClDG,KAAKyB,GAAgB,EAEjBiD,EAAyB,SAC3B1E,KAAKkC,EAAY3C,EAAiB,UAAWmF,EAAyB,QAAG1E,KAAKhG,GAEjF,GC/SH,MAAMuN,EAAU,CACd,IAAInC,EAAO,EAAY,GACvB,IAAIA,EAAO,EAAY,OACvB,IAAIA,EAAO,WAAY,OACvB,IAAIA,EAAO,WAAY,YACvB,IAAIA,EAAO,EAAY,OACvB,IAAIA,EAAO,EAAY,YACvB,IAAIA,EAAO,WAAY,YACvB,IAAIA,EAAO,WAAY,OACvB,IAAIA,EAAO,EAAY,KACvB,IAAIA,EAAO,EAAY,KACvB,IAAIA,EAAO,EAAY,YACvB,IAAIA,EAAO,EAAY,YACvB,IAAIA,EAAO,EAAY,YACvB,IAAIA,EAAO,WAAY,KACvB,IAAIA,EAAO,WAAY,OACvB,IAAIA,EAAO,WAAY,OACvB,IAAIA,EAAO,WAAY,OACvB,IAAIA,EAAO,WAAY,KACvB,IAAIA,EAAO,EAAY,OACvB,IAAIA,EAAO,WAAY,YACvB,IAAIA,EAAO,WAAY,YACvB,IAAIA,EAAO,WAAY,OACvB,IAAIA,EAAO,EAAY,YACvB,IAAIA,EAAO,WAAY,aAGnBoC,EAAS,CACb,CAAC,EAAG,GAAI,EAAG,GAAI,IACf,CAAC,EAAG,GAAI,GAAI,GAAI,GAChB,CAAC,GAAI,EAAG,GAAI,GAAI,IAChB,CAAC,GAAI,GAAI,GAAI,GAAI,IACjB,CAAC,GAAI,GAAI,GAAI,EAAG,KASlB,SAAS5D,GAAYC,GACnB,IAAI5J,EACJ,MAAM4D,EAAS,GAEf,IAAK5D,EAAI,EAAGA,EAAI,EAAGA,GAAK,EACtB4D,EAAO5D,GAAK,CAAC,IAAImL,EAAO,EAAG,GAAI,IAAIA,EAAO,EAAG,GAAI,IAAIA,EAAO,EAAG,GAAI,IAAIA,EAAO,EAAG,GAAI,IAAIA,EAAO,EAAG,IAGrG,OAAOvH,CACT,CAQA,SAAS4J,GAAe9C,GACtB,IAAI1K,EACJ,MAAMyN,EAAQ,GACd,IAAKzN,EAAI,EAAGA,EAAI,EAAGA,GAAK,EACtByN,EAAMzN,GAAK0K,EAAM1K,GAAGqH,QAGtB,OAAOoG,CACT,CASA,SAASC,GAAU5D,EAAwBY,GACzC,IAAIiD,EAAOnF,EAAGK,EAAG+E,EACjB,MAAMC,EAAI,GACRC,EAAI,GAEN,GAAI,OAAShE,EACX,IAAKtB,EAAI,EAAGA,EAAIsB,EAAMxJ,OAAQkI,GAAK,EACjCkC,GAAOlC,IAAM,GAAK,IAAKA,IAAM,GAAK,EAAK,GAAK6D,EAC1C3B,GAAOlC,IAAM,GAAK,IAAKA,IAAM,GAAK,EAAK,GACvC,IAAI2C,EAAOrB,EAAMtB,EAAI,GAAIsB,EAAMtB,KAKrC,IAAKmF,EAAQ,EAAGA,EAAQ,GAAIA,GAAS,EAAG,CAKtC,IAHAC,EAAIjE,KAGCnB,EAAI,EAAGA,EAAI,EAAGA,GAAK,EACtBqF,EAAErF,IF2HiBjE,EE3HHmG,EAAMlC,GAAG,GF2HKhE,EE3HDkG,EAAMlC,GAAG,GF2HGa,EE3HCqB,EAAMlC,GAAG,GF2HCc,EE3HGoB,EAAMlC,GAAG,GF2HDgB,EE3HKkB,EAAMlC,GAAG,GF4H1E,IAAI2C,EACT5G,EAAE+G,EAAY9G,EAAE8G,EAAYjC,EAAEiC,EAAYhC,EAAEgC,EAAY9B,EAAE8B,EAC1D/G,EAAEgH,EAAW/G,EAAE+G,EAAWlC,EAAEkC,EAAWjC,EAAEiC,EAAW/B,EAAE+B,IE5HtD,IAAK/C,EAAI,EAAGA,EAAI,EAAGA,GAAK,EACtBsF,EAAEtF,GAAK6D,EAASwB,GAAGrF,EAAI,GAAK,GAAIgD,EAAQqC,GAAGrF,EAAI,GAAK,GAAI,IAE1D,IAAKA,EAAI,EAAGA,EAAI,EAAGA,GAAK,EACtB,IAAKK,EAAI,EAAGA,EAAI,EAAGA,GAAK,EACtB6B,EAAMlC,GAAGK,GAAKwD,EAAS3B,EAAMlC,GAAGK,GAAIiF,EAAEtF,IAK1C,IAAKA,EAAI,EAAGA,EAAI,EAAGA,GAAK,EACtB,IAAKK,EAAI,EAAGA,EAAI,EAAGA,GAAK,EACtB+E,EAAE/E,IAAI,EAAIL,EAAI,EAAIK,GAAK,GAAK2C,EAAQd,EAAMlC,GAAGK,GAAI0E,EAAO/E,GAAGK,IAK/D,IAAKL,EAAI,EAAGA,EAAI,EAAGA,GAAK,EACtB,IAAKK,EAAI,EAAGA,EAAI,EAAGA,GAAK,EACtB6B,EAAMlC,GAAGK,GAAKwD,EACZuB,EAAEpF,GAAGK,GACL,IAAIsC,GACDyC,GAAGpF,EAAI,GAAK,GAAGK,GAAGyC,EAAYsC,GAAGpF,EAAI,GAAK,GAAGK,GAAGyC,GAChDsC,GAAGpF,EAAI,GAAK,GAAGK,GAAG0C,EAAWqC,GAAGpF,EAAI,GAAK,GAAGK,GAAG0C,IAOxDb,EAAM,GAAG,GAAK2B,EAAS3B,EAAM,GAAG,GAAI4C,EAAQK,GAC7C,CF0FG,IAAmBpJ,EAAWC,EAAW6E,EAAWC,EAAWE,EExFnE,OAAOkB,CACT,CA6EA,SAASqD,GAAYvF,GACnB,IAAItI,EACF8N,EACAC,EAAkB,EAEpB,MAAMrK,EAAS,CAAC,EAAG,GACjBsK,EAAO,CAAK,WAAJ1F,EAAiBA,EAAIxE,EAAc,SAE7C,IAAK9D,EAAa,EAAGA,GAAc,EAAGA,IAEpC8N,EAAQE,EAAKhO,GAAc,KAAQ,EAAIA,EAAe,IAIzC,IAAT8N,GAAkC,IAApBC,IAChBrK,EAAQqK,EAAkB,GAAM,IAAMD,GAAiC,GAAvBC,EAAkB,GAClEA,GAAmB,GAMvB,OAHAA,EAAsC,IAApBA,EAAwBA,EAAkB,EAC5DrK,EAAO,IAAMqK,EAEN,CAAEzN,MAAOyN,EAAkB,EAAI,EAAIrK,EAAS,CAACA,EAAO,IAAKnD,OAAQ,EAAsB,EAAlBwN,EAC9E,CAuCA,SAASE,GAAcC,GACrB,OAAO9J,EAAeyJ,GAAYK,EAAc,QAAIA,EACtD,CASA,SAASC,GAASlO,EAAqBmO,GACrC,IACEtO,EADEuO,EAAaR,GAAYO,GAG7BC,EAAajK,EAAeiK,EAAYpO,GACxC,MAAMqO,EAAeF,IAAkB,EACrCG,GAAgBD,EAAgBD,EAAkB,MAAEjO,OAASkO,GAAiBA,EAEhF,IAAKxO,EAAI,EAAGA,EAAIyO,EAAczO,IAC5BuO,EAAkB,MAAEhO,KAAK,GAG3B,OAAOgO,EAAkB,KAC3B,QAgCqB,cAAc7I,EAmCjC,WAAAC,CAAYC,EAAcC,EAAkBZ,GAC1C,IAAIyJ,EAAY,EACd7H,EAAmB,EACrB2D,MAAM5E,EAASC,EAAaZ,GAC5B,MAAMwF,EAAkBxF,GAAW,GAInC,GAAuB,IAAnBc,KAAKC,UAAiB,CACxB,GAAIyE,EAAyB,SAAKA,EAAyB,QACzD,MAAM,IAAI5J,MAAMwD,GACX,GAAwB,cAApB0B,KAAKE,GAAkD,cAApBF,KAAKE,EACjD,MAAM,IAAIpF,MAAM,2CAEnB,CAUD,OARAkF,KAAKhG,EAAe,EACpBgG,KAAKgB,EAAgBrG,EAAgBqF,KAAKF,EAAaE,KAAKnF,EAASmF,KAAKhG,GAC1EgG,KAAKqB,EAAYsG,GACjB3H,KAAK6B,EAAiB4F,GACtBzH,KAAK8B,EAAe8B,GACpB5D,KAAKoB,EAAoBwC,KAEzB5D,KAAKyB,GAAgB,EACb5B,GACN,IAAK,WACHG,KAAKc,EAAmBA,EAAmB,KAC3Cd,KAAKnD,EAAe,IACpBmD,KAAKgC,GAAgB,EAErBhC,KAAK2B,EAAS3B,KAAKsC,EACnB,MACF,IAAK,WACHtC,KAAKc,EAAmBA,EAAmB,KAC3Cd,KAAKnD,EAAe,IACpBmD,KAAKgC,GAAgB,EAErBhC,KAAK2B,EAAS3B,KAAKsC,EACnB,MACF,IAAK,WACHtC,KAAKc,EAAmBA,EAAmB,IAC3Cd,KAAKnD,EAAe,IACpBmD,KAAKgC,GAAgB,EAErBhC,KAAK2B,EAAS3B,KAAKsC,EACnB,MACF,IAAK,WACHtC,KAAKc,EAAmBA,EAAmB,IAC3Cd,KAAKnD,EAAe,IACpBmD,KAAKgC,GAAgB,EAErBhC,KAAK2B,EAAS3B,KAAKsC,EACnB,MACF,IAAK,WACHqG,EAAY,GACZ3I,KAAKc,EAAmBA,EAAmB,KAE3Cd,KAAKnD,GAAgB,EACrBmD,KAAKyB,GAAgB,EACrBzB,KAAKgC,GAAgB,EACrBhC,KAAK2B,EAAS,KACd,MACF,IAAK,WACHgH,EAAY,GACZ3I,KAAKc,EAAmBA,EAAmB,KAE3Cd,KAAKnD,GAAgB,EACrBmD,KAAKyB,GAAgB,EACrBzB,KAAKgC,GAAgB,EACrBhC,KAAK2B,EAAS,KACd,MACF,IAAK,UACHgH,EAAY,EACZ3I,KAAKc,EAAmBA,EAAmB,KAC3Cd,KAAK4I,EAAgB1J,GAErBc,KAAKnD,GAAgB,EACrBmD,KAAKyB,GAAgB,EACrBzB,KAAKgC,GAAgB,EAErBhC,KAAK2B,EAAS3B,KAAK6I,EACnB,MACF,IAAK,UACHF,EAAY,EACZ3I,KAAKc,EAAmBA,EAAmB,KAC3Cd,KAAK4I,EAAgB1J,GAErBc,KAAKnD,GAAgB,EACrBmD,KAAKyB,GAAgB,EACrBzB,KAAKgC,GAAgB,EAErBhC,KAAK2B,EAAS3B,KAAK6I,EACnB,MACF,IAAK,YACH7I,KAAKc,EAAmBA,EAAmB,KAC3C6H,EAAY3I,KAAK8I,EAAkB5J,GAEnCc,KAAKnD,GAAgB,EACrBmD,KAAKyB,GAAgB,EACrBzB,KAAKgC,GAAgB,EACrBhC,KAAK2B,EAAS,KACd,MACF,IAAK,YACH3B,KAAKc,EAAmBA,EAAmB,KAC3C6H,EAAY3I,KAAK8I,EAAkB5J,GAEnCc,KAAKnD,GAAgB,EACrBmD,KAAKyB,GAAgB,EACrBzB,KAAKgC,GAAgB,EACrBhC,KAAK2B,EAAS,KACd,MACF,QACE,MAAM,IAAI7G,MAAMuD,GAIpB2B,KAAK4B,EAAe,SAAUzB,EAAWkE,EAAiBC,EAAiBK,EAAO9H,GAChF,OA7UN,SACEsD,EACAkE,EACA0E,EACApE,EACAqE,EACAL,EACAtJ,GAEA,IAAIpF,EAEFgP,EADAC,EAAe,EAEjB,MAAMrL,EAAS,GACbsL,EAAkBH,IAAc,EAChCI,EAAkB/E,IAAoB,EAKxC,IAAKpK,EAAI,EAAGA,EAAImP,GAAmB/E,GAAmB2E,EAAW/O,GAAKkP,EACpExE,EAAQgD,GAAUxH,EAAUmB,MAAMrH,EAAGA,EAAIkP,GAAkBxE,GAC3DN,GAAmB2E,EAOrB,IAJA7I,EAAYA,EAAUmB,MAAMrH,GAC5BoK,GAAoC2E,EAG7B7I,EAAU5F,OAAS4O,GACxBhJ,EAAU3F,KAAK,GAUjB,IANAP,EAAIoK,IAAoB,EACxBlE,EAAUlG,GAAK,IAAM0O,GAAmB1O,EAAI,EAAT,EAEnCkG,EAAUgJ,EAAkB,IAAM,WAClCxE,EAAQgD,GAAUxH,EAAWwE,GAEN,GAAhB9G,EAAOtD,OAAc8E,IAC1B4J,EAAOtE,EAAMuE,EAAe,GAAIA,EAAe,EAAK,GACpDrL,EAAOrD,KAAKyO,EAAKzD,KACG,GAAhB3H,EAAOtD,QAAe8E,KAG1BxB,EAAOrD,KAAKyO,EAAK1D,GACjB2D,GAAgB,EAEZ,GAAsB,GAAfA,EAAqBF,IAC9BrB,GAAU,KAAMhD,GAChBuE,EAAe,GAInB,OAAOrL,CACT,CAsRawL,CACLlJ,EACAkE,EACAC,EACAK,EACA7D,EACA6H,EACA9L,EAEJ,EAEI6H,EAAyB,SAC3B1E,KAAKkC,EAAY3C,EAAiB,UAAWmF,EAAyB,QAAG1E,KAAKhG,GAEjF,CASS,CAAA8O,CAAkB5J,EAAuCoK,GACjE,MAAM5E,EAzMV,SAA8BxF,GAC5B,MAAMwF,EAAkBxF,GAAW,GAEnC,MAAO,CACLqK,SAAUhK,EAAiB,WAAYmF,EAA0B,SAAG,EAAG,CAAEjK,MAAO,GAAIC,OAAQ,IAC5F8O,cAAejK,EAAiB,gBAAiBmF,EAA+B,cAAG,EAAG,CAAEjK,MAAO,GAAIC,OAAQ,IAE/G,CAkM4B+O,CAAqBvK,GAAW,CAAE,GACtDoK,IACF5E,EAA0B,SAAI4E,GAEhC,MAAMI,EAAenL,EACnB6J,GAAc1D,EAA0B,UACxC0D,GAAc1D,EAA+B,gBAK/C,GAAmD,IAA/CA,EAA+B,cAAU,QAAqD,IAA1CA,EAA0B,SAAU,OAAS,CACnG,MAAMiF,EAAerB,GAASoB,EAAc1J,KAAKc,IAAqB,GACtE,IAAK,IAAI7G,EAAI,EAAGA,EAAI0P,EAAapP,OAAQN,GAAK+F,KAAKc,IAAqB,EACtEd,KAAKoB,EAAoBpB,KAAKqB,EAC5BsI,EAAarI,MAAMrH,EAAGA,GAAK+F,KAAKc,IAAqB,IACrDd,KAAKoB,GAEPpB,KAAKM,GAAgBN,KAAKc,EAE5B,OAAO,CACR,CACC,OAAO,EAEV,CAOS,CAAA8H,CAAgB1J,GACxB,MAAMwF,EA3NV,SAA4BxF,GAC1B,MAAMwF,EAAkBxF,GAAW,GAEnC,MAAO,CACL0K,QAASrK,EAAiB,UAAWmF,EAAyB,QAAG,GAEjE6E,SAAU,CAAE9O,MAAO,CAAC,YAAaC,OAAQ,IACzC8O,cAAejK,EAAiB,gBAAiBmF,EAA+B,cAAG,EAAG,CAAEjK,MAAO,GAAIC,OAAQ,IAE/G,CAkN4BmP,CAAmB3K,GAAW,CAAE,GAExDc,KAAK8I,EAAkB5J,EAASwF,EAA0B,UAC1D,MAAMiF,EAAerB,GAASF,GAAc1D,EAAyB,SAAI1E,KAAKc,IAAqB,GACnG,IAAK,IAAI7G,EAAI,EAAGA,EAAI0P,EAAapP,OAAQN,GAAK+F,KAAKc,IAAqB,EACtEd,KAAKoB,EAAoBpB,KAAKqB,EAC5BsI,EAAarI,MAAMrH,EAAGA,GAAK+F,KAAKc,IAAqB,IACrDd,KAAKoB,GAEPpB,KAAKM,GAAgBN,KAAKc,EAE5Bd,KAAKO,GAAY,CAClB,CASS,CAAAsI,CAAS3J,GACjB,MAAM4K,EAAoBvL,EACxB,CAAE9D,MAAOuF,KAAKG,EAAUmB,QAAS5G,OAAQsF,KAAKI,GA9TpD,SAAsBqC,GACpB,IAAItI,EACF8N,EACAC,EAAkB,EAEpB,MAAMrK,EAAS,CAAC,EAAG,GACjBsK,EAAO,CAAK,WAAJ1F,EAAiBA,EAAIxE,EAAc,SAE7C,IAAK9D,EAAa,EAAGA,GAAc,EAAGA,IAEpC8N,EAAQE,EAAKhO,GAAc,KAAQ,EAAIA,EAAe,IAIzC,IAAT8N,GAAkC,IAApBC,IAChBrK,EAAOqK,GAAmB,IAAMD,GAA2B,EAAlBC,EACzCA,GAAmB,GAMvB,OAHAA,EAAsC,IAApBA,EAAwBA,EAAkB,EAC5DrK,EAAOqK,GAAmB,IAAMA,GAAsC,EAAlBA,EAE7C,CAAEzN,MAAOyN,EAAkB,EAAI,EAAIrK,EAAS,CAACA,EAAO,IAAKnD,OAAQ,EAAsB,EAAlBwN,EAC9E,CAwSM6B,CAAa7K,EAAmB,YAGlC,OAAOc,KAAK4B,EACVkI,EAAyB,MACzBA,EAA0B,OAC1B9J,KAAKM,EACLN,KAAK6B,EAAe7B,KAAKoB,GACzBlC,EAAmB,UAEtB,GCjjBW,MAAO8K,GAgCnB,WAAApK,CAAYC,EAAcC,EAAkBZ,GAC1C,GAAI,SAAWW,EACbG,KAAKiK,EAAS,IAAIC,EAAOrK,EAASC,EAAaZ,QAC1C,GAAI,WAAaW,GAAW,WAAaA,EAC9CG,KAAKiK,EAAS,IAAIE,EAAStK,EAASC,EAAaZ,QAC5C,GAAI,WAAaW,GAAW,WAAaA,EAC9CG,KAAKiK,EAAS,IAAIG,EAASvK,EAASC,EAAaZ,OAC5C,IACL,YAAcW,GACd,YAAcA,GACd,YAAcA,GACd,YAAcA,GACd,YAAcA,GACd,YAAcA,GACd,aAAeA,GACf,aAAeA,GACf,WAAaA,GACb,WAAaA,EAIb,MAAM,IAAI/E,MAAMuD,GAFhB2B,KAAKiK,EAAS,IAAII,GAAOxK,EAASC,EAAaZ,EAGhD,CACF,CAQD,MAAAwB,CAAO2H,GAGL,OAFArI,KAAKiK,EAAOvJ,OAAO2H,GAEZrI,IACR,CAkBD,OAAAuB,CAAQ3G,EAAasE,GACnB,OAAOc,KAAKiK,EAAO1I,QAAQ3G,EAAQsE,EACpC,CAgBD,UAAA6C,CAAWvC,EAAUM,EAAkBZ,GACrCc,KAAKiK,EAAOlI,WAAWvC,EAAKM,EAAaZ,EAC1C,CAiBD,OAAAmD,CAAQzH,EAAasE,GACnB,OAAOc,KAAKiK,EAAO5H,QAAQzH,EAAQsE,EACpC"}