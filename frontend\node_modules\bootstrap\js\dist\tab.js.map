{"version": 3, "file": "tab.js", "sources": ["../src/tab.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap tab.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport BaseComponent from './base-component.js'\nimport EventHandler from './dom/event-handler.js'\nimport SelectorEngine from './dom/selector-engine.js'\nimport { defineJQueryPlugin, getNextActiveElement, isDisabled } from './util/index.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'tab'\nconst DATA_KEY = 'bs.tab'\nconst EVENT_KEY = `.${DATA_KEY}`\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}`\nconst EVENT_KEYDOWN = `keydown${EVENT_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}`\n\nconst ARROW_LEFT_KEY = 'ArrowLeft'\nconst ARROW_RIGHT_KEY = 'ArrowRight'\nconst ARROW_UP_KEY = 'ArrowUp'\nconst ARROW_DOWN_KEY = 'ArrowDown'\nconst HOME_KEY = 'Home'\nconst END_KEY = 'End'\n\nconst CLASS_NAME_ACTIVE = 'active'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_DROPDOWN = 'dropdown'\n\nconst SELECTOR_DROPDOWN_TOGGLE = '.dropdown-toggle'\nconst SELECTOR_DROPDOWN_MENU = '.dropdown-menu'\nconst NOT_SELECTOR_DROPDOWN_TOGGLE = `:not(${SELECTOR_DROPDOWN_TOGGLE})`\n\nconst SELECTOR_TAB_PANEL = '.list-group, .nav, [role=\"tablist\"]'\nconst SELECTOR_OUTER = '.nav-item, .list-group-item'\nconst SELECTOR_INNER = `.nav-link${NOT_SELECTOR_DROPDOWN_TOGGLE}, .list-group-item${NOT_SELECTOR_DROPDOWN_TOGGLE}, [role=\"tab\"]${NOT_SELECTOR_DROPDOWN_TOGGLE}`\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"tab\"], [data-bs-toggle=\"pill\"], [data-bs-toggle=\"list\"]' // TODO: could only be `tab` in v6\nconst SELECTOR_INNER_ELEM = `${SELECTOR_INNER}, ${SELECTOR_DATA_TOGGLE}`\n\nconst SELECTOR_DATA_TOGGLE_ACTIVE = `.${CLASS_NAME_ACTIVE}[data-bs-toggle=\"tab\"], .${CLASS_NAME_ACTIVE}[data-bs-toggle=\"pill\"], .${CLASS_NAME_ACTIVE}[data-bs-toggle=\"list\"]`\n\n/**\n * Class definition\n */\n\nclass Tab extends BaseComponent {\n  constructor(element) {\n    super(element)\n    this._parent = this._element.closest(SELECTOR_TAB_PANEL)\n\n    if (!this._parent) {\n      return\n      // TODO: should throw exception in v6\n      // throw new TypeError(`${element.outerHTML} has not a valid parent ${SELECTOR_INNER_ELEM}`)\n    }\n\n    // Set up initial aria attributes\n    this._setInitialAttributes(this._parent, this._getChildren())\n\n    EventHandler.on(this._element, EVENT_KEYDOWN, event => this._keydown(event))\n  }\n\n  // Getters\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  show() { // Shows this elem and deactivate the active sibling if exists\n    const innerElem = this._element\n    if (this._elemIsActive(innerElem)) {\n      return\n    }\n\n    // Search for active tab on same parent to deactivate it\n    const active = this._getActiveElem()\n\n    const hideEvent = active ?\n      EventHandler.trigger(active, EVENT_HIDE, { relatedTarget: innerElem }) :\n      null\n\n    const showEvent = EventHandler.trigger(innerElem, EVENT_SHOW, { relatedTarget: active })\n\n    if (showEvent.defaultPrevented || (hideEvent && hideEvent.defaultPrevented)) {\n      return\n    }\n\n    this._deactivate(active, innerElem)\n    this._activate(innerElem, active)\n  }\n\n  // Private\n  _activate(element, relatedElem) {\n    if (!element) {\n      return\n    }\n\n    element.classList.add(CLASS_NAME_ACTIVE)\n\n    this._activate(SelectorEngine.getElementFromSelector(element)) // Search and activate/show the proper section\n\n    const complete = () => {\n      if (element.getAttribute('role') !== 'tab') {\n        element.classList.add(CLASS_NAME_SHOW)\n        return\n      }\n\n      element.removeAttribute('tabindex')\n      element.setAttribute('aria-selected', true)\n      this._toggleDropDown(element, true)\n      EventHandler.trigger(element, EVENT_SHOWN, {\n        relatedTarget: relatedElem\n      })\n    }\n\n    this._queueCallback(complete, element, element.classList.contains(CLASS_NAME_FADE))\n  }\n\n  _deactivate(element, relatedElem) {\n    if (!element) {\n      return\n    }\n\n    element.classList.remove(CLASS_NAME_ACTIVE)\n    element.blur()\n\n    this._deactivate(SelectorEngine.getElementFromSelector(element)) // Search and deactivate the shown section too\n\n    const complete = () => {\n      if (element.getAttribute('role') !== 'tab') {\n        element.classList.remove(CLASS_NAME_SHOW)\n        return\n      }\n\n      element.setAttribute('aria-selected', false)\n      element.setAttribute('tabindex', '-1')\n      this._toggleDropDown(element, false)\n      EventHandler.trigger(element, EVENT_HIDDEN, { relatedTarget: relatedElem })\n    }\n\n    this._queueCallback(complete, element, element.classList.contains(CLASS_NAME_FADE))\n  }\n\n  _keydown(event) {\n    if (!([ARROW_LEFT_KEY, ARROW_RIGHT_KEY, ARROW_UP_KEY, ARROW_DOWN_KEY, HOME_KEY, END_KEY].includes(event.key))) {\n      return\n    }\n\n    event.stopPropagation()// stopPropagation/preventDefault both added to support up/down keys without scrolling the page\n    event.preventDefault()\n\n    const children = this._getChildren().filter(element => !isDisabled(element))\n    let nextActiveElement\n\n    if ([HOME_KEY, END_KEY].includes(event.key)) {\n      nextActiveElement = children[event.key === HOME_KEY ? 0 : children.length - 1]\n    } else {\n      const isNext = [ARROW_RIGHT_KEY, ARROW_DOWN_KEY].includes(event.key)\n      nextActiveElement = getNextActiveElement(children, event.target, isNext, true)\n    }\n\n    if (nextActiveElement) {\n      nextActiveElement.focus({ preventScroll: true })\n      Tab.getOrCreateInstance(nextActiveElement).show()\n    }\n  }\n\n  _getChildren() { // collection of inner elements\n    return SelectorEngine.find(SELECTOR_INNER_ELEM, this._parent)\n  }\n\n  _getActiveElem() {\n    return this._getChildren().find(child => this._elemIsActive(child)) || null\n  }\n\n  _setInitialAttributes(parent, children) {\n    this._setAttributeIfNotExists(parent, 'role', 'tablist')\n\n    for (const child of children) {\n      this._setInitialAttributesOnChild(child)\n    }\n  }\n\n  _setInitialAttributesOnChild(child) {\n    child = this._getInnerElement(child)\n    const isActive = this._elemIsActive(child)\n    const outerElem = this._getOuterElement(child)\n    child.setAttribute('aria-selected', isActive)\n\n    if (outerElem !== child) {\n      this._setAttributeIfNotExists(outerElem, 'role', 'presentation')\n    }\n\n    if (!isActive) {\n      child.setAttribute('tabindex', '-1')\n    }\n\n    this._setAttributeIfNotExists(child, 'role', 'tab')\n\n    // set attributes to the related panel too\n    this._setInitialAttributesOnTargetPanel(child)\n  }\n\n  _setInitialAttributesOnTargetPanel(child) {\n    const target = SelectorEngine.getElementFromSelector(child)\n\n    if (!target) {\n      return\n    }\n\n    this._setAttributeIfNotExists(target, 'role', 'tabpanel')\n\n    if (child.id) {\n      this._setAttributeIfNotExists(target, 'aria-labelledby', `${child.id}`)\n    }\n  }\n\n  _toggleDropDown(element, open) {\n    const outerElem = this._getOuterElement(element)\n    if (!outerElem.classList.contains(CLASS_DROPDOWN)) {\n      return\n    }\n\n    const toggle = (selector, className) => {\n      const element = SelectorEngine.findOne(selector, outerElem)\n      if (element) {\n        element.classList.toggle(className, open)\n      }\n    }\n\n    toggle(SELECTOR_DROPDOWN_TOGGLE, CLASS_NAME_ACTIVE)\n    toggle(SELECTOR_DROPDOWN_MENU, CLASS_NAME_SHOW)\n    outerElem.setAttribute('aria-expanded', open)\n  }\n\n  _setAttributeIfNotExists(element, attribute, value) {\n    if (!element.hasAttribute(attribute)) {\n      element.setAttribute(attribute, value)\n    }\n  }\n\n  _elemIsActive(elem) {\n    return elem.classList.contains(CLASS_NAME_ACTIVE)\n  }\n\n  // Try to get the inner element (usually the .nav-link)\n  _getInnerElement(elem) {\n    return elem.matches(SELECTOR_INNER_ELEM) ? elem : SelectorEngine.findOne(SELECTOR_INNER_ELEM, elem)\n  }\n\n  // Try to get the outer element (usually the .nav-item)\n  _getOuterElement(elem) {\n    return elem.closest(SELECTOR_OUTER) || elem\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Tab.getOrCreateInstance(this)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (data[config] === undefined || config.startsWith('_') || config === 'constructor') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  if (['A', 'AREA'].includes(this.tagName)) {\n    event.preventDefault()\n  }\n\n  if (isDisabled(this)) {\n    return\n  }\n\n  Tab.getOrCreateInstance(this).show()\n})\n\n/**\n * Initialize on focus\n */\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  for (const element of SelectorEngine.find(SELECTOR_DATA_TOGGLE_ACTIVE)) {\n    Tab.getOrCreateInstance(element)\n  }\n})\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Tab)\n\nexport default Tab\n"], "names": ["NAME", "DATA_KEY", "EVENT_KEY", "EVENT_HIDE", "EVENT_HIDDEN", "EVENT_SHOW", "EVENT_SHOWN", "EVENT_CLICK_DATA_API", "EVENT_KEYDOWN", "EVENT_LOAD_DATA_API", "ARROW_LEFT_KEY", "ARROW_RIGHT_KEY", "ARROW_UP_KEY", "ARROW_DOWN_KEY", "HOME_KEY", "END_KEY", "CLASS_NAME_ACTIVE", "CLASS_NAME_FADE", "CLASS_NAME_SHOW", "CLASS_DROPDOWN", "SELECTOR_DROPDOWN_TOGGLE", "SELECTOR_DROPDOWN_MENU", "NOT_SELECTOR_DROPDOWN_TOGGLE", "SELECTOR_TAB_PANEL", "SELECTOR_OUTER", "SELECTOR_INNER", "SELECTOR_DATA_TOGGLE", "SELECTOR_INNER_ELEM", "SELECTOR_DATA_TOGGLE_ACTIVE", "Tab", "BaseComponent", "constructor", "element", "_parent", "_element", "closest", "_setInitialAttributes", "_get<PERSON><PERSON><PERSON>n", "EventHandler", "on", "event", "_keydown", "show", "innerElem", "_elemIsActive", "active", "_getActiveElem", "hideEvent", "trigger", "relatedTarget", "showEvent", "defaultPrevented", "_deactivate", "_activate", "relatedElem", "classList", "add", "SelectorEngine", "getElementFromSelector", "complete", "getAttribute", "removeAttribute", "setAttribute", "_toggleDropDown", "_queueCallback", "contains", "remove", "blur", "includes", "key", "stopPropagation", "preventDefault", "children", "filter", "isDisabled", "nextActiveElement", "length", "isNext", "getNextActiveElement", "target", "focus", "preventScroll", "getOrCreateInstance", "find", "child", "parent", "_setAttributeIfNotExists", "_setInitialAttributesOnChild", "_getInnerElement", "isActive", "outerElem", "_getOuterElement", "_setInitialAttributesOnTargetPanel", "id", "open", "toggle", "selector", "className", "findOne", "attribute", "value", "hasAttribute", "elem", "matches", "jQueryInterface", "config", "each", "data", "undefined", "startsWith", "TypeError", "document", "tagName", "window", "defineJQueryPlugin"], "mappings": ";;;;;;;;;;;EAAA;EACA;EACA;EACA;EACA;EACA;;;EAOA;EACA;EACA;;EAEA,MAAMA,IAAI,GAAG,KAAK;EAClB,MAAMC,QAAQ,GAAG,QAAQ;EACzB,MAAMC,SAAS,GAAG,CAAID,CAAAA,EAAAA,QAAQ,CAAE,CAAA;EAEhC,MAAME,UAAU,GAAG,CAAOD,IAAAA,EAAAA,SAAS,CAAE,CAAA;EACrC,MAAME,YAAY,GAAG,CAASF,MAAAA,EAAAA,SAAS,CAAE,CAAA;EACzC,MAAMG,UAAU,GAAG,CAAOH,IAAAA,EAAAA,SAAS,CAAE,CAAA;EACrC,MAAMI,WAAW,GAAG,CAAQJ,KAAAA,EAAAA,SAAS,CAAE,CAAA;EACvC,MAAMK,oBAAoB,GAAG,CAAQL,KAAAA,EAAAA,SAAS,CAAE,CAAA;EAChD,MAAMM,aAAa,GAAG,CAAUN,OAAAA,EAAAA,SAAS,CAAE,CAAA;EAC3C,MAAMO,mBAAmB,GAAG,CAAOP,IAAAA,EAAAA,SAAS,CAAE,CAAA;EAE9C,MAAMQ,cAAc,GAAG,WAAW;EAClC,MAAMC,eAAe,GAAG,YAAY;EACpC,MAAMC,YAAY,GAAG,SAAS;EAC9B,MAAMC,cAAc,GAAG,WAAW;EAClC,MAAMC,QAAQ,GAAG,MAAM;EACvB,MAAMC,OAAO,GAAG,KAAK;EAErB,MAAMC,iBAAiB,GAAG,QAAQ;EAClC,MAAMC,eAAe,GAAG,MAAM;EAC9B,MAAMC,eAAe,GAAG,MAAM;EAC9B,MAAMC,cAAc,GAAG,UAAU;EAEjC,MAAMC,wBAAwB,GAAG,kBAAkB;EACnD,MAAMC,sBAAsB,GAAG,gBAAgB;EAC/C,MAAMC,4BAA4B,GAAG,CAAQF,KAAAA,EAAAA,wBAAwB,CAAG,CAAA,CAAA;EAExE,MAAMG,kBAAkB,GAAG,qCAAqC;EAChE,MAAMC,cAAc,GAAG,6BAA6B;EACpD,MAAMC,cAAc,GAAG,CAAYH,SAAAA,EAAAA,4BAA4B,qBAAqBA,4BAA4B,CAAA,cAAA,EAAiBA,4BAA4B,CAAE,CAAA;EAC/J,MAAMI,oBAAoB,GAAG,0EAA0E,CAAC;EACxG,MAAMC,mBAAmB,GAAG,CAAA,EAAGF,cAAc,CAAA,EAAA,EAAKC,oBAAoB,CAAE,CAAA;EAExE,MAAME,2BAA2B,GAAG,CAAIZ,CAAAA,EAAAA,iBAAiB,4BAA4BA,iBAAiB,CAAA,0BAAA,EAA6BA,iBAAiB,CAAyB,uBAAA,CAAA;;EAE7K;EACA;EACA;;EAEA,MAAMa,GAAG,SAASC,aAAa,CAAC;IAC9BC,WAAWA,CAACC,OAAO,EAAE;MACnB,KAAK,CAACA,OAAO,CAAC;MACd,IAAI,CAACC,OAAO,GAAG,IAAI,CAACC,QAAQ,CAACC,OAAO,CAACZ,kBAAkB,CAAC;EAExD,IAAA,IAAI,CAAC,IAAI,CAACU,OAAO,EAAE;EACjB,MAAA;EACA;EACA;EACF;;EAEA;EACA,IAAA,IAAI,CAACG,qBAAqB,CAAC,IAAI,CAACH,OAAO,EAAE,IAAI,CAACI,YAAY,EAAE,CAAC;EAE7DC,IAAAA,YAAY,CAACC,EAAE,CAAC,IAAI,CAACL,QAAQ,EAAE1B,aAAa,EAAEgC,KAAK,IAAI,IAAI,CAACC,QAAQ,CAACD,KAAK,CAAC,CAAC;EAC9E;;EAEA;IACA,WAAWxC,IAAIA,GAAG;EAChB,IAAA,OAAOA,IAAI;EACb;;EAEA;EACA0C,EAAAA,IAAIA,GAAG;EAAE;EACP,IAAA,MAAMC,SAAS,GAAG,IAAI,CAACT,QAAQ;EAC/B,IAAA,IAAI,IAAI,CAACU,aAAa,CAACD,SAAS,CAAC,EAAE;EACjC,MAAA;EACF;;EAEA;EACA,IAAA,MAAME,MAAM,GAAG,IAAI,CAACC,cAAc,EAAE;MAEpC,MAAMC,SAAS,GAAGF,MAAM,GACtBP,YAAY,CAACU,OAAO,CAACH,MAAM,EAAE1C,UAAU,EAAE;EAAE8C,MAAAA,aAAa,EAAEN;OAAW,CAAC,GACtE,IAAI;MAEN,MAAMO,SAAS,GAAGZ,YAAY,CAACU,OAAO,CAACL,SAAS,EAAEtC,UAAU,EAAE;EAAE4C,MAAAA,aAAa,EAAEJ;EAAO,KAAC,CAAC;MAExF,IAAIK,SAAS,CAACC,gBAAgB,IAAKJ,SAAS,IAAIA,SAAS,CAACI,gBAAiB,EAAE;EAC3E,MAAA;EACF;EAEA,IAAA,IAAI,CAACC,WAAW,CAACP,MAAM,EAAEF,SAAS,CAAC;EACnC,IAAA,IAAI,CAACU,SAAS,CAACV,SAAS,EAAEE,MAAM,CAAC;EACnC;;EAEA;EACAQ,EAAAA,SAASA,CAACrB,OAAO,EAAEsB,WAAW,EAAE;MAC9B,IAAI,CAACtB,OAAO,EAAE;EACZ,MAAA;EACF;EAEAA,IAAAA,OAAO,CAACuB,SAAS,CAACC,GAAG,CAACxC,iBAAiB,CAAC;MAExC,IAAI,CAACqC,SAAS,CAACI,cAAc,CAACC,sBAAsB,CAAC1B,OAAO,CAAC,CAAC,CAAC;;MAE/D,MAAM2B,QAAQ,GAAGA,MAAM;QACrB,IAAI3B,OAAO,CAAC4B,YAAY,CAAC,MAAM,CAAC,KAAK,KAAK,EAAE;EAC1C5B,QAAAA,OAAO,CAACuB,SAAS,CAACC,GAAG,CAACtC,eAAe,CAAC;EACtC,QAAA;EACF;EAEAc,MAAAA,OAAO,CAAC6B,eAAe,CAAC,UAAU,CAAC;EACnC7B,MAAAA,OAAO,CAAC8B,YAAY,CAAC,eAAe,EAAE,IAAI,CAAC;EAC3C,MAAA,IAAI,CAACC,eAAe,CAAC/B,OAAO,EAAE,IAAI,CAAC;EACnCM,MAAAA,YAAY,CAACU,OAAO,CAAChB,OAAO,EAAE1B,WAAW,EAAE;EACzC2C,QAAAA,aAAa,EAAEK;EACjB,OAAC,CAAC;OACH;EAED,IAAA,IAAI,CAACU,cAAc,CAACL,QAAQ,EAAE3B,OAAO,EAAEA,OAAO,CAACuB,SAAS,CAACU,QAAQ,CAAChD,eAAe,CAAC,CAAC;EACrF;EAEAmC,EAAAA,WAAWA,CAACpB,OAAO,EAAEsB,WAAW,EAAE;MAChC,IAAI,CAACtB,OAAO,EAAE;EACZ,MAAA;EACF;EAEAA,IAAAA,OAAO,CAACuB,SAAS,CAACW,MAAM,CAAClD,iBAAiB,CAAC;MAC3CgB,OAAO,CAACmC,IAAI,EAAE;MAEd,IAAI,CAACf,WAAW,CAACK,cAAc,CAACC,sBAAsB,CAAC1B,OAAO,CAAC,CAAC,CAAC;;MAEjE,MAAM2B,QAAQ,GAAGA,MAAM;QACrB,IAAI3B,OAAO,CAAC4B,YAAY,CAAC,MAAM,CAAC,KAAK,KAAK,EAAE;EAC1C5B,QAAAA,OAAO,CAACuB,SAAS,CAACW,MAAM,CAAChD,eAAe,CAAC;EACzC,QAAA;EACF;EAEAc,MAAAA,OAAO,CAAC8B,YAAY,CAAC,eAAe,EAAE,KAAK,CAAC;EAC5C9B,MAAAA,OAAO,CAAC8B,YAAY,CAAC,UAAU,EAAE,IAAI,CAAC;EACtC,MAAA,IAAI,CAACC,eAAe,CAAC/B,OAAO,EAAE,KAAK,CAAC;EACpCM,MAAAA,YAAY,CAACU,OAAO,CAAChB,OAAO,EAAE5B,YAAY,EAAE;EAAE6C,QAAAA,aAAa,EAAEK;EAAY,OAAC,CAAC;OAC5E;EAED,IAAA,IAAI,CAACU,cAAc,CAACL,QAAQ,EAAE3B,OAAO,EAAEA,OAAO,CAACuB,SAAS,CAACU,QAAQ,CAAChD,eAAe,CAAC,CAAC;EACrF;IAEAwB,QAAQA,CAACD,KAAK,EAAE;MACd,IAAI,CAAE,CAAC9B,cAAc,EAAEC,eAAe,EAAEC,YAAY,EAAEC,cAAc,EAAEC,QAAQ,EAAEC,OAAO,CAAC,CAACqD,QAAQ,CAAC5B,KAAK,CAAC6B,GAAG,CAAE,EAAE;EAC7G,MAAA;EACF;MAEA7B,KAAK,CAAC8B,eAAe,EAAE,CAAA;MACvB9B,KAAK,CAAC+B,cAAc,EAAE;EAEtB,IAAA,MAAMC,QAAQ,GAAG,IAAI,CAACnC,YAAY,EAAE,CAACoC,MAAM,CAACzC,OAAO,IAAI,CAAC0C,mBAAU,CAAC1C,OAAO,CAAC,CAAC;EAC5E,IAAA,IAAI2C,iBAAiB;EAErB,IAAA,IAAI,CAAC7D,QAAQ,EAAEC,OAAO,CAAC,CAACqD,QAAQ,CAAC5B,KAAK,CAAC6B,GAAG,CAAC,EAAE;EAC3CM,MAAAA,iBAAiB,GAAGH,QAAQ,CAAChC,KAAK,CAAC6B,GAAG,KAAKvD,QAAQ,GAAG,CAAC,GAAG0D,QAAQ,CAACI,MAAM,GAAG,CAAC,CAAC;EAChF,KAAC,MAAM;EACL,MAAA,MAAMC,MAAM,GAAG,CAAClE,eAAe,EAAEE,cAAc,CAAC,CAACuD,QAAQ,CAAC5B,KAAK,CAAC6B,GAAG,CAAC;EACpEM,MAAAA,iBAAiB,GAAGG,6BAAoB,CAACN,QAAQ,EAAEhC,KAAK,CAACuC,MAAM,EAAEF,MAAM,EAAE,IAAI,CAAC;EAChF;EAEA,IAAA,IAAIF,iBAAiB,EAAE;QACrBA,iBAAiB,CAACK,KAAK,CAAC;EAAEC,QAAAA,aAAa,EAAE;EAAK,OAAC,CAAC;QAChDpD,GAAG,CAACqD,mBAAmB,CAACP,iBAAiB,CAAC,CAACjC,IAAI,EAAE;EACnD;EACF;EAEAL,EAAAA,YAAYA,GAAG;EAAE;MACf,OAAOoB,cAAc,CAAC0B,IAAI,CAACxD,mBAAmB,EAAE,IAAI,CAACM,OAAO,CAAC;EAC/D;EAEAa,EAAAA,cAAcA,GAAG;EACf,IAAA,OAAO,IAAI,CAACT,YAAY,EAAE,CAAC8C,IAAI,CAACC,KAAK,IAAI,IAAI,CAACxC,aAAa,CAACwC,KAAK,CAAC,CAAC,IAAI,IAAI;EAC7E;EAEAhD,EAAAA,qBAAqBA,CAACiD,MAAM,EAAEb,QAAQ,EAAE;MACtC,IAAI,CAACc,wBAAwB,CAACD,MAAM,EAAE,MAAM,EAAE,SAAS,CAAC;EAExD,IAAA,KAAK,MAAMD,KAAK,IAAIZ,QAAQ,EAAE;EAC5B,MAAA,IAAI,CAACe,4BAA4B,CAACH,KAAK,CAAC;EAC1C;EACF;IAEAG,4BAA4BA,CAACH,KAAK,EAAE;EAClCA,IAAAA,KAAK,GAAG,IAAI,CAACI,gBAAgB,CAACJ,KAAK,CAAC;EACpC,IAAA,MAAMK,QAAQ,GAAG,IAAI,CAAC7C,aAAa,CAACwC,KAAK,CAAC;EAC1C,IAAA,MAAMM,SAAS,GAAG,IAAI,CAACC,gBAAgB,CAACP,KAAK,CAAC;EAC9CA,IAAAA,KAAK,CAACtB,YAAY,CAAC,eAAe,EAAE2B,QAAQ,CAAC;MAE7C,IAAIC,SAAS,KAAKN,KAAK,EAAE;QACvB,IAAI,CAACE,wBAAwB,CAACI,SAAS,EAAE,MAAM,EAAE,cAAc,CAAC;EAClE;MAEA,IAAI,CAACD,QAAQ,EAAE;EACbL,MAAAA,KAAK,CAACtB,YAAY,CAAC,UAAU,EAAE,IAAI,CAAC;EACtC;MAEA,IAAI,CAACwB,wBAAwB,CAACF,KAAK,EAAE,MAAM,EAAE,KAAK,CAAC;;EAEnD;EACA,IAAA,IAAI,CAACQ,kCAAkC,CAACR,KAAK,CAAC;EAChD;IAEAQ,kCAAkCA,CAACR,KAAK,EAAE;EACxC,IAAA,MAAML,MAAM,GAAGtB,cAAc,CAACC,sBAAsB,CAAC0B,KAAK,CAAC;MAE3D,IAAI,CAACL,MAAM,EAAE;EACX,MAAA;EACF;MAEA,IAAI,CAACO,wBAAwB,CAACP,MAAM,EAAE,MAAM,EAAE,UAAU,CAAC;MAEzD,IAAIK,KAAK,CAACS,EAAE,EAAE;EACZ,MAAA,IAAI,CAACP,wBAAwB,CAACP,MAAM,EAAE,iBAAiB,EAAE,CAAA,EAAGK,KAAK,CAACS,EAAE,CAAA,CAAE,CAAC;EACzE;EACF;EAEA9B,EAAAA,eAAeA,CAAC/B,OAAO,EAAE8D,IAAI,EAAE;EAC7B,IAAA,MAAMJ,SAAS,GAAG,IAAI,CAACC,gBAAgB,CAAC3D,OAAO,CAAC;MAChD,IAAI,CAAC0D,SAAS,CAACnC,SAAS,CAACU,QAAQ,CAAC9C,cAAc,CAAC,EAAE;EACjD,MAAA;EACF;EAEA,IAAA,MAAM4E,MAAM,GAAGA,CAACC,QAAQ,EAAEC,SAAS,KAAK;QACtC,MAAMjE,OAAO,GAAGyB,cAAc,CAACyC,OAAO,CAACF,QAAQ,EAAEN,SAAS,CAAC;EAC3D,MAAA,IAAI1D,OAAO,EAAE;UACXA,OAAO,CAACuB,SAAS,CAACwC,MAAM,CAACE,SAAS,EAAEH,IAAI,CAAC;EAC3C;OACD;EAEDC,IAAAA,MAAM,CAAC3E,wBAAwB,EAAEJ,iBAAiB,CAAC;EACnD+E,IAAAA,MAAM,CAAC1E,sBAAsB,EAAEH,eAAe,CAAC;EAC/CwE,IAAAA,SAAS,CAAC5B,YAAY,CAAC,eAAe,EAAEgC,IAAI,CAAC;EAC/C;EAEAR,EAAAA,wBAAwBA,CAACtD,OAAO,EAAEmE,SAAS,EAAEC,KAAK,EAAE;EAClD,IAAA,IAAI,CAACpE,OAAO,CAACqE,YAAY,CAACF,SAAS,CAAC,EAAE;EACpCnE,MAAAA,OAAO,CAAC8B,YAAY,CAACqC,SAAS,EAAEC,KAAK,CAAC;EACxC;EACF;IAEAxD,aAAaA,CAAC0D,IAAI,EAAE;EAClB,IAAA,OAAOA,IAAI,CAAC/C,SAAS,CAACU,QAAQ,CAACjD,iBAAiB,CAAC;EACnD;;EAEA;IACAwE,gBAAgBA,CAACc,IAAI,EAAE;EACrB,IAAA,OAAOA,IAAI,CAACC,OAAO,CAAC5E,mBAAmB,CAAC,GAAG2E,IAAI,GAAG7C,cAAc,CAACyC,OAAO,CAACvE,mBAAmB,EAAE2E,IAAI,CAAC;EACrG;;EAEA;IACAX,gBAAgBA,CAACW,IAAI,EAAE;EACrB,IAAA,OAAOA,IAAI,CAACnE,OAAO,CAACX,cAAc,CAAC,IAAI8E,IAAI;EAC7C;;EAEA;IACA,OAAOE,eAAeA,CAACC,MAAM,EAAE;EAC7B,IAAA,OAAO,IAAI,CAACC,IAAI,CAAC,YAAY;EAC3B,MAAA,MAAMC,IAAI,GAAG9E,GAAG,CAACqD,mBAAmB,CAAC,IAAI,CAAC;EAE1C,MAAA,IAAI,OAAOuB,MAAM,KAAK,QAAQ,EAAE;EAC9B,QAAA;EACF;EAEA,MAAA,IAAIE,IAAI,CAACF,MAAM,CAAC,KAAKG,SAAS,IAAIH,MAAM,CAACI,UAAU,CAAC,GAAG,CAAC,IAAIJ,MAAM,KAAK,aAAa,EAAE;EACpF,QAAA,MAAM,IAAIK,SAAS,CAAC,CAAoBL,iBAAAA,EAAAA,MAAM,GAAG,CAAC;EACpD;EAEAE,MAAAA,IAAI,CAACF,MAAM,CAAC,EAAE;EAChB,KAAC,CAAC;EACJ;EACF;;EAEA;EACA;EACA;;EAEAnE,YAAY,CAACC,EAAE,CAACwE,QAAQ,EAAExG,oBAAoB,EAAEmB,oBAAoB,EAAE,UAAUc,KAAK,EAAE;EACrF,EAAA,IAAI,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC4B,QAAQ,CAAC,IAAI,CAAC4C,OAAO,CAAC,EAAE;MACxCxE,KAAK,CAAC+B,cAAc,EAAE;EACxB;EAEA,EAAA,IAAIG,mBAAU,CAAC,IAAI,CAAC,EAAE;EACpB,IAAA;EACF;IAEA7C,GAAG,CAACqD,mBAAmB,CAAC,IAAI,CAAC,CAACxC,IAAI,EAAE;EACtC,CAAC,CAAC;;EAEF;EACA;EACA;EACAJ,YAAY,CAACC,EAAE,CAAC0E,MAAM,EAAExG,mBAAmB,EAAE,MAAM;IACjD,KAAK,MAAMuB,OAAO,IAAIyB,cAAc,CAAC0B,IAAI,CAACvD,2BAA2B,CAAC,EAAE;EACtEC,IAAAA,GAAG,CAACqD,mBAAmB,CAAClD,OAAO,CAAC;EAClC;EACF,CAAC,CAAC;EACF;EACA;EACA;;AAEAkF,6BAAkB,CAACrF,GAAG,CAAC;;;;;;;;"}