{"name": "jssha", "version": "3.3.1", "description": "jsSHA implements the complete Secure Hash Standard (SHA) family (SHA-1, SHA-224/256/384/512, SHA3-224/256/384/512, SHAKE128/256, cSHAKE128/256, and KMAC128/256) with HMAC", "main": "./dist/sha.js", "exports": {".": {"types": "./dist/sha.d.ts", "import": "./dist/sha.mjs", "require": "./dist/sha.js"}, "./sha1": {"types": "./dist/sha1.d.ts", "import": "./dist/sha1.mjs", "require": "./dist/sha1.js"}, "./dist/sha1": {"types": "./dist/sha1.d.ts", "import": "./dist/sha1.mjs", "require": "./dist/sha1.js"}, "./sha256": {"types": "./dist/sha256.d.ts", "import": "./dist/sha256.mjs", "require": "./dist/sha256.js"}, "./dist/sha256": {"types": "./dist/sha256.d.ts", "import": "./dist/sha256.mjs", "require": "./dist/sha256.js"}, "./sha512": {"types": "./dist/sha512.d.ts", "import": "./dist/sha512.mjs", "require": "./dist/sha512.js"}, "./dist/sha512": {"types": "./dist/sha512.d.ts", "import": "./dist/sha512.mjs", "require": "./dist/sha512.js"}, "./sha3": {"types": "./dist/sha3.d.ts", "import": "./dist/sha3.mjs", "require": "./dist/sha3.js"}, "./dist/sha3": {"types": "./dist/sha3.d.ts", "import": "./dist/sha3.mjs", "require": "./dist/sha3.js"}, "./package.json": "./package.json"}, "module": "./dist/sha.mjs", "types": "./dist/sha.d.ts", "repository": {"type": "git", "url": "https://github.com/Caligatio/jsSHA.git"}, "keywords": ["SHA-1", "SHA-224", "SHA3-224", "SHA-256", "SHA3-256", "SHA-384", "SHA3-384", "SHA-512", "SHA3-512", "SHAKE-128", "SHAKE-256", "SHAKE128", "SHAKE256", "cSHAKE128", "cSHAKE256", "KMAC128", "KMAC256", "SHA1", "SHA224", "SHA256", "SHA384", "SHA512", "SHA-2", "SHA2", "SHA-3", "SHA3", "SHAKE", "cSHAKE", "cSHAKE-128", "cSHAKE-256", "HMAC", "KMAC", "KMAC-128", "KMAC-256", "hash", "cryptography"], "license": "BSD-3-<PERSON><PERSON>", "author": "<PERSON> <<EMAIL>>", "bugs": {"url": "https://github.com/Caligatio/jsSHA/issues"}, "engines": {"node": "*"}, "homepage": "https://github.com/Caligatio/jsSHA", "devDependencies": {"@rollup/plugin-terser": "^0.4.3", "@rollup/plugin-typescript": "^11.1.2", "@types/chai": "^4.3.5", "@types/mocha": "^10.0.1", "@types/rewire": "^2.5.28", "@types/sinon": "^10.0.16", "@typescript-eslint/eslint-plugin": "^6.2.1", "@typescript-eslint/parser": "^6.2.1", "chai": "^4.3.7", "coveralls": "^3.1.1", "eslint": "^8.46.0", "husky": "^8.0.3", "karma": "^6.4.2", "karma-chai": "^0.1.0", "karma-chrome-launcher": "^3.2.0", "karma-firefox-launcher": "^2.1.2", "karma-mocha": "^2.0.1", "lint-staged": "^13.2.3", "mocha": "^10.2.0", "nyc": "^15.1.0", "prettier": "^3.0.1", "rewire": "^6.0.0", "rollup": "^3.27.2", "rollup-plugin-dts": "^5.3.1", "sinon": "^15.2.0", "ts-node": "^10.9.1", "tslib": "^2.6.1", "typescript": "^5.1.6"}, "scripts": {"build": "rollup -c", "test": "nyc --reporter=html --reporter=text mocha test/src/*.ts", "test_dist": "mocha test/dist/ && karma start karma.conf.js --file-variant sha && karma start karma.conf.js --file-variant sha1 && karma start karma.conf.js --file-variant sha256 && karma start karma.conf.js --file-variant sha512 && karma start karma.conf.js --file-variant sha3", "coverage": "nyc report --reporter=text-lcov | coveralls", "prepare": "husky install"}, "mocha": {"require": ["ts-node/register"], "timeout": 10000}, "nyc": {"require": ["ts-node/register"], "exclude": ["dist", "*.js", "coverage", "test", "rollup.config.mjs"], "reporter": ["text", "html"], "all": true}, "prettier": {"printWidth": 120, "proseWrap": "always", "overrides": [{"files": "*.md", "options": {"printWidth": 80}}]}, "lint-staged": {"*.{js,ts}": "eslint --cache --fix", "*.{js,ts,css,md,html,json}": "prettier --write"}}