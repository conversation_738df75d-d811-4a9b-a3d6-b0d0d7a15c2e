{"version": 3, "file": "sha.js", "sources": ["../src/converters.ts", "../src/common.ts", "../node_modules/tslib/tslib.es6.js", "../src/primitives_32.ts", "../src/sha1.ts", "../src/sha256.ts", "../src/primitives_64.ts", "../src/sha512.ts", "../src/sha3.ts", "../src/sha.ts"], "sourcesContent": ["import { packedValue, EncodingType, FormatType } from \"./custom_types\";\n/**\n * Return type for all the *2packed functions\n */\nconst b64Tab = \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/\";\n\nconst arraybuffer_error = \"ARRAYBUFFER not supported by this environment\";\nconst uint8array_error = \"UINT8ARRAY not supported by this environment\";\n\n/**\n * Convert a string to an array of words.\n *\n * There is a known bug with an odd number of existing bytes and using a UTF-16 encoding.  However, this function is\n * used such that the existing bytes are always a result of a previous UTF-16 str2packed call and therefore there \n * should never be an odd number of existing bytes.\n\n * @param str Unicode string to be converted to binary representation.\n * @param utfType The Unicode type to use to encode the source string.\n * @param existingPacked A packed int array of bytes to append the results to.\n * @param existingPackedLen The number of bits in `existingPacked`.\n * @param bigEndianMod Modifier for whether hash function is big or small endian.\n * @returns Hashmap of the packed values.\n */\nfunction str2packed(\n  str: string,\n  utfType: EncodingType,\n  existingPacked: number[] | undefined,\n  existingPackedLen: number | undefined,\n  bigEndianMod: -1 | 1\n): packedValue {\n  let codePnt,\n    codePntArr,\n    byteCnt = 0,\n    i,\n    j,\n    intOffset,\n    byteOffset,\n    shiftModifier,\n    transposeBytes;\n\n  existingPackedLen = existingPackedLen || 0;\n  const packed = existingPacked || [0],\n    existingByteLen = existingPackedLen >>> 3;\n\n  if (\"UTF8\" === utfType) {\n    shiftModifier = bigEndianMod === -1 ? 3 : 0;\n    for (i = 0; i < str.length; i += 1) {\n      codePnt = str.charCodeAt(i);\n      codePntArr = [];\n\n      if (0x80 > codePnt) {\n        codePntArr.push(codePnt);\n      } else if (0x800 > codePnt) {\n        codePntArr.push(0xc0 | (codePnt >>> 6));\n        codePntArr.push(0x80 | (codePnt & 0x3f));\n      } else if (0xd800 > codePnt || 0xe000 <= codePnt) {\n        codePntArr.push(0xe0 | (codePnt >>> 12), 0x80 | ((codePnt >>> 6) & 0x3f), 0x80 | (codePnt & 0x3f));\n      } else {\n        i += 1;\n        codePnt = 0x10000 + (((codePnt & 0x3ff) << 10) | (str.charCodeAt(i) & 0x3ff));\n        codePntArr.push(\n          0xf0 | (codePnt >>> 18),\n          0x80 | ((codePnt >>> 12) & 0x3f),\n          0x80 | ((codePnt >>> 6) & 0x3f),\n          0x80 | (codePnt & 0x3f)\n        );\n      }\n\n      for (j = 0; j < codePntArr.length; j += 1) {\n        byteOffset = byteCnt + existingByteLen;\n        intOffset = byteOffset >>> 2;\n        while (packed.length <= intOffset) {\n          packed.push(0);\n        }\n        /* Known bug kicks in here */\n        packed[intOffset] |= codePntArr[j] << (8 * (shiftModifier + bigEndianMod * (byteOffset % 4)));\n        byteCnt += 1;\n      }\n    }\n  } else {\n    /* UTF16BE or UTF16LE */\n    shiftModifier = bigEndianMod === -1 ? 2 : 0;\n    /* Internally strings are UTF-16BE so transpose bytes under two conditions:\n     * need LE and not switching endianness due to SHA-3\n     * need BE and switching endianness due to SHA-3 */\n    transposeBytes = (\"UTF16LE\" === utfType && bigEndianMod !== 1) || (\"UTF16LE\" !== utfType && bigEndianMod === 1);\n    for (i = 0; i < str.length; i += 1) {\n      codePnt = str.charCodeAt(i);\n      if (transposeBytes === true) {\n        j = codePnt & 0xff;\n        codePnt = (j << 8) | (codePnt >>> 8);\n      }\n\n      byteOffset = byteCnt + existingByteLen;\n      intOffset = byteOffset >>> 2;\n      while (packed.length <= intOffset) {\n        packed.push(0);\n      }\n      packed[intOffset] |= codePnt << (8 * (shiftModifier + bigEndianMod * (byteOffset % 4)));\n      byteCnt += 2;\n    }\n  }\n  return { value: packed, binLen: byteCnt * 8 + existingPackedLen };\n}\n\n/**\n * Convert a hex string to an array of words.\n *\n * @param str Hexadecimal string to be converted to binary representation.\n * @param existingPacked A packed int array of bytes to append the results to.\n * @param existingPackedLen The number of bits in `existingPacked` array.\n * @param bigEndianMod Modifier for whether hash function is big or small endian.\n * @returns Hashmap of the packed values.\n */\nfunction hex2packed(\n  str: string,\n  existingPacked: number[] | undefined,\n  existingPackedLen: number | undefined,\n  bigEndianMod: -1 | 1\n): packedValue {\n  let i, num, intOffset, byteOffset;\n\n  if (0 !== str.length % 2) {\n    throw new Error(\"String of HEX type must be in byte increments\");\n  }\n\n  existingPackedLen = existingPackedLen || 0;\n  const packed = existingPacked || [0],\n    existingByteLen = existingPackedLen >>> 3,\n    shiftModifier = bigEndianMod === -1 ? 3 : 0;\n\n  for (i = 0; i < str.length; i += 2) {\n    num = parseInt(str.substr(i, 2), 16);\n    if (!isNaN(num)) {\n      byteOffset = (i >>> 1) + existingByteLen;\n      intOffset = byteOffset >>> 2;\n      while (packed.length <= intOffset) {\n        packed.push(0);\n      }\n      packed[intOffset] |= num << (8 * (shiftModifier + bigEndianMod * (byteOffset % 4)));\n    } else {\n      throw new Error(\"String of HEX type contains invalid characters\");\n    }\n  }\n\n  return { value: packed, binLen: str.length * 4 + existingPackedLen };\n}\n\n/**\n * Convert a string of raw bytes to an array of words.\n *\n * @param str String of raw bytes to be converted to binary representation.\n * @param existingPacked A packed int array of bytes to append the results to.\n * @param existingPackedLen The number of bits in `existingPacked` array.\n * @param bigEndianMod Modifier for whether hash function is big or small endian.\n * @returns Hashmap of the packed values.\n */\nfunction bytes2packed(\n  str: string,\n  existingPacked: number[] | undefined,\n  existingPackedLen: number | undefined,\n  bigEndianMod: -1 | 1\n): packedValue {\n  let codePnt, i, intOffset, byteOffset;\n\n  existingPackedLen = existingPackedLen || 0;\n  const packed = existingPacked || [0],\n    existingByteLen = existingPackedLen >>> 3,\n    shiftModifier = bigEndianMod === -1 ? 3 : 0;\n\n  for (i = 0; i < str.length; i += 1) {\n    codePnt = str.charCodeAt(i);\n\n    byteOffset = i + existingByteLen;\n    intOffset = byteOffset >>> 2;\n    if (packed.length <= intOffset) {\n      packed.push(0);\n    }\n    packed[intOffset] |= codePnt << (8 * (shiftModifier + bigEndianMod * (byteOffset % 4)));\n  }\n\n  return { value: packed, binLen: str.length * 8 + existingPackedLen };\n}\n\n/**\n * Convert a base-64 string to an array of words.\n *\n * @param str Base64-encoded string to be converted to binary representation.\n * @param existingPacked A packed int array of bytes to append the results to.\n * @param existingPackedLen The number of bits in `existingPacked` array.\n * @param bigEndianMod Modifier for whether hash function is big or small endian.\n * @returns Hashmap of the packed values.\n */\nfunction b642packed(\n  str: string,\n  existingPacked: number[] | undefined,\n  existingPackedLen: number | undefined,\n  bigEndianMod: -1 | 1\n): packedValue {\n  let byteCnt = 0,\n    index,\n    i,\n    j,\n    tmpInt,\n    strPart,\n    intOffset,\n    byteOffset;\n\n  existingPackedLen = existingPackedLen || 0;\n  const packed = existingPacked || [0],\n    existingByteLen = existingPackedLen >>> 3,\n    shiftModifier = bigEndianMod === -1 ? 3 : 0,\n    firstEqual = str.indexOf(\"=\");\n\n  if (-1 === str.search(/^[a-zA-Z0-9=+/]+$/)) {\n    throw new Error(\"Invalid character in base-64 string\");\n  }\n\n  str = str.replace(/=/g, \"\");\n  if (-1 !== firstEqual && firstEqual < str.length) {\n    throw new Error(\"Invalid '=' found in base-64 string\");\n  }\n\n  for (i = 0; i < str.length; i += 4) {\n    strPart = str.substr(i, 4);\n    tmpInt = 0;\n\n    for (j = 0; j < strPart.length; j += 1) {\n      index = b64Tab.indexOf(strPart.charAt(j));\n      tmpInt |= index << (18 - 6 * j);\n    }\n\n    for (j = 0; j < strPart.length - 1; j += 1) {\n      byteOffset = byteCnt + existingByteLen;\n      intOffset = byteOffset >>> 2;\n      while (packed.length <= intOffset) {\n        packed.push(0);\n      }\n      packed[intOffset] |=\n        ((tmpInt >>> (16 - j * 8)) & 0xff) << (8 * (shiftModifier + bigEndianMod * (byteOffset % 4)));\n      byteCnt += 1;\n    }\n  }\n\n  return { value: packed, binLen: byteCnt * 8 + existingPackedLen };\n}\n\n/**\n * Convert an Uint8Array to an array of words.\n *\n * @param arr Uint8Array to be converted to binary representation.\n * @param existingPacked A packed int array of bytes to append the results to.\n * @param existingPackedLen The number of bits in `existingPacked` array.\n * @param bigEndianMod Modifier for whether hash function is big or small endian.\n * @returns Hashmap of the packed values.\n */\nfunction uint8array2packed(\n  arr: Uint8Array,\n  existingPacked: number[] | undefined,\n  existingPackedLen: number | undefined,\n  bigEndianMod: -1 | 1\n): packedValue {\n  let i, intOffset, byteOffset;\n\n  existingPackedLen = existingPackedLen || 0;\n  const packed = existingPacked || [0],\n    existingByteLen = existingPackedLen >>> 3,\n    shiftModifier = bigEndianMod === -1 ? 3 : 0;\n\n  for (i = 0; i < arr.length; i += 1) {\n    byteOffset = i + existingByteLen;\n    intOffset = byteOffset >>> 2;\n    if (packed.length <= intOffset) {\n      packed.push(0);\n    }\n    packed[intOffset] |= arr[i] << (8 * (shiftModifier + bigEndianMod * (byteOffset % 4)));\n  }\n\n  return { value: packed, binLen: arr.length * 8 + existingPackedLen };\n}\n\n/**\n * Convert an ArrayBuffer to an array of words\n *\n * @param arr ArrayBuffer to be converted to binary representation.\n * @param existingPacked A packed int array of bytes to append the results to.\n * @param existingPackedLen The number of bits in `existingPacked` array.\n * @param bigEndianMod Modifier for whether hash function is big or small endian.\n * @returns Hashmap of the packed values.\n */\nfunction arraybuffer2packed(\n  arr: ArrayBuffer,\n  existingPacked: number[] | undefined,\n  existingPackedLen: number | undefined,\n  bigEndianMod: -1 | 1\n): packedValue {\n  return uint8array2packed(new Uint8Array(arr), existingPacked, existingPackedLen, bigEndianMod);\n}\n\n/**\n * Function that takes an input format and UTF encoding and returns the appropriate function used to convert the input.\n *\n * @param format The format of the input to be converted\n * @param utfType The string encoding to use for TEXT inputs.\n * @param bigEndianMod Modifier for whether hash function is big or small endian\n * @returns Function that will convert an input to a packed int array.\n */\nexport function getStrConverter(\n  format: FormatType,\n  utfType: EncodingType,\n  bigEndianMod: -1 | 1\n  /* eslint-disable-next-line @typescript-eslint/no-explicit-any */\n): (input: any, existingBin?: number[], existingBinLen?: number) => packedValue {\n  /* Validate encoding */\n  switch (utfType) {\n    case \"UTF8\":\n    /* Fallthrough */\n    case \"UTF16BE\":\n    /* Fallthrough */\n    case \"UTF16LE\":\n      /* Fallthrough */\n      break;\n    default:\n      throw new Error(\"encoding must be UTF8, UTF16BE, or UTF16LE\");\n  }\n\n  /* Map inputFormat to the appropriate converter */\n  switch (format) {\n    case \"HEX\":\n      /**\n       * @param str String of hexadecimal bytes to be converted to binary representation.\n       * @param existingPacked A packed int array of bytes to append the results to.\n       * @param existingPackedLen The number of bits in `existingPacked` array.\n       * @returns Hashmap of the packed values.\n       */\n      return function (str: string, existingBin?: number[], existingBinLen?: number): packedValue {\n        return hex2packed(str, existingBin, existingBinLen, bigEndianMod);\n      };\n    case \"TEXT\":\n      /**\n       * @param str Unicode string to be converted to binary representation.\n       * @param existingPacked A packed int array of bytes to append the results to.\n       * @param existingPackedLen The number of bits in `existingPacked` array.\n       * @returns Hashmap of the packed values.\n       */\n      return function (str: string, existingBin?: number[], existingBinLen?: number): packedValue {\n        return str2packed(str, utfType, existingBin, existingBinLen, bigEndianMod);\n      };\n    case \"B64\":\n      /**\n       * @param str Base64-encoded string to be converted to binary representation.\n       * @param existingPacked A packed int array of bytes to append the results to.\n       * @param existingPackedLen The number of bits in `existingPacked` array.\n       * @returns Hashmap of the packed values.\n       */\n      return function (str: string, existingBin?: number[], existingBinLen?: number): packedValue {\n        return b642packed(str, existingBin, existingBinLen, bigEndianMod);\n      };\n    case \"BYTES\":\n      /**\n       * @param str String of raw bytes to be converted to binary representation.\n       * @param existingPacked A packed int array of bytes to append the results to.\n       * @param existingPackedLen The number of bits in `existingPacked` array.\n       * @returns Hashmap of the packed values.\n       */\n      return function (str: string, existingBin?: number[], existingBinLen?: number): packedValue {\n        return bytes2packed(str, existingBin, existingBinLen, bigEndianMod);\n      };\n    case \"ARRAYBUFFER\":\n      try {\n        new ArrayBuffer(0);\n      } catch (ignore) {\n        throw new Error(arraybuffer_error);\n      }\n      /**\n       * @param arr ArrayBuffer to be converted to binary representation.\n       * @param existingPacked A packed int array of bytes to append the results to.\n       * @param existingPackedLen The number of bits in `existingPacked` array.\n       * @returns Hashmap of the packed values.\n       */\n      return function (arr: ArrayBuffer, existingBin?: number[], existingBinLen?: number): packedValue {\n        return arraybuffer2packed(arr, existingBin, existingBinLen, bigEndianMod);\n      };\n    case \"UINT8ARRAY\":\n      try {\n        new Uint8Array(0);\n      } catch (ignore) {\n        throw new Error(uint8array_error);\n      }\n      /**\n       * @param arr Uint8Array to be converted to binary representation.\n       * @param existingPacked A packed int array of bytes to append the results to.\n       * @param existingPackedLen The number of bits in `existingPacked` array.\n       * @returns Hashmap of the packed values.\n       */\n      return function (arr: Uint8Array, existingBin?: number[], existingBinLen?: number): packedValue {\n        return uint8array2packed(arr, existingBin, existingBinLen, bigEndianMod);\n      };\n    default:\n      throw new Error(\"format must be HEX, TEXT, B64, BYTES, ARRAYBUFFER, or UINT8ARRAY\");\n  }\n}\n\n/**\n * Convert an array of words to a hexadecimal string.\n *\n * toString() won't work here because it removes preceding zeros (e.g. 0x00000001.toString === \"1\" rather than\n * \"00000001\" and 0.toString(16) === \"0\" rather than \"00\").\n *\n * @param packed Array of integers to be converted.\n * @param outputLength Length of output in bits.\n * @param bigEndianMod Modifier for whether hash function is big or small endian.\n * @param formatOpts Hashmap containing validated output formatting options.\n * @returns Hexadecimal representation of `packed`.\n */\nexport function packed2hex(\n  packed: number[],\n  outputLength: number,\n  bigEndianMod: -1 | 1,\n  formatOpts: { outputUpper: boolean; b64Pad: string }\n): string {\n  const hex_tab = \"0123456789abcdef\";\n  let str = \"\",\n    i,\n    srcByte;\n\n  const length = outputLength / 8,\n    shiftModifier = bigEndianMod === -1 ? 3 : 0;\n\n  for (i = 0; i < length; i += 1) {\n    /* The below is more than a byte but it gets taken care of later */\n    srcByte = packed[i >>> 2] >>> (8 * (shiftModifier + bigEndianMod * (i % 4)));\n    str += hex_tab.charAt((srcByte >>> 4) & 0xf) + hex_tab.charAt(srcByte & 0xf);\n  }\n\n  return formatOpts[\"outputUpper\"] ? str.toUpperCase() : str;\n}\n\n/**\n * Convert an array of words to a base-64 string.\n *\n * @param packed Array of integers to be converted.\n * @param outputLength Length of output in bits.\n * @param bigEndianMod Modifier for whether hash function is big or small endian.\n * @param formatOpts Hashmap containing validated output formatting options.\n * @returns Base64-encoded representation of `packed`.\n */\nexport function packed2b64(\n  packed: number[],\n  outputLength: number,\n  bigEndianMod: -1 | 1,\n  formatOpts: { outputUpper: boolean; b64Pad: string }\n): string {\n  let str = \"\",\n    i,\n    j,\n    triplet,\n    int1,\n    int2;\n\n  const length = outputLength / 8,\n    shiftModifier = bigEndianMod === -1 ? 3 : 0;\n\n  for (i = 0; i < length; i += 3) {\n    int1 = i + 1 < length ? packed[(i + 1) >>> 2] : 0;\n    int2 = i + 2 < length ? packed[(i + 2) >>> 2] : 0;\n    triplet =\n      (((packed[i >>> 2] >>> (8 * (shiftModifier + bigEndianMod * (i % 4)))) & 0xff) << 16) |\n      (((int1 >>> (8 * (shiftModifier + bigEndianMod * ((i + 1) % 4)))) & 0xff) << 8) |\n      ((int2 >>> (8 * (shiftModifier + bigEndianMod * ((i + 2) % 4)))) & 0xff);\n    for (j = 0; j < 4; j += 1) {\n      if (i * 8 + j * 6 <= outputLength) {\n        str += b64Tab.charAt((triplet >>> (6 * (3 - j))) & 0x3f);\n      } else {\n        str += formatOpts[\"b64Pad\"];\n      }\n    }\n  }\n  return str;\n}\n\n/**\n * Convert an array of words to raw bytes string.\n *\n * @param packed Array of integers to be converted.\n * @param outputLength Length of output in bits.\n * @param bigEndianMod Modifier for whether hash function is big or small endian.\n * @returns Raw bytes representation of `packed`.\n */\nexport function packed2bytes(packed: number[], outputLength: number, bigEndianMod: -1 | 1): string {\n  let str = \"\",\n    i,\n    srcByte;\n\n  const length = outputLength / 8,\n    shiftModifier = bigEndianMod === -1 ? 3 : 0;\n\n  for (i = 0; i < length; i += 1) {\n    srcByte = (packed[i >>> 2] >>> (8 * (shiftModifier + bigEndianMod * (i % 4)))) & 0xff;\n    str += String.fromCharCode(srcByte);\n  }\n\n  return str;\n}\n\n/**\n * Convert an array of words to an ArrayBuffer.\n *\n * @param packed Array of integers to be converted.\n * @param outputLength Length of output in bits.\n * @param bigEndianMod Modifier for whether hash function is big or small endian.\n * @returns An ArrayBuffer containing bytes from `packed.\n */\nexport function packed2arraybuffer(packed: number[], outputLength: number, bigEndianMod: -1 | 1): ArrayBuffer {\n  let i;\n  const length = outputLength / 8,\n    retVal = new ArrayBuffer(length),\n    arrView = new Uint8Array(retVal),\n    shiftModifier = bigEndianMod === -1 ? 3 : 0;\n\n  for (i = 0; i < length; i += 1) {\n    arrView[i] = (packed[i >>> 2] >>> (8 * (shiftModifier + bigEndianMod * (i % 4)))) & 0xff;\n  }\n\n  return retVal;\n}\n\n/**\n * Convert an array of words to an Uint8Array.\n *\n * @param packed Array of integers to be converted.\n * @param outputLength Length of output in bits.\n * @param bigEndianMod Modifier for whether hash function is big or small endian.\n * @returns An Uint8Array containing bytes from `packed.\n */\nexport function packed2uint8array(packed: number[], outputLength: number, bigEndianMod: -1 | 1): Uint8Array {\n  let i;\n  const length = outputLength / 8,\n    shiftModifier = bigEndianMod === -1 ? 3 : 0,\n    retVal = new Uint8Array(length);\n\n  for (i = 0; i < length; i += 1) {\n    retVal[i] = (packed[i >>> 2] >>> (8 * (shiftModifier + bigEndianMod * (i % 4)))) & 0xff;\n  }\n\n  return retVal;\n}\n\n/**\n * Function that takes an output format and associated parameters and returns a function that converts packed integers\n * to that format.\n *\n * @param format The desired output formatting.\n * @param outputBinLen Output length in bits.\n * @param bigEndianMod Modifier for whether hash function is big or small endian.\n * @param outputOptions Hashmap of output formatting options\n * @returns Function that will convert a packed integer array to desired format.\n */\nexport function getOutputConverter(\n  format: \"HEX\" | \"B64\" | \"BYTES\",\n  outputBinLen: number,\n  bigEndianMod: -1 | 1,\n  outputOptions: { outputUpper: boolean; b64Pad: string }\n): (binarray: number[]) => string;\nexport function getOutputConverter(\n  format: \"ARRAYBUFFER\",\n  outputBinLen: number,\n  bigEndianMod: -1 | 1,\n  outputOptions: { outputUpper: boolean; b64Pad: string }\n): (binarray: number[]) => ArrayBuffer;\nexport function getOutputConverter(\n  format: \"UINT8ARRAY\",\n  outputBinLen: number,\n  bigEndianMod: -1 | 1,\n  outputOptions: { outputUpper: boolean; b64Pad: string }\n): (binarray: number[]) => Uint8Array;\n/* eslint-disable-next-line @typescript-eslint/no-explicit-any */\nexport function getOutputConverter(format: any, outputBinLen: any, bigEndianMod: any, outputOptions: any): any {\n  switch (format) {\n    case \"HEX\":\n      return function (binarray: number[]): string {\n        return packed2hex(binarray, outputBinLen, bigEndianMod, outputOptions);\n      };\n    case \"B64\":\n      return function (binarray: number[]): string {\n        return packed2b64(binarray, outputBinLen, bigEndianMod, outputOptions);\n      };\n    case \"BYTES\":\n      return function (binarray: number[]): string {\n        return packed2bytes(binarray, outputBinLen, bigEndianMod);\n      };\n    case \"ARRAYBUFFER\":\n      try {\n        /* Need to test ArrayBuffer support */\n        new ArrayBuffer(0);\n      } catch (ignore) {\n        throw new Error(arraybuffer_error);\n      }\n      return function (binarray: number[]): ArrayBuffer {\n        return packed2arraybuffer(binarray, outputBinLen, bigEndianMod);\n      };\n    case \"UINT8ARRAY\":\n      try {\n        /* Need to test Uint8Array support */\n        new Uint8Array(0);\n      } catch (ignore) {\n        throw new Error(uint8array_error);\n      }\n      return function (binarray: number[]): Uint8Array {\n        return packed2uint8array(binarray, outputBinLen, bigEndianMod);\n      };\n    default:\n      throw new Error(\"format must be HEX, B64, BYTES, ARRAYBUFFER, or UINT8ARRAY\");\n  }\n}\n", "import { getStrConverter, getOutputConverter } from \"./converters\";\n\nimport {\n  FormatType,\n  EncodingType,\n  FixedLengthOptionsEncodingType,\n  FixedLengthOptionsNoEncodingType,\n  FormatNoTextType,\n  packedValue,\n  GenericInputType,\n} from \"./custom_types\";\n\nexport const TWO_PWR_32 = 4294967296;\n\n/* Constant used in SHA-2 families */\nexport const K_sha2 = [\n  0x428a2f98, 0x71374491, 0xb5c0fbcf, 0xe9b5dba5, 0x3956c25b, 0x59f111f1, 0x923f82a4, 0xab1c5ed5, 0xd807aa98,\n  0x12835b01, 0x243185be, 0x550c7dc3, 0x72be5d74, 0x80deb1fe, 0x9bdc06a7, 0xc19bf174, 0xe49b69c1, 0xefbe4786,\n  0x0fc19dc6, 0x240ca1cc, 0x2de92c6f, 0x4a7484aa, 0x5cb0a9dc, 0x76f988da, 0x983e5152, 0xa831c66d, 0xb00327c8,\n  0xbf597fc7, 0xc6e00bf3, 0xd5a79147, 0x06ca6351, 0x14292967, 0x27b70a85, 0x2e1b2138, 0x4d2c6dfc, 0x53380d13,\n  0x650a7354, 0x766a0abb, 0x81c2c92e, 0x92722c85, 0xa2bfe8a1, 0xa81a664b, 0xc24b8b70, 0xc76c51a3, 0xd192e819,\n  0xd6990624, 0xf40e3585, 0x106aa070, 0x19a4c116, 0x1e376c08, 0x2748774c, 0x34b0bcb5, 0x391c0cb3, 0x4ed8aa4a,\n  0x5b9cca4f, 0x682e6ff3, 0x748f82ee, 0x78a5636f, 0x84c87814, 0x8cc70208, 0x90befffa, 0xa4506ceb, 0xbef9a3f7,\n  0xc67178f2,\n];\n\n/* Constant used in SHA-2 families */\nexport const H_trunc = [0xc1059ed8, 0x367cd507, 0x3070dd17, 0xf70e5939, 0xffc00b31, 0x68581511, 0x64f98fa7, 0xbefa4fa4];\n\n/* Constant used in SHA-2 families */\nexport const H_full = [0x6a09e667, 0xbb67ae85, 0x3c6ef372, 0xa54ff53a, 0x510e527f, 0x9b05688c, 0x1f83d9ab, 0x5be0cd19];\n\nexport const sha_variant_error = \"Chosen SHA variant is not supported\";\nexport const mac_rounds_error = \"Cannot set numRounds with MAC\";\n\n/**\n * Concatenates 2 packed arrays. Clobbers array `a`.\n *\n * @param a First array to concatenate.\n * @param b Second array to concatenate.\n * @returns The concatentation of `a` + `b`.\n */\nexport function packedLEConcat(a: packedValue, b: packedValue): packedValue {\n  let i, arrOffset;\n  const aByteLen = a[\"binLen\"] >>> 3,\n    bByteLen = b[\"binLen\"] >>> 3,\n    leftShiftAmount = aByteLen << 3,\n    rightShiftAmount = (4 - aByteLen) << 3;\n\n  /* If a only contains \"full\" integers, we can just use concat which is so much easier */\n  if (aByteLen % 4 !== 0) {\n    for (i = 0; i < bByteLen; i += 4) {\n      arrOffset = (aByteLen + i) >>> 2;\n      /* Left shift chops off bits over 32-bits */\n      a[\"value\"][arrOffset] |= b[\"value\"][i >>> 2] << leftShiftAmount;\n      a[\"value\"].push(0);\n      a[\"value\"][arrOffset + 1] |= b[\"value\"][i >>> 2] >>> rightShiftAmount;\n    }\n\n    /* Since an unconditional push was performed above, we may have pushed an extra value if it could have been\n       encoded without it.  Check if popping an int off (reducing total length by 4 bytes) is still bigger than the\n       needed size. */\n    if ((a[\"value\"].length << 2) - 4 >= bByteLen + aByteLen) {\n      a[\"value\"].pop();\n    }\n\n    return { value: a[\"value\"], binLen: a[\"binLen\"] + b[\"binLen\"] };\n  } else {\n    return { value: a[\"value\"].concat(b[\"value\"]), binLen: a[\"binLen\"] + b[\"binLen\"] };\n  }\n}\n\n/**\n * Validate hash list containing output formatting options, ensuring presence of every option or adding the default\n * value.\n *\n * @param options Hashmap of output formatting options from user.\n * @returns Validated hashmap containing output formatting options.\n */\nexport function getOutputOpts(options?: {\n  outputUpper?: boolean;\n  b64Pad?: string;\n  shakeLen?: number;\n  outputLen?: number;\n}): { outputUpper: boolean; b64Pad: string; outputLen: number } {\n  const retVal = { outputUpper: false, b64Pad: \"=\", outputLen: -1 },\n    outputOptions: { outputUpper?: boolean; b64Pad?: string; shakeLen?: number; outputLen?: number } = options || {},\n    lenErrstr = \"Output length must be a multiple of 8\";\n\n  retVal[\"outputUpper\"] = outputOptions[\"outputUpper\"] || false;\n\n  if (outputOptions[\"b64Pad\"]) {\n    retVal[\"b64Pad\"] = outputOptions[\"b64Pad\"];\n  }\n\n  if (outputOptions[\"outputLen\"]) {\n    if (outputOptions[\"outputLen\"] % 8 !== 0) {\n      throw new Error(lenErrstr);\n    }\n    retVal[\"outputLen\"] = outputOptions[\"outputLen\"];\n  } else if (outputOptions[\"shakeLen\"]) {\n    if (outputOptions[\"shakeLen\"] % 8 !== 0) {\n      throw new Error(lenErrstr);\n    }\n    retVal[\"outputLen\"] = outputOptions[\"shakeLen\"];\n  }\n\n  if (\"boolean\" !== typeof retVal[\"outputUpper\"]) {\n    throw new Error(\"Invalid outputUpper formatting option\");\n  }\n\n  if (\"string\" !== typeof retVal[\"b64Pad\"]) {\n    throw new Error(\"Invalid b64Pad formatting option\");\n  }\n\n  return retVal;\n}\n\n/**\n * Parses an external constructor object and returns a packed number, if possible.\n *\n * @param key The human-friendly key name to prefix any errors with\n * @param value The input value object to parse\n * @param bigEndianMod Modifier for whether hash function is big or small endian.\n * @param fallback Fallback value if `value` is undefined.  If not present and `value` is undefined, an Error is thrown\n */\nexport function parseInputOption(\n  key: string,\n  value: GenericInputType | undefined,\n  bigEndianMod: -1 | 1,\n  fallback?: packedValue\n): packedValue {\n  const errStr = key + \" must include a value and format\";\n  if (!value) {\n    if (!fallback) {\n      throw new Error(errStr);\n    }\n    return fallback;\n  }\n\n  if (typeof value[\"value\"] === \"undefined\" || !value[\"format\"]) {\n    throw new Error(errStr);\n  }\n\n  return getStrConverter(\n    value[\"format\"],\n    // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n    // @ts-ignore - the value of encoding gets value checked by getStrConverter\n    value[\"encoding\"] || \"UTF8\",\n    bigEndianMod\n  )(value[\"value\"]);\n}\n\nexport abstract class jsSHABase<StateT, VariantT> {\n  /**\n   * @param variant The desired SHA variant.\n   * @param inputFormat The input format to be used in future `update` calls.\n   * @param options Hashmap of extra input options.\n   */\n  /* Needed inputs */\n  protected readonly shaVariant: VariantT;\n  protected readonly inputFormat: FormatType;\n  protected readonly utfType: EncodingType;\n  protected readonly numRounds: number;\n\n  /* State */\n  protected abstract intermediateState: StateT;\n  protected keyWithIPad: number[];\n  protected keyWithOPad: number[];\n  protected remainder: number[];\n  protected remainderLen: number;\n  protected updateCalled: boolean;\n  protected processedLen: number;\n  protected macKeySet: boolean;\n\n  /* Variant specifics */\n  protected abstract readonly variantBlockSize: number;\n  protected abstract readonly bigEndianMod: -1 | 1;\n  protected abstract readonly outputBinLen: number;\n  protected abstract readonly isVariableLen: boolean;\n  protected abstract readonly HMACSupported: boolean;\n\n  /* Functions */\n  /* eslint-disable-next-line @typescript-eslint/no-explicit-any */\n  protected abstract readonly converterFunc: (input: any, existingBin: number[], existingBinLen: number) => packedValue;\n  protected abstract readonly roundFunc: (block: number[], H: StateT) => StateT;\n  protected abstract readonly finalizeFunc: (\n    remainder: number[],\n    remainderBinLen: number,\n    processedBinLen: number,\n    H: StateT,\n    outputLen: number\n  ) => number[];\n  protected abstract readonly stateCloneFunc: (state: StateT) => StateT;\n  protected abstract readonly newStateFunc: (variant: VariantT) => StateT;\n  protected abstract readonly getMAC: ((options: { outputLen: number }) => number[]) | null;\n\n  protected constructor(variant: VariantT, inputFormat: \"TEXT\", options?: FixedLengthOptionsEncodingType);\n  protected constructor(variant: VariantT, inputFormat: FormatNoTextType, options?: FixedLengthOptionsNoEncodingType);\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  protected constructor(variant: any, inputFormat: any, options?: any) {\n    const inputOptions = options || {};\n    this.inputFormat = inputFormat;\n\n    this.utfType = inputOptions[\"encoding\"] || \"UTF8\";\n    this.numRounds = inputOptions[\"numRounds\"] || 1;\n\n    /* eslint-disable-next-line @typescript-eslint/ban-ts-comment */\n    // @ts-ignore - The spec actually says ToString is called on the first parseInt argument so it's OK to use it here\n    // to check if an arugment is an integer. This cheat would break if it's used to get the value of the argument.\n    if (isNaN(this.numRounds) || this.numRounds !== parseInt(this.numRounds, 10) || 1 > this.numRounds) {\n      throw new Error(\"numRounds must a integer >= 1\");\n    }\n\n    this.shaVariant = variant;\n    this.remainder = [];\n    this.remainderLen = 0;\n    this.updateCalled = false;\n    this.processedLen = 0;\n    this.macKeySet = false;\n    this.keyWithIPad = [];\n    this.keyWithOPad = [];\n  }\n\n  /**\n   * Hashes as many blocks as possible.  Stores the rest for either a future update or getHash call.\n   *\n   * @param srcString The input to be hashed.\n   * @returns A reference to the object.\n   */\n  update(srcString: string | ArrayBuffer | Uint8Array): this {\n    let i,\n      updateProcessedLen = 0;\n    const variantBlockIntInc = this.variantBlockSize >>> 5,\n      convertRet = this.converterFunc(srcString, this.remainder, this.remainderLen),\n      chunkBinLen = convertRet[\"binLen\"],\n      chunk = convertRet[\"value\"],\n      chunkIntLen = chunkBinLen >>> 5;\n\n    for (i = 0; i < chunkIntLen; i += variantBlockIntInc) {\n      if (updateProcessedLen + this.variantBlockSize <= chunkBinLen) {\n        this.intermediateState = this.roundFunc(chunk.slice(i, i + variantBlockIntInc), this.intermediateState);\n        updateProcessedLen += this.variantBlockSize;\n      }\n    }\n    this.processedLen += updateProcessedLen;\n    this.remainder = chunk.slice(updateProcessedLen >>> 5);\n    this.remainderLen = chunkBinLen % this.variantBlockSize;\n    this.updateCalled = true;\n\n    return this;\n  }\n\n  /**\n   * Returns the desired SHA hash of the input fed in via `update` calls.\n   *\n   * @param format The desired output formatting\n   * @param options Hashmap of output formatting options. `outputLen` must be specified for variable length hashes.\n   *   `outputLen` replaces the now deprecated `shakeLen` key.\n   * @returns The hash in the format specified.\n   */\n  getHash(format: \"HEX\", options?: { outputUpper?: boolean; outputLen?: number; shakeLen?: number }): string;\n  getHash(format: \"B64\", options?: { b64Pad?: string; outputLen?: number; shakeLen?: number }): string;\n  getHash(format: \"BYTES\", options?: { outputLen?: number; shakeLen?: number }): string;\n  getHash(format: \"UINT8ARRAY\", options?: { outputLen?: number; shakeLen?: number }): Uint8Array;\n  getHash(format: \"ARRAYBUFFER\", options?: { outputLen?: number; shakeLen?: number }): ArrayBuffer;\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  getHash(format: any, options?: any): any {\n    let i,\n      finalizedState,\n      outputBinLen = this.outputBinLen;\n\n    const outputOptions = getOutputOpts(options);\n\n    if (this.isVariableLen) {\n      if (outputOptions[\"outputLen\"] === -1) {\n        throw new Error(\"Output length must be specified in options\");\n      }\n      outputBinLen = outputOptions[\"outputLen\"];\n    }\n\n    const formatFunc = getOutputConverter(format, outputBinLen, this.bigEndianMod, outputOptions);\n    if (this.macKeySet && this.getMAC) {\n      return formatFunc(this.getMAC(outputOptions));\n    }\n\n    finalizedState = this.finalizeFunc(\n      this.remainder.slice(),\n      this.remainderLen,\n      this.processedLen,\n      this.stateCloneFunc(this.intermediateState),\n      outputBinLen\n    );\n    for (i = 1; i < this.numRounds; i += 1) {\n      /* Need to mask out bits that should be zero due to output not being a multiple of 32 */\n      if (this.isVariableLen && outputBinLen % 32 !== 0) {\n        finalizedState[finalizedState.length - 1] &= 0x00ffffff >>> (24 - (outputBinLen % 32));\n      }\n      finalizedState = this.finalizeFunc(\n        finalizedState,\n        outputBinLen,\n        0,\n        this.newStateFunc(this.shaVariant),\n        outputBinLen\n      );\n    }\n\n    return formatFunc(finalizedState);\n  }\n\n  /**\n   * Sets the HMAC key for an eventual `getHMAC` call.  Must be called immediately after jsSHA object instantiation.\n   *\n   * @param key The key used to calculate the HMAC\n   * @param inputFormat The format of key.\n   * @param options Hashmap of extra input options.\n   */\n  setHMACKey(key: string, inputFormat: \"TEXT\", options?: { encoding?: EncodingType }): void;\n  setHMACKey(key: string, inputFormat: \"B64\" | \"HEX\" | \"BYTES\"): void;\n  setHMACKey(key: ArrayBuffer, inputFormat: \"ARRAYBUFFER\"): void;\n  setHMACKey(key: Uint8Array, inputFormat: \"UINT8ARRAY\"): void;\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  setHMACKey(key: any, inputFormat: any, options?: any): void {\n    if (!this.HMACSupported) {\n      throw new Error(\"Variant does not support HMAC\");\n    }\n\n    if (this.updateCalled) {\n      throw new Error(\"Cannot set MAC key after calling update\");\n    }\n\n    const keyOptions = options || {},\n      keyConverterFunc = getStrConverter(inputFormat, keyOptions[\"encoding\"] || \"UTF8\", this.bigEndianMod);\n\n    this._setHMACKey(keyConverterFunc(key));\n  }\n\n  /**\n   * Internal function that sets the MAC key.\n   *\n   * @param key The packed MAC key to use\n   */\n  protected _setHMACKey(key: packedValue): void {\n    const blockByteSize = this.variantBlockSize >>> 3,\n      lastArrayIndex = blockByteSize / 4 - 1;\n    let i;\n    if (this.numRounds !== 1) {\n      throw new Error(mac_rounds_error);\n    }\n\n    if (this.macKeySet) {\n      throw new Error(\"MAC key already set\");\n    }\n\n    /* Figure out what to do with the key based on its size relative to\n     * the hash's block size */\n    if (blockByteSize < key[\"binLen\"] / 8) {\n      key[\"value\"] = this.finalizeFunc(\n        key[\"value\"],\n        key[\"binLen\"],\n        0,\n        this.newStateFunc(this.shaVariant),\n        this.outputBinLen\n      );\n    }\n    while (key[\"value\"].length <= lastArrayIndex) {\n      key[\"value\"].push(0);\n    }\n    /* Create ipad and opad */\n    for (i = 0; i <= lastArrayIndex; i += 1) {\n      this.keyWithIPad[i] = key[\"value\"][i] ^ 0x36363636;\n      this.keyWithOPad[i] = key[\"value\"][i] ^ 0x5c5c5c5c;\n    }\n\n    this.intermediateState = this.roundFunc(this.keyWithIPad, this.intermediateState);\n    this.processedLen = this.variantBlockSize;\n\n    this.macKeySet = true;\n  }\n\n  /**\n   * Returns the the HMAC in the specified format using the key given by a previous `setHMACKey` call.\n   *\n   * @param format The desired output formatting.\n   * @param options Hashmap of extra outputs options.\n   * @returns The HMAC in the format specified.\n   */\n  getHMAC(format: \"HEX\", options?: { outputUpper?: boolean }): string;\n  getHMAC(format: \"B64\", options?: { b64Pad?: string }): string;\n  getHMAC(format: \"BYTES\"): string;\n  getHMAC(format: \"UINT8ARRAY\"): Uint8Array;\n  getHMAC(format: \"ARRAYBUFFER\"): ArrayBuffer;\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  getHMAC(format: any, options?: any): any {\n    const outputOptions = getOutputOpts(options),\n      formatFunc = getOutputConverter(format, this.outputBinLen, this.bigEndianMod, outputOptions);\n\n    return formatFunc(this._getHMAC());\n  }\n\n  /**\n   * Internal function that returns the \"raw\" HMAC\n   */\n  protected _getHMAC(): number[] {\n    let finalizedState;\n\n    if (!this.macKeySet) {\n      throw new Error(\"Cannot call getHMAC without first setting MAC key\");\n    }\n\n    const firstHash = this.finalizeFunc(\n      this.remainder.slice(),\n      this.remainderLen,\n      this.processedLen,\n      this.stateCloneFunc(this.intermediateState),\n      this.outputBinLen\n    );\n    finalizedState = this.roundFunc(this.keyWithOPad, this.newStateFunc(this.shaVariant));\n    finalizedState = this.finalizeFunc(\n      firstHash,\n      this.outputBinLen,\n      this.variantBlockSize,\n      finalizedState,\n      this.outputBinLen\n    );\n\n    return finalizedState;\n  }\n}\n", "/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise, SuppressedError, Symbol */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nexport function __extends(d, b) {\r\n    if (typeof b !== \"function\" && b !== null)\r\n        throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    }\r\n    return __assign.apply(this, arguments);\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __esDecorate(ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {\r\n    function accept(f) { if (f !== void 0 && typeof f !== \"function\") throw new TypeError(\"Function expected\"); return f; }\r\n    var kind = contextIn.kind, key = kind === \"getter\" ? \"get\" : kind === \"setter\" ? \"set\" : \"value\";\r\n    var target = !descriptorIn && ctor ? contextIn[\"static\"] ? ctor : ctor.prototype : null;\r\n    var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});\r\n    var _, done = false;\r\n    for (var i = decorators.length - 1; i >= 0; i--) {\r\n        var context = {};\r\n        for (var p in contextIn) context[p] = p === \"access\" ? {} : contextIn[p];\r\n        for (var p in contextIn.access) context.access[p] = contextIn.access[p];\r\n        context.addInitializer = function (f) { if (done) throw new TypeError(\"Cannot add initializers after decoration has completed\"); extraInitializers.push(accept(f || null)); };\r\n        var result = (0, decorators[i])(kind === \"accessor\" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);\r\n        if (kind === \"accessor\") {\r\n            if (result === void 0) continue;\r\n            if (result === null || typeof result !== \"object\") throw new TypeError(\"Object expected\");\r\n            if (_ = accept(result.get)) descriptor.get = _;\r\n            if (_ = accept(result.set)) descriptor.set = _;\r\n            if (_ = accept(result.init)) initializers.unshift(_);\r\n        }\r\n        else if (_ = accept(result)) {\r\n            if (kind === \"field\") initializers.unshift(_);\r\n            else descriptor[key] = _;\r\n        }\r\n    }\r\n    if (target) Object.defineProperty(target, contextIn.name, descriptor);\r\n    done = true;\r\n};\r\n\r\nexport function __runInitializers(thisArg, initializers, value) {\r\n    var useValue = arguments.length > 2;\r\n    for (var i = 0; i < initializers.length; i++) {\r\n        value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);\r\n    }\r\n    return useValue ? value : void 0;\r\n};\r\n\r\nexport function __propKey(x) {\r\n    return typeof x === \"symbol\" ? x : \"\".concat(x);\r\n};\r\n\r\nexport function __setFunctionName(f, name, prefix) {\r\n    if (typeof name === \"symbol\") name = name.description ? \"[\".concat(name.description, \"]\") : \"\";\r\n    return Object.defineProperty(f, \"name\", { configurable: true, value: prefix ? \"\".concat(prefix, \" \", name) : name });\r\n};\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (g && (g = 0, op[0] && (_ = 0)), _) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport var __createBinding = Object.create ? (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    var desc = Object.getOwnPropertyDescriptor(m, k);\r\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\r\n        desc = { enumerable: true, get: function() { return m[k]; } };\r\n    }\r\n    Object.defineProperty(o, k2, desc);\r\n}) : (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n});\r\n\r\nexport function __exportStar(m, o) {\r\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(o, p)) __createBinding(o, m, p);\r\n}\r\n\r\nexport function __values(o) {\r\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\r\n    if (m) return m.call(o);\r\n    if (o && typeof o.length === \"number\") return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\n/** @deprecated */\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\n/** @deprecated */\r\nexport function __spreadArrays() {\r\n    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\r\n    for (var r = Array(s), k = 0, i = 0; i < il; i++)\r\n        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\r\n            r[k] = a[j];\r\n    return r;\r\n}\r\n\r\nexport function __spreadArray(to, from, pack) {\r\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\r\n        if (ar || !(i in from)) {\r\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\r\n            ar[i] = from[i];\r\n        }\r\n    }\r\n    return to.concat(ar || Array.prototype.slice.call(from));\r\n}\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: false } : f ? f(v) : v; } : f; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator], i;\r\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\r\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\r\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\r\n}\r\n\r\nexport function __makeTemplateObject(cooked, raw) {\r\n    if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n    return cooked;\r\n};\r\n\r\nvar __setModuleDefault = Object.create ? (function(o, v) {\r\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\r\n}) : function(o, v) {\r\n    o[\"default\"] = v;\r\n};\r\n\r\nexport function __importStar(mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\r\n    __setModuleDefault(result, mod);\r\n    return result;\r\n}\r\n\r\nexport function __importDefault(mod) {\r\n    return (mod && mod.__esModule) ? mod : { default: mod };\r\n}\r\n\r\nexport function __classPrivateFieldGet(receiver, state, kind, f) {\r\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\r\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\r\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\r\n}\r\n\r\nexport function __classPrivateFieldSet(receiver, state, value, kind, f) {\r\n    if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\r\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\r\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\r\n    return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\r\n}\r\n\r\nexport function __classPrivateFieldIn(state, receiver) {\r\n    if (receiver === null || (typeof receiver !== \"object\" && typeof receiver !== \"function\")) throw new TypeError(\"Cannot use 'in' operator on non-object\");\r\n    return typeof state === \"function\" ? receiver === state : state.has(receiver);\r\n}\r\n\r\nexport function __addDisposableResource(env, value, async) {\r\n    if (value !== null && value !== void 0) {\r\n        if (typeof value !== \"object\" && typeof value !== \"function\") throw new TypeError(\"Object expected.\");\r\n        var dispose;\r\n        if (async) {\r\n            if (!Symbol.asyncDispose) throw new TypeError(\"Symbol.asyncDispose is not defined.\");\r\n            dispose = value[Symbol.asyncDispose];\r\n        }\r\n        if (dispose === void 0) {\r\n            if (!Symbol.dispose) throw new TypeError(\"Symbol.dispose is not defined.\");\r\n            dispose = value[Symbol.dispose];\r\n        }\r\n        if (typeof dispose !== \"function\") throw new TypeError(\"Object not disposable.\");\r\n        env.stack.push({ value: value, dispose: dispose, async: async });\r\n    }\r\n    else if (async) {\r\n        env.stack.push({ async: true });\r\n    }\r\n    return value;\r\n}\r\n\r\nvar _SuppressedError = typeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\r\n    var e = new Error(message);\r\n    return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\r\n};\r\n\r\nexport function __disposeResources(env) {\r\n    function fail(e) {\r\n        env.error = env.hasError ? new _SuppressedError(e, env.error, \"An error was suppressed during disposal.\") : e;\r\n        env.hasError = true;\r\n    }\r\n    function next() {\r\n        while (env.stack.length) {\r\n            var rec = env.stack.pop();\r\n            try {\r\n                var result = rec.dispose && rec.dispose.call(rec.value);\r\n                if (rec.async) return Promise.resolve(result).then(next, function(e) { fail(e); return next(); });\r\n            }\r\n            catch (e) {\r\n                fail(e);\r\n            }\r\n        }\r\n        if (env.hasError) throw env.error;\r\n    }\r\n    return next();\r\n}\r\n\r\nexport default {\r\n    __extends: __extends,\r\n    __assign: __assign,\r\n    __rest: __rest,\r\n    __decorate: __decorate,\r\n    __param: __param,\r\n    __metadata: __metadata,\r\n    __awaiter: __awaiter,\r\n    __generator: __generator,\r\n    __createBinding: __createBinding,\r\n    __exportStar: __exportStar,\r\n    __values: __values,\r\n    __read: __read,\r\n    __spread: __spread,\r\n    __spreadArrays: __spreadArrays,\r\n    __spreadArray: __spreadArray,\r\n    __await: __await,\r\n    __asyncGenerator: __asyncGenerator,\r\n    __asyncDelegator: __asyncDelegator,\r\n    __asyncValues: __asyncValues,\r\n    __makeTemplateObject: __makeTemplateObject,\r\n    __importStar: __importStar,\r\n    __importDefault: __importDefault,\r\n    __classPrivateFieldGet: __classPrivateFieldGet,\r\n    __classPrivateFieldSet: __classPrivateFieldSet,\r\n    __classPrivateFieldIn: __classPrivateFieldIn,\r\n    __addDisposableResource: __addDisposableResource,\r\n    __disposeResources: __disposeResources,\r\n};\r\n", "/*\n * Note 1: All the functions in this file guarantee only that the bottom 32-bits of the return value are correct.\n * JavaScript is flakey when it comes to bit operations and a '1' in the highest order bit of a 32-bit number causes\n * it to be interpreted as a negative number per two's complement.\n *\n * Note 2: Per the ECMAScript spec, all JavaScript operations mask the shift amount by 0x1F.  This results in weird\n * cases like 1 << 32 == 1 and 1 << 33 === 1 << 1 === 2\n */\n\n/**\n * The 32-bit implementation of circular rotate left.\n *\n * @param x The 32-bit integer argument.\n * @param n The number of bits to shift.\n * @returns `x` shifted left circularly by `n` bits\n */\nexport function rotl_32(x: number, n: number): number {\n  return (x << n) | (x >>> (32 - n));\n}\n\n/**\n * The 32-bit implementation of circular rotate right.\n *\n * @param x The 32-bit integer argument.\n * @param n The number of bits to shift.\n * @returns `x` shifted right circularly by `n` bits\n */\nfunction rotr_32(x: number, n: number): number {\n  return (x >>> n) | (x << (32 - n));\n}\n\n/**\n * The 32-bit implementation of shift right.\n *\n * @param x The 32-bit integer argument.\n * @param n The number of bits to shift.\n * @returns `x` shifted by `n` bits.\n */\nfunction shr_32(x: number, n: number): number {\n  return x >>> n;\n}\n\n/**\n * The 32-bit implementation of the NIST specified Parity function.\n *\n * @param x The first 32-bit integer argument.\n * @param y The second 32-bit integer argument.\n * @param z The third 32-bit integer argument.\n * @returns The NIST specified output of the function.\n */\nexport function parity_32(x: number, y: number, z: number): number {\n  return x ^ y ^ z;\n}\n\n/**\n * The 32-bit implementation of the NIST specified Ch function.\n *\n * @param x The first 32-bit integer argument.\n * @param y The second 32-bit integer argument.\n * @param z The third 32-bit integer argument.\n * @returns The NIST specified output of the function.\n */\nexport function ch_32(x: number, y: number, z: number): number {\n  return (x & y) ^ (~x & z);\n}\n\n/**\n * The 32-bit implementation of the NIST specified Maj function.\n *\n * @param x The first 32-bit integer argument.\n * @param y The second 32-bit integer argument.\n * @param z The third 32-bit integer argument.\n * @returns The NIST specified output of the function.\n */\nexport function maj_32(x: number, y: number, z: number): number {\n  return (x & y) ^ (x & z) ^ (y & z);\n}\n\n/**\n * The 32-bit implementation of the NIST specified Sigma0 function.\n *\n * @param x The 32-bit integer argument.\n * @returns The NIST specified output of the function.\n */\nexport function sigma0_32(x: number): number {\n  return rotr_32(x, 2) ^ rotr_32(x, 13) ^ rotr_32(x, 22);\n}\n\n/**\n * Add two 32-bit integers.\n *\n * This uses 16-bit operations internally to work around sign problems due to JavaScript's lack of uint32 support.\n *\n * @param a The first 32-bit integer argument to be added.\n * @param b The second 32-bit integer argument to be added.\n * @returns The sum of `a` + `b`.\n */\nexport function safeAdd_32_2(a: number, b: number): number {\n  const lsw = (a & 0xffff) + (b & 0xffff),\n    msw = (a >>> 16) + (b >>> 16) + (lsw >>> 16);\n\n  return ((msw & 0xffff) << 16) | (lsw & 0xffff);\n}\n\n/**\n * Add four 32-bit integers.\n *\n * This uses 16-bit operations internally to work around sign problems due to JavaScript's lack of uint32 support.\n *\n * @param a The first 32-bit integer argument to be added.\n * @param b The second 32-bit integer argument to be added.\n * @param c The third 32-bit integer argument to be added.\n * @param d The fourth 32-bit integer argument to be added.\n * @returns The sum of `a` + `b` + `c` + `d`.\n */\nexport function safeAdd_32_4(a: number, b: number, c: number, d: number): number {\n  const lsw = (a & 0xffff) + (b & 0xffff) + (c & 0xffff) + (d & 0xffff),\n    msw = (a >>> 16) + (b >>> 16) + (c >>> 16) + (d >>> 16) + (lsw >>> 16);\n\n  return ((msw & 0xffff) << 16) | (lsw & 0xffff);\n}\n\n/**\n * Add five 32-bit integers.\n *\n * This uses 16-bit operations internally to work around sign problems due to JavaScript's lack of uint32 support.\n *\n * @param a The first 32-bit integer argument to be added.\n * @param b The second 32-bit integer argument to be added.\n * @param c The third 32-bit integer argument to be added.\n * @param d The fourth 32-bit integer argument to be added.\n * @param e The fifth 32-bit integer argument to be added.\n * @returns The sum of `a` + `b` + `c` + `d` + `e`.\n */\nexport function safeAdd_32_5(a: number, b: number, c: number, d: number, e: number): number {\n  const lsw = (a & 0xffff) + (b & 0xffff) + (c & 0xffff) + (d & 0xffff) + (e & 0xffff),\n    msw = (a >>> 16) + (b >>> 16) + (c >>> 16) + (d >>> 16) + (e >>> 16) + (lsw >>> 16);\n\n  return ((msw & 0xffff) << 16) | (lsw & 0xffff);\n}\n\n/**\n * The 32-bit implementation of the NIST specified Gamma1 function.\n *\n * @param x The 32-bit integer argument.\n * @returns The NIST specified output of the function.\n */\nexport function gamma1_32(x: number): number {\n  return rotr_32(x, 17) ^ rotr_32(x, 19) ^ shr_32(x, 10);\n}\n\n/**\n * The 32-bit implementation of the NIST specified Gamma0 function.\n *\n * @param x The 32-bit integer argument.\n * @returns The NIST specified output of the function.\n */\nexport function gamma0_32(x: number): number {\n  return rotr_32(x, 7) ^ rotr_32(x, 18) ^ shr_32(x, 3);\n}\n\n/**\n * The 32-bit implementation of the NIST specified Sigma1 function.\n *\n * @param x The 32-bit integer argument.\n * @returns The NIST specified output of the function.\n */\nexport function sigma1_32(x: number): number {\n  return rotr_32(x, 6) ^ rotr_32(x, 11) ^ rotr_32(x, 25);\n}\n", "import { jsSHABase, TWO_PWR_32, sha_variant_error, parseInputOption } from \"./common\";\nimport {\n  packedValue,\n  FixedLengthOptionsEncodingType,\n  FixedLengthOptionsNoEncodingType,\n  FormatNoTextType,\n} from \"./custom_types\";\nimport { getStrConverter } from \"./converters\";\nimport { ch_32, parity_32, maj_32, rotl_32, safeAdd_32_2, safeAdd_32_5 } from \"./primitives_32\";\n\n/**\n * Gets the state values for the specified SHA variant.\n *\n * @param _variant: Unused\n * @returns The initial state values.\n */\nfunction getNewState(_variant: \"SHA-1\"): number[] {\n  return [0x67452301, 0xefcdab89, 0x98badcfe, 0x10325476, 0xc3d2e1f0];\n}\n\n/**\n * Performs a round of SHA-1 hashing over a 512-byte block.  This clobbers `H`.\n *\n * @param block The binary array representation of the block to hash.\n * @param H The intermediate H values from a previous round.\n * @returns The resulting H values.\n */\nfunction roundSHA1(block: number[], H: number[]): number[] {\n  let a, b, c, d, e, T, t;\n  const W: number[] = [];\n\n  a = H[0];\n  b = H[1];\n  c = H[2];\n  d = H[3];\n  e = H[4];\n\n  for (t = 0; t < 80; t += 1) {\n    if (t < 16) {\n      W[t] = block[t];\n    } else {\n      W[t] = rotl_32(W[t - 3] ^ W[t - 8] ^ W[t - 14] ^ W[t - 16], 1);\n    }\n\n    if (t < 20) {\n      T = safeAdd_32_5(rotl_32(a, 5), ch_32(b, c, d), e, 0x5a827999, W[t]);\n    } else if (t < 40) {\n      T = safeAdd_32_5(rotl_32(a, 5), parity_32(b, c, d), e, 0x6ed9eba1, W[t]);\n    } else if (t < 60) {\n      T = safeAdd_32_5(rotl_32(a, 5), maj_32(b, c, d), e, 0x8f1bbcdc, W[t]);\n    } else {\n      T = safeAdd_32_5(rotl_32(a, 5), parity_32(b, c, d), e, 0xca62c1d6, W[t]);\n    }\n\n    e = d;\n    d = c;\n    c = rotl_32(b, 30);\n    b = a;\n    a = T;\n  }\n\n  H[0] = safeAdd_32_2(a, H[0]);\n  H[1] = safeAdd_32_2(b, H[1]);\n  H[2] = safeAdd_32_2(c, H[2]);\n  H[3] = safeAdd_32_2(d, H[3]);\n  H[4] = safeAdd_32_2(e, H[4]);\n\n  return H;\n}\n\n/**\n * Finalizes the SHA-1 hash.  This clobbers `remainder` and `H`.\n *\n * @param remainder Any leftover unprocessed packed ints that still need to be processed.\n * @param remainderBinLen The number of bits in `remainder`.\n * @param processedBinLen The number of bits already processed.\n * @param H The intermediate H values from a previous round.\n * @returns The array of integers representing the SHA-1 hash of message.\n */\nfunction finalizeSHA1(remainder: number[], remainderBinLen: number, processedBinLen: number, H: number[]): number[] {\n  let i;\n\n  /* The 65 addition is a hack but it works.  The correct number is\n\t\tactually 72 (64 + 8) but the below math fails if\n\t\tremainderBinLen + 72 % 512 = 0. Since remainderBinLen % 8 = 0,\n\t\t\"shorting\" the addition is OK. */\n  const offset = (((remainderBinLen + 65) >>> 9) << 4) + 15,\n    totalLen = remainderBinLen + processedBinLen;\n  while (remainder.length <= offset) {\n    remainder.push(0);\n  }\n  /* Append '1' at the end of the binary string */\n  remainder[remainderBinLen >>> 5] |= 0x80 << (24 - (remainderBinLen % 32));\n\n  /* Append length of binary string in the position such that the new\n   * length is a multiple of 512.  Logic does not work for even multiples\n   * of 512 but there can never be even multiples of 512. JavaScript\n   * numbers are limited to 2^53 so it's \"safe\" to treat the totalLen as\n   * a 64-bit integer. */\n  remainder[offset] = totalLen & 0xffffffff;\n\n  /* Bitwise operators treat the operand as a 32-bit number so need to\n   * use hacky division and round to get access to upper 32-ish bits */\n  remainder[offset - 1] = (totalLen / TWO_PWR_32) | 0;\n\n  /* This will always be at least 1 full chunk */\n  for (i = 0; i < remainder.length; i += 16) {\n    H = roundSHA1(remainder.slice(i, i + 16), H);\n  }\n\n  return H;\n}\n\nexport default class jsSHA extends jsSHABase<number[], \"SHA-1\"> {\n  intermediateState: number[];\n  variantBlockSize: number;\n  bigEndianMod: -1 | 1;\n  outputBinLen: number;\n  isVariableLen: boolean;\n  HMACSupported: boolean;\n\n  /* eslint-disable-next-line @typescript-eslint/no-explicit-any */\n  converterFunc: (input: any, existingBin: number[], existingBinLen: number) => packedValue;\n  roundFunc: (block: number[], H: number[]) => number[];\n  finalizeFunc: (remainder: number[], remainderBinLen: number, processedBinLen: number, H: number[]) => number[];\n  stateCloneFunc: (state: number[]) => number[];\n  newStateFunc: (variant: \"SHA-1\") => number[];\n  getMAC: () => number[];\n\n  constructor(variant: \"SHA-1\", inputFormat: \"TEXT\", options?: FixedLengthOptionsEncodingType);\n  constructor(variant: \"SHA-1\", inputFormat: FormatNoTextType, options?: FixedLengthOptionsNoEncodingType);\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  constructor(variant: any, inputFormat: any, options?: any) {\n    if (\"SHA-1\" !== variant) {\n      throw new Error(sha_variant_error);\n    }\n    super(variant, inputFormat, options);\n    const resolvedOptions = options || {};\n\n    this.HMACSupported = true;\n    // eslint-disable-next-line @typescript-eslint/unbound-method\n    this.getMAC = this._getHMAC;\n    this.bigEndianMod = -1;\n    this.converterFunc = getStrConverter(this.inputFormat, this.utfType, this.bigEndianMod);\n    this.roundFunc = roundSHA1;\n    this.stateCloneFunc = function (state: number[]): number[] {\n      return state.slice();\n    };\n    this.newStateFunc = getNewState;\n    this.finalizeFunc = finalizeSHA1;\n\n    this.intermediateState = getNewState(variant);\n    this.variantBlockSize = 512;\n    this.outputBinLen = 160;\n    this.isVariableLen = false;\n\n    if (resolvedOptions[\"hmacKey\"]) {\n      this._setHMACKey(parseInputOption(\"hmacKey\", resolvedOptions[\"hmacKey\"], this.bigEndianMod));\n    }\n  }\n}\n", "import { jsS<PERSON><PERSON>ase, TWO_PWR_32, H_full, H_trunc, K_sha2, sha_variant_error, parseInputOption } from \"./common\";\nimport {\n  packedValue,\n  FixedLengthOptionsEncodingType,\n  FixedLengthOptionsNoEncodingType,\n  FormatNoTextType,\n} from \"./custom_types\";\nimport { getStrConverter } from \"./converters\";\nimport {\n  ch_32,\n  gamma0_32,\n  gamma1_32,\n  maj_32,\n  safeAdd_32_2,\n  safeAdd_32_4,\n  safeAdd_32_5,\n  sigma0_32,\n  sigma1_32,\n} from \"./primitives_32\";\n\ntype VariantType = \"SHA-224\" | \"SHA-256\";\n\n/**\n * Gets the state values for the specified SHA variant.\n *\n * @param variant: The SHA-256 family variant.\n * @returns The initial state values.\n */\nfunction getNewState256(variant: VariantType): number[] {\n  let retVal;\n\n  if (\"SHA-224\" == variant) {\n    retVal = H_trunc.slice();\n  } else {\n    /* \"SHA-256\" */\n    retVal = H_full.slice();\n  }\n  return retVal;\n}\n\n/**\n * Performs a round of SHA-256 hashing over a block. This clobbers `H`.\n *\n * @param block The binary array representation of the block to hash.\n * @param H The intermediate H values from a previous round.\n * @returns The resulting H values.\n */\nfunction roundSHA256(block: number[], H: number[]): number[] {\n  let a, b, c, d, e, f, g, h, T1, T2, t;\n\n  const W: number[] = [];\n\n  a = H[0];\n  b = H[1];\n  c = H[2];\n  d = H[3];\n  e = H[4];\n  f = H[5];\n  g = H[6];\n  h = H[7];\n\n  for (t = 0; t < 64; t += 1) {\n    if (t < 16) {\n      W[t] = block[t];\n    } else {\n      W[t] = safeAdd_32_4(gamma1_32(W[t - 2]), W[t - 7], gamma0_32(W[t - 15]), W[t - 16]);\n    }\n    T1 = safeAdd_32_5(h, sigma1_32(e), ch_32(e, f, g), K_sha2[t], W[t]);\n    T2 = safeAdd_32_2(sigma0_32(a), maj_32(a, b, c));\n    h = g;\n    g = f;\n    f = e;\n    e = safeAdd_32_2(d, T1);\n    d = c;\n    c = b;\n    b = a;\n    a = safeAdd_32_2(T1, T2);\n  }\n\n  H[0] = safeAdd_32_2(a, H[0]);\n  H[1] = safeAdd_32_2(b, H[1]);\n  H[2] = safeAdd_32_2(c, H[2]);\n  H[3] = safeAdd_32_2(d, H[3]);\n  H[4] = safeAdd_32_2(e, H[4]);\n  H[5] = safeAdd_32_2(f, H[5]);\n  H[6] = safeAdd_32_2(g, H[6]);\n  H[7] = safeAdd_32_2(h, H[7]);\n\n  return H;\n}\n\n/**\n * Finalizes the SHA-256 hash. This clobbers `remainder` and `H`.\n *\n * @param remainder Any leftover unprocessed packed ints that still need to be processed.\n * @param remainderBinLen The number of bits in `remainder`.\n * @param processedBinLen The number of bits already processed.\n * @param H The intermediate H values from a previous round.\n * @param variant The desired SHA-256 variant.\n * @returns The array of integers representing the SHA-2 hash of message.\n */\nfunction finalizeSHA256(\n  remainder: number[],\n  remainderBinLen: number,\n  processedBinLen: number,\n  H: number[],\n  variant: VariantType\n): number[] {\n  let i, retVal;\n\n  /* The 65 addition is a hack but it works.  The correct number is\n    actually 72 (64 + 8) but the below math fails if\n    remainderBinLen + 72 % 512 = 0. Since remainderBinLen % 8 = 0,\n    \"shorting\" the addition is OK. */\n  const offset = (((remainderBinLen + 65) >>> 9) << 4) + 15,\n    binaryStringInc = 16,\n    totalLen = remainderBinLen + processedBinLen;\n\n  while (remainder.length <= offset) {\n    remainder.push(0);\n  }\n  /* Append '1' at the end of the binary string */\n  remainder[remainderBinLen >>> 5] |= 0x80 << (24 - (remainderBinLen % 32));\n  /* Append length of binary string in the position such that the new\n   * length is correct. JavaScript numbers are limited to 2^53 so it's\n   * \"safe\" to treat the totalLen as a 64-bit integer. */\n\n  remainder[offset] = totalLen & 0xffffffff;\n  /* Bitwise operators treat the operand as a 32-bit number so need to\n   * use hacky division and round to get access to upper 32-ish bits */\n  remainder[offset - 1] = (totalLen / TWO_PWR_32) | 0;\n\n  /* This will always be at least 1 full chunk */\n  for (i = 0; i < remainder.length; i += binaryStringInc) {\n    H = roundSHA256(remainder.slice(i, i + binaryStringInc), H);\n  }\n\n  if (\"SHA-224\" === variant) {\n    retVal = [H[0], H[1], H[2], H[3], H[4], H[5], H[6]];\n  } else {\n    /* \"SHA-256 */\n    retVal = H;\n  }\n\n  return retVal;\n}\nexport default class jsSHA extends jsSHABase<number[], VariantType> {\n  intermediateState: number[];\n  variantBlockSize: number;\n  bigEndianMod: -1 | 1;\n  outputBinLen: number;\n  isVariableLen: boolean;\n  HMACSupported: boolean;\n\n  /* eslint-disable-next-line @typescript-eslint/no-explicit-any */\n  converterFunc: (input: any, existingBin: number[], existingBinLen: number) => packedValue;\n  roundFunc: (block: number[], H: number[]) => number[];\n  finalizeFunc: (remainder: number[], remainderBinLen: number, processedBinLen: number, H: number[]) => number[];\n  stateCloneFunc: (state: number[]) => number[];\n  newStateFunc: (variant: VariantType) => number[];\n  getMAC: () => number[];\n\n  constructor(variant: VariantType, inputFormat: \"TEXT\", options?: FixedLengthOptionsEncodingType);\n  constructor(variant: VariantType, inputFormat: FormatNoTextType, options?: FixedLengthOptionsNoEncodingType);\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  constructor(variant: any, inputFormat: any, options?: any) {\n    if (!(\"SHA-224\" === variant || \"SHA-256\" === variant)) {\n      throw new Error(sha_variant_error);\n    }\n    super(variant, inputFormat, options);\n    const resolvedOptions = options || {};\n\n    // eslint-disable-next-line @typescript-eslint/unbound-method\n    this.getMAC = this._getHMAC;\n    this.HMACSupported = true;\n    this.bigEndianMod = -1;\n    this.converterFunc = getStrConverter(this.inputFormat, this.utfType, this.bigEndianMod);\n    this.roundFunc = roundSHA256;\n    this.stateCloneFunc = function (state): number[] {\n      return state.slice();\n    };\n\n    this.newStateFunc = getNewState256;\n    this.finalizeFunc = function (remainder, remainderBinLen, processedBinLen, H): number[] {\n      return finalizeSHA256(remainder, remainderBinLen, processedBinLen, H, variant);\n    };\n\n    this.intermediateState = getNewState256(variant);\n    this.variantBlockSize = 512;\n    this.outputBinLen = \"SHA-224\" === variant ? 224 : 256;\n    this.isVariableLen = false;\n\n    if (resolvedOptions[\"hmacKey\"]) {\n      this._setHMACKey(parseInputOption(\"hmacKey\", resolvedOptions[\"hmacKey\"], this.bigEndianMod));\n    }\n  }\n}\n", "/*\n * Note 1: All the functions in this file guarantee only that the bottom 32-bits of the returned Int_64 are correct.\n * JavaScript is flakey when it comes to bit operations and a '1' in the highest order bit of a 32-bit number causes\n * it to be interpreted as a negative number per two's complement.\n *\n * Note 2: Per the ECMAScript spec, all JavaScript operations mask the shift amount by 0x1F.  This results in weird\n * cases like 1 << 32 == 1 and 1 << 33 === 1 << 1 === 2\n */\n\n/**\n * Int_64 is a object for 2 32-bit numbers emulating a 64-bit number.\n */\nexport class Int_64 {\n  /**\n   * @param msint_32 The most significant 32-bits of a 64-bit number.\n   * @param lsint_32 The least significant 32-bits of a 64-bit number.\n   */\n  readonly highOrder: number;\n  readonly lowOrder: number;\n  constructor(msint_32: number, lsint_32: number) {\n    this.highOrder = msint_32;\n    this.lowOrder = lsint_32;\n  }\n}\n\n/**\n * The 64-bit implementation of circular rotate left.\n *\n * This does not work for n >= 64 or n == 32 but those are never done.\n *\n * @param x The 64-bit integer argument.\n * @param n The number of bits to shift.\n * @returns `x` shifted left circularly by `n` bits.\n */\nexport function rotl_64(x: Int_64, n: number): Int_64 {\n  let tmp;\n  if (n > 32) {\n    tmp = 64 - n;\n    return new Int_64((x.lowOrder << n) | (x.highOrder >>> tmp), (x.highOrder << n) | (x.lowOrder >>> tmp));\n  } else if (0 !== n) {\n    tmp = 32 - n;\n    return new Int_64((x.highOrder << n) | (x.lowOrder >>> tmp), (x.lowOrder << n) | (x.highOrder >>> tmp));\n  } else {\n    return x;\n  }\n}\n\n/**\n * The 64-bit implementation of circular rotate right.\n *\n * This does not work for n >= 64, n == 32, or n == 0 but those are never done.\n *\n * @param x The 64-bit integer argument.\n * @param n The number of bits to shift.\n * @returns `x` shifted right circularly by `n` bits.\n */\nfunction rotr_64(x: Int_64, n: number): Int_64 {\n  let tmp;\n  if (n < 32) {\n    tmp = 32 - n;\n    return new Int_64((x.highOrder >>> n) | (x.lowOrder << tmp), (x.lowOrder >>> n) | (x.highOrder << tmp));\n  } else {\n    tmp = 64 - n;\n    return new Int_64((x.lowOrder >>> n) | (x.highOrder << tmp), (x.highOrder >>> n) | (x.lowOrder << tmp));\n  }\n}\n\n/**\n * The 64-bit implementation of shift right.\n *\n * This does not work for n >= 32 but is only called for n < 32.\n *\n * @param x The 64-bit integer argument.\n * @param n The number of bits to shift.\n * @returns `x` shifted right by `n` bits\n */\nfunction shr_64(x: Int_64, n: number): Int_64 {\n  return new Int_64(x.highOrder >>> n, (x.lowOrder >>> n) | (x.highOrder << (32 - n)));\n}\n\n/**\n * The 64-bit implementation of the NIST specified Ch function.\n *\n * @param x The first 64-bit integer argument.\n * @param y The second 64-bit integer argument.\n * @param z The third 64-bit integer argument.\n * @returns The NIST specified output of the function.\n */\nexport function ch_64(x: Int_64, y: Int_64, z: Int_64): Int_64 {\n  return new Int_64(\n    (x.highOrder & y.highOrder) ^ (~x.highOrder & z.highOrder),\n    (x.lowOrder & y.lowOrder) ^ (~x.lowOrder & z.lowOrder)\n  );\n}\n\n/**\n * The 64-bit implementation of the NIST specified Maj function.\n *\n * @param x The first 64-bit integer argument.\n * @param y The second 64-bit integer argument.\n * @param z The third 64-bit integer argument.\n * @returns The NIST specified output of the function.\n */\nexport function maj_64(x: Int_64, y: Int_64, z: Int_64): Int_64 {\n  return new Int_64(\n    (x.highOrder & y.highOrder) ^ (x.highOrder & z.highOrder) ^ (y.highOrder & z.highOrder),\n    (x.lowOrder & y.lowOrder) ^ (x.lowOrder & z.lowOrder) ^ (y.lowOrder & z.lowOrder)\n  );\n}\n\n/**\n * The 64-bit implementation of the NIST specified Sigma0 function.\n *\n * @param x The 64-bit integer argument.\n * @returns The NIST specified output of the function.\n */\nexport function sigma0_64(x: Int_64): Int_64 {\n  const rotr28 = rotr_64(x, 28),\n    rotr34 = rotr_64(x, 34),\n    rotr39 = rotr_64(x, 39);\n\n  return new Int_64(\n    rotr28.highOrder ^ rotr34.highOrder ^ rotr39.highOrder,\n    rotr28.lowOrder ^ rotr34.lowOrder ^ rotr39.lowOrder\n  );\n}\n\n/**\n * Add two 64-bit integers.\n *\n * @param x The first 64-bit integer argument to be added.\n * @param y The second 64-bit integer argument to be added.\n * @returns The sum of `x` + `y`.\n */\nexport function safeAdd_64_2(x: Int_64, y: Int_64): Int_64 {\n  let lsw, msw;\n\n  lsw = (x.lowOrder & 0xffff) + (y.lowOrder & 0xffff);\n  msw = (x.lowOrder >>> 16) + (y.lowOrder >>> 16) + (lsw >>> 16);\n  const lowOrder = ((msw & 0xffff) << 16) | (lsw & 0xffff);\n\n  lsw = (x.highOrder & 0xffff) + (y.highOrder & 0xffff) + (msw >>> 16);\n  msw = (x.highOrder >>> 16) + (y.highOrder >>> 16) + (lsw >>> 16);\n  const highOrder = ((msw & 0xffff) << 16) | (lsw & 0xffff);\n\n  return new Int_64(highOrder, lowOrder);\n}\n\n/**\n * Add four 64-bit integers.\n *\n * @param a The first 64-bit integer argument to be added.\n * @param b The second 64-bit integer argument to be added.\n * @param c The third 64-bit integer argument to be added.\n * @param d The fouth 64-bit integer argument to be added.\n * @returns The sum of `a` + `b` + `c` + `d`.\n */\nexport function safeAdd_64_4(a: Int_64, b: Int_64, c: Int_64, d: Int_64): Int_64 {\n  let lsw, msw;\n\n  lsw = (a.lowOrder & 0xffff) + (b.lowOrder & 0xffff) + (c.lowOrder & 0xffff) + (d.lowOrder & 0xffff);\n  msw = (a.lowOrder >>> 16) + (b.lowOrder >>> 16) + (c.lowOrder >>> 16) + (d.lowOrder >>> 16) + (lsw >>> 16);\n  const lowOrder = ((msw & 0xffff) << 16) | (lsw & 0xffff);\n\n  lsw =\n    (a.highOrder & 0xffff) + (b.highOrder & 0xffff) + (c.highOrder & 0xffff) + (d.highOrder & 0xffff) + (msw >>> 16);\n  msw = (a.highOrder >>> 16) + (b.highOrder >>> 16) + (c.highOrder >>> 16) + (d.highOrder >>> 16) + (lsw >>> 16);\n  const highOrder = ((msw & 0xffff) << 16) | (lsw & 0xffff);\n\n  return new Int_64(highOrder, lowOrder);\n}\n\n/**\n * Add five 64-bit integers.\n *\n * @param a The first 64-bit integer argument to be added.\n * @param b The second 64-bit integer argument to be added.\n * @param c The third 64-bit integer argument to be added.\n * @param d The fouth 64-bit integer argument to be added.\n * @param e The fifth 64-bit integer argument to be added.\n * @returns The sum of `a` + `b` + `c` + `d` + `e`.\n */\nexport function safeAdd_64_5(a: Int_64, b: Int_64, c: Int_64, d: Int_64, e: Int_64): Int_64 {\n  let lsw, msw;\n\n  lsw =\n    (a.lowOrder & 0xffff) +\n    (b.lowOrder & 0xffff) +\n    (c.lowOrder & 0xffff) +\n    (d.lowOrder & 0xffff) +\n    (e.lowOrder & 0xffff);\n  msw =\n    (a.lowOrder >>> 16) +\n    (b.lowOrder >>> 16) +\n    (c.lowOrder >>> 16) +\n    (d.lowOrder >>> 16) +\n    (e.lowOrder >>> 16) +\n    (lsw >>> 16);\n  const lowOrder = ((msw & 0xffff) << 16) | (lsw & 0xffff);\n\n  lsw =\n    (a.highOrder & 0xffff) +\n    (b.highOrder & 0xffff) +\n    (c.highOrder & 0xffff) +\n    (d.highOrder & 0xffff) +\n    (e.highOrder & 0xffff) +\n    (msw >>> 16);\n  msw =\n    (a.highOrder >>> 16) +\n    (b.highOrder >>> 16) +\n    (c.highOrder >>> 16) +\n    (d.highOrder >>> 16) +\n    (e.highOrder >>> 16) +\n    (lsw >>> 16);\n  const highOrder = ((msw & 0xffff) << 16) | (lsw & 0xffff);\n\n  return new Int_64(highOrder, lowOrder);\n}\n\n/**\n * XORs two given arguments.\n *\n * @param a The first argument to be XORed.\n * @param b The second argument to be XORed.\n * @returns The The XOR `a` and `b`\n */\nexport function xor_64_2(a: Int_64, b: Int_64): Int_64 {\n  return new Int_64(a.highOrder ^ b.highOrder, a.lowOrder ^ b.lowOrder);\n}\n\n/**\n * XORs five given arguments.\n *\n * @param a The first argument to be XORed.\n * @param b The second argument to be XORed.\n * @param c The third argument to be XORed.\n * @param d The fourth argument to be XORed.\n * @param e The fifth argument to be XORed.\n * @returns The XOR of `a`, `b`, `c`, `d`, and `e`.\n */\nexport function xor_64_5(a: Int_64, b: Int_64, c: Int_64, d: Int_64, e: Int_64): Int_64 {\n  return new Int_64(\n    a.highOrder ^ b.highOrder ^ c.highOrder ^ d.highOrder ^ e.highOrder,\n    a.lowOrder ^ b.lowOrder ^ c.lowOrder ^ d.lowOrder ^ e.lowOrder\n  );\n}\n\n/**\n * The 64-bit implementation of the NIST specified Gamma1 function.\n *\n * @param x The 64-bit integer argument.\n * @returns The NIST specified output of the function.\n */\nexport function gamma1_64(x: Int_64): Int_64 {\n  const rotr19 = rotr_64(x, 19),\n    rotr61 = rotr_64(x, 61),\n    shr6 = shr_64(x, 6);\n\n  return new Int_64(\n    rotr19.highOrder ^ rotr61.highOrder ^ shr6.highOrder,\n    rotr19.lowOrder ^ rotr61.lowOrder ^ shr6.lowOrder\n  );\n}\n\n/**\n * The 64-bit implementation of the NIST specified Gamma0 function.\n *\n * @param x The 64-bit integer argument.\n * @returns The NIST specified output of the function.\n */\nexport function gamma0_64(x: Int_64): Int_64 {\n  const rotr1 = rotr_64(x, 1),\n    rotr8 = rotr_64(x, 8),\n    shr7 = shr_64(x, 7);\n\n  return new Int_64(\n    rotr1.highOrder ^ rotr8.highOrder ^ shr7.highOrder,\n    rotr1.lowOrder ^ rotr8.lowOrder ^ shr7.lowOrder\n  );\n}\n\n/**\n * The 64-bit implementation of the NIST specified Sigma1 function.\n *\n * @param x The 64-bit integer argument.\n * @returns The NIST specified output of the function.\n */\nexport function sigma1_64(x: Int_64): Int_64 {\n  const rotr14 = rotr_64(x, 14),\n    rotr18 = rotr_64(x, 18),\n    rotr41 = rotr_64(x, 41);\n\n  return new Int_64(\n    rotr14.highOrder ^ rotr18.highOrder ^ rotr41.highOrder,\n    rotr14.lowOrder ^ rotr18.lowOrder ^ rotr41.lowOrder\n  );\n}\n", "import { js<PERSON><PERSON><PERSON><PERSON>, TWO_PWR_32, H_trunc, H_full, K_sha2, sha_variant_error, parseInputOption } from \"./common\";\nimport {\n  packedValue,\n  FixedLengthOptionsEncodingType,\n  FixedLengthOptionsNoEncodingType,\n  FormatNoTextType,\n} from \"./custom_types\";\nimport { getStrConverter } from \"./converters\";\nimport {\n  ch_64,\n  gamma0_64,\n  gamma1_64,\n  Int_64,\n  maj_64,\n  safeAdd_64_2,\n  safeAdd_64_4,\n  safeAdd_64_5,\n  sigma0_64,\n  sigma1_64,\n} from \"./primitives_64\";\n\ntype VariantType = \"SHA-384\" | \"SHA-512\";\n\nconst K_sha512 = [\n  new Int_64(K_sha2[0], 0xd728ae22),\n  new Int_64(K_sha2[1], 0x23ef65cd),\n  new Int_64(K_sha2[2], 0xec4d3b2f),\n  new Int_64(K_sha2[3], 0x8189dbbc),\n  new Int_64(K_sha2[4], 0xf348b538),\n  new Int_64(K_sha2[5], 0xb605d019),\n  new Int_64(K_sha2[6], 0xaf194f9b),\n  new Int_64(K_sha2[7], 0xda6d8118),\n  new Int_64(K_sha2[8], 0xa3030242),\n  new Int_64(K_sha2[9], 0x45706fbe),\n  new Int_64(K_sha2[10], 0x4ee4b28c),\n  new Int_64(K_sha2[11], 0xd5ffb4e2),\n  new Int_64(K_sha2[12], 0xf27b896f),\n  new Int_64(K_sha2[13], 0x3b1696b1),\n  new Int_64(K_sha2[14], 0x25c71235),\n  new Int_64(K_sha2[15], 0xcf692694),\n  new Int_64(K_sha2[16], 0x9ef14ad2),\n  new Int_64(K_sha2[17], 0x384f25e3),\n  new Int_64(K_sha2[18], 0x8b8cd5b5),\n  new Int_64(K_sha2[19], 0x77ac9c65),\n  new Int_64(K_sha2[20], 0x592b0275),\n  new Int_64(K_sha2[21], 0x6ea6e483),\n  new Int_64(K_sha2[22], 0xbd41fbd4),\n  new Int_64(K_sha2[23], 0x831153b5),\n  new Int_64(K_sha2[24], 0xee66dfab),\n  new Int_64(K_sha2[25], 0x2db43210),\n  new Int_64(K_sha2[26], 0x98fb213f),\n  new Int_64(K_sha2[27], 0xbeef0ee4),\n  new Int_64(K_sha2[28], 0x3da88fc2),\n  new Int_64(K_sha2[29], 0x930aa725),\n  new Int_64(K_sha2[30], 0xe003826f),\n  new Int_64(K_sha2[31], 0x0a0e6e70),\n  new Int_64(K_sha2[32], 0x46d22ffc),\n  new Int_64(K_sha2[33], 0x5c26c926),\n  new Int_64(K_sha2[34], 0x5ac42aed),\n  new Int_64(K_sha2[35], 0x9d95b3df),\n  new Int_64(K_sha2[36], 0x8baf63de),\n  new Int_64(K_sha2[37], 0x3c77b2a8),\n  new Int_64(K_sha2[38], 0x47edaee6),\n  new Int_64(K_sha2[39], 0x1482353b),\n  new Int_64(K_sha2[40], 0x4cf10364),\n  new Int_64(K_sha2[41], 0xbc423001),\n  new Int_64(K_sha2[42], 0xd0f89791),\n  new Int_64(K_sha2[43], 0x0654be30),\n  new Int_64(K_sha2[44], 0xd6ef5218),\n  new Int_64(K_sha2[45], 0x5565a910),\n  new Int_64(K_sha2[46], 0x5771202a),\n  new Int_64(K_sha2[47], 0x32bbd1b8),\n  new Int_64(K_sha2[48], 0xb8d2d0c8),\n  new Int_64(K_sha2[49], 0x5141ab53),\n  new Int_64(K_sha2[50], 0xdf8eeb99),\n  new Int_64(K_sha2[51], 0xe19b48a8),\n  new Int_64(K_sha2[52], 0xc5c95a63),\n  new Int_64(K_sha2[53], 0xe3418acb),\n  new Int_64(K_sha2[54], 0x7763e373),\n  new Int_64(K_sha2[55], 0xd6b2b8a3),\n  new Int_64(K_sha2[56], 0x5defb2fc),\n  new Int_64(K_sha2[57], 0x43172f60),\n  new Int_64(K_sha2[58], 0xa1f0ab72),\n  new Int_64(K_sha2[59], 0x1a6439ec),\n  new Int_64(K_sha2[60], 0x23631e28),\n  new Int_64(K_sha2[61], 0xde82bde9),\n  new Int_64(K_sha2[62], 0xb2c67915),\n  new Int_64(K_sha2[63], 0xe372532b),\n  new Int_64(0xca273ece, 0xea26619c),\n  new Int_64(0xd186b8c7, 0x21c0c207),\n  new Int_64(0xeada7dd6, 0xcde0eb1e),\n  new Int_64(0xf57d4f7f, 0xee6ed178),\n  new Int_64(0x06f067aa, 0x72176fba),\n  new Int_64(0x0a637dc5, 0xa2c898a6),\n  new Int_64(0x113f9804, 0xbef90dae),\n  new Int_64(0x1b710b35, 0x131c471b),\n  new Int_64(0x28db77f5, 0x23047d84),\n  new Int_64(0x32caab7b, 0x40c72493),\n  new Int_64(0x3c9ebe0a, 0x15c9bebc),\n  new Int_64(0x431d67c4, 0x9c100d4c),\n  new Int_64(0x4cc5d4be, 0xcb3e42b6),\n  new Int_64(0x597f299c, 0xfc657e2a),\n  new Int_64(0x5fcb6fab, 0x3ad6faec),\n  new Int_64(0x6c44198c, 0x4a475817),\n];\n\n/**\n * Gets the state values for the specified SHA variant.\n *\n * @param variant: The SHA-512 family variant.\n * @returns The initial state values.\n */\nfunction getNewState512(variant: VariantType): Int_64[] {\n  if (\"SHA-384\" === variant) {\n    return [\n      new Int_64(0xcbbb9d5d, H_trunc[0]),\n      new Int_64(0x0629a292a, H_trunc[1]),\n      new Int_64(0x9159015a, H_trunc[2]),\n      new Int_64(0x0152fecd8, H_trunc[3]),\n      new Int_64(0x67332667, H_trunc[4]),\n      new Int_64(0x98eb44a87, H_trunc[5]),\n      new Int_64(0xdb0c2e0d, H_trunc[6]),\n      new Int_64(0x047b5481d, H_trunc[7]),\n    ];\n  } else {\n    /* SHA-512 */\n    return [\n      new Int_64(H_full[0], 0xf3bcc908),\n      new Int_64(H_full[1], 0x84caa73b),\n      new Int_64(H_full[2], 0xfe94f82b),\n      new Int_64(H_full[3], 0x5f1d36f1),\n      new Int_64(H_full[4], 0xade682d1),\n      new Int_64(H_full[5], 0x2b3e6c1f),\n      new Int_64(H_full[6], 0xfb41bd6b),\n      new Int_64(H_full[7], 0x137e2179),\n    ];\n  }\n}\n\n/**\n * Performs a round of SHA-512 hashing over a block. This clobbers `H`.\n *\n * @param block The binary array representation of the block to hash.\n * @param H The intermediate H values from a previous round.\n * @returns The resulting H values.\n */\nfunction roundSHA512(block: number[], H: Int_64[]): Int_64[] {\n  let a, b, c, d, e, f, g, h, T1, T2, t, offset;\n\n  const W: Int_64[] = [];\n\n  a = H[0];\n  b = H[1];\n  c = H[2];\n  d = H[3];\n  e = H[4];\n  f = H[5];\n  g = H[6];\n  h = H[7];\n\n  for (t = 0; t < 80; t += 1) {\n    if (t < 16) {\n      offset = t * 2;\n      W[t] = new Int_64(block[offset], block[offset + 1]);\n    } else {\n      W[t] = safeAdd_64_4(gamma1_64(W[t - 2]), W[t - 7], gamma0_64(W[t - 15]), W[t - 16]);\n    }\n    T1 = safeAdd_64_5(h, sigma1_64(e), ch_64(e, f, g), K_sha512[t], W[t]);\n    T2 = safeAdd_64_2(sigma0_64(a), maj_64(a, b, c));\n    h = g;\n    g = f;\n    f = e;\n    e = safeAdd_64_2(d, T1);\n    d = c;\n    c = b;\n    b = a;\n    a = safeAdd_64_2(T1, T2);\n  }\n\n  H[0] = safeAdd_64_2(a, H[0]);\n  H[1] = safeAdd_64_2(b, H[1]);\n  H[2] = safeAdd_64_2(c, H[2]);\n  H[3] = safeAdd_64_2(d, H[3]);\n  H[4] = safeAdd_64_2(e, H[4]);\n  H[5] = safeAdd_64_2(f, H[5]);\n  H[6] = safeAdd_64_2(g, H[6]);\n  H[7] = safeAdd_64_2(h, H[7]);\n\n  return H;\n}\n\n/**\n * Finalizes the SHA-512 hash. This clobbers `remainder` and `H`.\n *\n * @param remainder Any leftover unprocessed packed ints that still need to be processed.\n * @param remainderBinLen The number of bits in `remainder`.\n * @param processedBinLen The number of bits already processed.\n * @param H The intermediate H values from a previous round.\n * @param variant The desired SHA-512 variant.\n * @returns The array of integers representing the SHA-512 hash of message.\n */\nfunction finalizeSHA512(\n  remainder: number[],\n  remainderBinLen: number,\n  processedBinLen: number,\n  H: Int_64[],\n  variant: VariantType\n): number[] {\n  let i, retVal;\n\n  /* The 129 addition is a hack but it works.  The correct number is\n    actually 136 (128 + 8) but the below math fails if\n    remainderBinLen + 136 % 1024 = 0. Since remainderBinLen % 8 = 0,\n    \"shorting\" the addition is OK. */\n  const offset = (((remainderBinLen + 129) >>> 10) << 5) + 31,\n    binaryStringInc = 32,\n    totalLen = remainderBinLen + processedBinLen;\n\n  while (remainder.length <= offset) {\n    remainder.push(0);\n  }\n  /* Append '1' at the end of the binary string */\n  remainder[remainderBinLen >>> 5] |= 0x80 << (24 - (remainderBinLen % 32));\n  /* Append length of binary string in the position such that the new\n   * length is correct. JavaScript numbers are limited to 2^53 so it's\n   * \"safe\" to treat the totalLen as a 64-bit integer. */\n\n  remainder[offset] = totalLen & 0xffffffff;\n  /* Bitwise operators treat the operand as a 32-bit number so need to\n   * use hacky division and round to get access to upper 32-ish bits */\n  remainder[offset - 1] = (totalLen / TWO_PWR_32) | 0;\n\n  /* This will always be at least 1 full chunk */\n  for (i = 0; i < remainder.length; i += binaryStringInc) {\n    H = roundSHA512(remainder.slice(i, i + binaryStringInc), H);\n  }\n\n  if (\"SHA-384\" === variant) {\n    H = (H as unknown) as Int_64[];\n    retVal = [\n      H[0].highOrder,\n      H[0].lowOrder,\n      H[1].highOrder,\n      H[1].lowOrder,\n      H[2].highOrder,\n      H[2].lowOrder,\n      H[3].highOrder,\n      H[3].lowOrder,\n      H[4].highOrder,\n      H[4].lowOrder,\n      H[5].highOrder,\n      H[5].lowOrder,\n    ];\n  } else {\n    /* SHA-512 */\n    retVal = [\n      H[0].highOrder,\n      H[0].lowOrder,\n      H[1].highOrder,\n      H[1].lowOrder,\n      H[2].highOrder,\n      H[2].lowOrder,\n      H[3].highOrder,\n      H[3].lowOrder,\n      H[4].highOrder,\n      H[4].lowOrder,\n      H[5].highOrder,\n      H[5].lowOrder,\n      H[6].highOrder,\n      H[6].lowOrder,\n      H[7].highOrder,\n      H[7].lowOrder,\n    ];\n  }\n  return retVal;\n}\n\nexport default class jsSHA extends jsSHABase<Int_64[], VariantType> {\n  intermediateState: Int_64[];\n  variantBlockSize: number;\n  bigEndianMod: -1 | 1;\n  outputBinLen: number;\n  isVariableLen: boolean;\n  HMACSupported: boolean;\n\n  /* eslint-disable-next-line @typescript-eslint/no-explicit-any */\n  converterFunc: (input: any, existingBin: number[], existingBinLen: number) => packedValue;\n  roundFunc: (block: number[], H: Int_64[]) => Int_64[];\n  finalizeFunc: (remainder: number[], remainderBinLen: number, processedBinLen: number, H: Int_64[]) => number[];\n  stateCloneFunc: (state: Int_64[]) => Int_64[];\n  newStateFunc: (variant: VariantType) => Int_64[];\n  getMAC: () => number[];\n\n  constructor(variant: VariantType, inputFormat: \"TEXT\", options?: FixedLengthOptionsEncodingType);\n  constructor(variant: VariantType, inputFormat: FormatNoTextType, options?: FixedLengthOptionsNoEncodingType);\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  constructor(variant: any, inputFormat: any, options?: any) {\n    if (!(\"SHA-384\" === variant || \"SHA-512\" === variant)) {\n      throw new Error(sha_variant_error);\n    }\n    super(variant, inputFormat, options);\n    const resolvedOptions = options || {};\n\n    // eslint-disable-next-line @typescript-eslint/unbound-method\n    this.getMAC = this._getHMAC;\n    this.HMACSupported = true;\n    this.bigEndianMod = -1;\n    this.converterFunc = getStrConverter(this.inputFormat, this.utfType, this.bigEndianMod);\n    this.roundFunc = roundSHA512;\n    this.stateCloneFunc = function (state): Int_64[] {\n      return state.slice();\n    };\n    this.newStateFunc = getNewState512;\n    this.finalizeFunc = function (remainder, remainderBinLen, processedBinLen, H): number[] {\n      return finalizeSHA512(remainder, remainderBinLen, processedBinLen, H, variant);\n    };\n\n    this.intermediateState = getNewState512(variant);\n    this.variantBlockSize = 1024;\n    this.outputBinLen = \"SHA-384\" === variant ? 384 : 512;\n    this.isVariableLen = false;\n\n    if (resolvedOptions[\"hmacKey\"]) {\n      this._setHMACKey(parseInputOption(\"hmacKey\", resolvedOptions[\"hmacKey\"], this.bigEndianMod));\n    }\n  }\n}\n", "import { jsSHABase, packedLEConcat, sha_variant_error, mac_rounds_error, TWO_PWR_32, parseInputOption } from \"./common\";\nimport {\n  packedValue,\n  CSHAKEOptionsNoEncodingType,\n  CSHAKEOptionsEncodingType,\n  SHAKEOptionsNoEncodingType,\n  SHAKEOptionsEncodingType,\n  KMACOptionsNoEncodingType,\n  KMACOptionsEncodingType,\n  FixedLengthOptionsEncodingType,\n  FixedLengthOptionsNoEncodingType,\n  FormatNoTextType,\n  ResolvedCSHAKEOptionsNoEncodingType,\n  ResolvedKMACOptionsNoEncodingType,\n} from \"./custom_types\";\nimport { getStrConverter } from \"./converters\";\nimport { Int_64, rotl_64, xor_64_2, xor_64_5 } from \"./primitives_64\";\n\ntype FixedLengthVariantType = \"SHA3-224\" | \"SHA3-256\" | \"SHA3-384\" | \"SHA3-512\" | \"SHAKE128\" | \"SHAKE256\";\n\ntype VariantType = FixedLengthVariantType | \"SHAKE128\" | \"SHAKE256\" | \"CSHAKE128\" | \"CSHAKE256\" | \"KMAC128\" | \"KMAC256\";\n\nconst rc_sha3 = [\n  new Int_64(0x00000000, 0x00000001),\n  new Int_64(0x00000000, 0x00008082),\n  new Int_64(0x80000000, 0x0000808a),\n  new Int_64(0x80000000, 0x80008000),\n  new Int_64(0x00000000, 0x0000808b),\n  new Int_64(0x00000000, 0x80000001),\n  new Int_64(0x80000000, 0x80008081),\n  new Int_64(0x80000000, 0x00008009),\n  new Int_64(0x00000000, 0x0000008a),\n  new Int_64(0x00000000, 0x00000088),\n  new Int_64(0x00000000, 0x80008009),\n  new Int_64(0x00000000, 0x8000000a),\n  new Int_64(0x00000000, 0x8000808b),\n  new Int_64(0x80000000, 0x0000008b),\n  new Int_64(0x80000000, 0x00008089),\n  new Int_64(0x80000000, 0x00008003),\n  new Int_64(0x80000000, 0x00008002),\n  new Int_64(0x80000000, 0x00000080),\n  new Int_64(0x00000000, 0x0000800a),\n  new Int_64(0x80000000, 0x8000000a),\n  new Int_64(0x80000000, 0x80008081),\n  new Int_64(0x80000000, 0x00008080),\n  new Int_64(0x00000000, 0x80000001),\n  new Int_64(0x80000000, 0x80008008),\n];\n\nconst r_sha3 = [\n  [0, 36, 3, 41, 18],\n  [1, 44, 10, 45, 2],\n  [62, 6, 43, 15, 61],\n  [28, 55, 25, 21, 56],\n  [27, 20, 39, 8, 14],\n];\n\n/**\n * Gets the state values for the specified SHA-3 variant.\n *\n * @param _variant Unused for this family.\n * @returns The initial state values.\n */\nfunction getNewState(_variant: VariantType): Int_64[][] {\n  let i;\n  const retVal = [];\n\n  for (i = 0; i < 5; i += 1) {\n    retVal[i] = [new Int_64(0, 0), new Int_64(0, 0), new Int_64(0, 0), new Int_64(0, 0), new Int_64(0, 0)];\n  }\n\n  return retVal;\n}\n\n/**\n * Returns a clone of the given SHA3 state.\n *\n * @param state The state to be cloned.\n * @returns The cloned state.\n */\nfunction cloneSHA3State(state: Int_64[][]): Int_64[][] {\n  let i;\n  const clone = [];\n  for (i = 0; i < 5; i += 1) {\n    clone[i] = state[i].slice();\n  }\n\n  return clone;\n}\n\n/**\n * Performs a round of SHA-3 hashing over a block. This clobbers `state`.\n *\n * @param block The binary array representation of the block to hash.\n * @param state Hash state from a previous round.\n * @returns The resulting state value.\n */\nfunction roundSHA3(block: number[] | null, state: Int_64[][]): Int_64[][] {\n  let round, x, y, B;\n  const C = [],\n    D = [];\n\n  if (null !== block) {\n    for (x = 0; x < block.length; x += 2) {\n      state[(x >>> 1) % 5][((x >>> 1) / 5) | 0] = xor_64_2(\n        state[(x >>> 1) % 5][((x >>> 1) / 5) | 0],\n        new Int_64(block[x + 1], block[x])\n      );\n    }\n  }\n\n  for (round = 0; round < 24; round += 1) {\n    /* Any SHA-3 variant name will do here */\n    B = getNewState(\"SHA3-384\");\n\n    /* Perform theta step */\n    for (x = 0; x < 5; x += 1) {\n      C[x] = xor_64_5(state[x][0], state[x][1], state[x][2], state[x][3], state[x][4]);\n    }\n    for (x = 0; x < 5; x += 1) {\n      D[x] = xor_64_2(C[(x + 4) % 5], rotl_64(C[(x + 1) % 5], 1));\n    }\n    for (x = 0; x < 5; x += 1) {\n      for (y = 0; y < 5; y += 1) {\n        state[x][y] = xor_64_2(state[x][y], D[x]);\n      }\n    }\n\n    /* Perform combined ro and pi steps */\n    for (x = 0; x < 5; x += 1) {\n      for (y = 0; y < 5; y += 1) {\n        B[y][(2 * x + 3 * y) % 5] = rotl_64(state[x][y], r_sha3[x][y]);\n      }\n    }\n\n    /* Perform chi step */\n    for (x = 0; x < 5; x += 1) {\n      for (y = 0; y < 5; y += 1) {\n        state[x][y] = xor_64_2(\n          B[x][y],\n          new Int_64(\n            ~B[(x + 1) % 5][y].highOrder & B[(x + 2) % 5][y].highOrder,\n            ~B[(x + 1) % 5][y].lowOrder & B[(x + 2) % 5][y].lowOrder\n          )\n        );\n      }\n    }\n\n    /* Perform iota step */\n    state[0][0] = xor_64_2(state[0][0], rc_sha3[round]);\n  }\n\n  return state;\n}\n\n/**\n * Finalizes the SHA-3 hash. This clobbers `remainder` and `state`.\n *\n * @param remainder Any leftover unprocessed packed ints that still need to be processed.\n * @param remainderBinLen The number of bits in `remainder`.\n * @param _processedBinLen Unused for this family.\n * @param state The state from a previous round.\n * @param blockSize The block size/rate of the variant in bits\n * @param delimiter The delimiter value for the variant\n * @param outputLen The output length for the variant in bits\n * @returns The array of integers representing the SHA-3 hash of message.\n */\nfunction finalizeSHA3(\n  remainder: number[],\n  remainderBinLen: number,\n  _processedBinLen: number,\n  state: Int_64[][],\n  blockSize: number,\n  delimiter: number,\n  outputLen: number\n): number[] {\n  let i,\n    state_offset = 0,\n    temp;\n  const retVal = [],\n    binaryStringInc = blockSize >>> 5,\n    remainderIntLen = remainderBinLen >>> 5;\n\n  /* Process as many blocks as possible, some may be here for multiple rounds\n\t\twith SHAKE\n\t*/\n  for (i = 0; i < remainderIntLen && remainderBinLen >= blockSize; i += binaryStringInc) {\n    state = roundSHA3(remainder.slice(i, i + binaryStringInc), state);\n    remainderBinLen -= blockSize;\n  }\n\n  remainder = remainder.slice(i);\n  remainderBinLen = remainderBinLen % blockSize;\n\n  /* Pad out the remainder to a full block */\n  while (remainder.length < binaryStringInc) {\n    remainder.push(0);\n  }\n\n  /* Find the next \"empty\" byte for the 0x80 and append it via an xor */\n  i = remainderBinLen >>> 3;\n  remainder[i >> 2] ^= delimiter << (8 * (i % 4));\n\n  remainder[binaryStringInc - 1] ^= 0x80000000;\n  state = roundSHA3(remainder, state);\n\n  while (retVal.length * 32 < outputLen) {\n    temp = state[state_offset % 5][(state_offset / 5) | 0];\n    retVal.push(temp.lowOrder);\n    if (retVal.length * 32 >= outputLen) {\n      break;\n    }\n    retVal.push(temp.highOrder);\n    state_offset += 1;\n\n    if (0 === (state_offset * 64) % blockSize) {\n      roundSHA3(null, state);\n      state_offset = 0;\n    }\n  }\n\n  return retVal;\n}\n\n/**\n * Performs NIST left_encode function returned with no extra garbage bits. `x` is limited to <= 9007199254740991.\n *\n * @param x 32-bit number to to encode.\n * @returns The NIST specified output of the function.\n */\nfunction left_encode(x: number): packedValue {\n  let byteOffset,\n    byte,\n    numEncodedBytes = 0;\n  /* JavaScript numbers max out at 0x1FFFFFFFFFFFFF (7 bytes) so this will return a maximum of 7 + 1 = 8 bytes */\n  const retVal = [0, 0],\n    x_64 = [x & 0xffffffff, (x / TWO_PWR_32) & 0x1fffff];\n\n  for (byteOffset = 6; byteOffset >= 0; byteOffset--) {\n    /* This will surprisingly work for large shifts because JavaScript masks the shift amount by 0x1F */\n    byte = (x_64[byteOffset >> 2] >>> (8 * byteOffset)) & 0xff;\n\n    /* Starting from the most significant byte of a 64-bit number, start recording the first non-0 byte and then\n       every byte thereafter */\n    if (byte !== 0 || numEncodedBytes !== 0) {\n      retVal[(numEncodedBytes + 1) >> 2] |= byte << ((numEncodedBytes + 1) * 8);\n      numEncodedBytes += 1;\n    }\n  }\n  numEncodedBytes = numEncodedBytes !== 0 ? numEncodedBytes : 1;\n  retVal[0] |= numEncodedBytes;\n\n  return { value: numEncodedBytes + 1 > 4 ? retVal : [retVal[0]], binLen: 8 + numEncodedBytes * 8 };\n}\n\n/**\n * Performs NIST right_encode function returned with no extra garbage bits. `x` is limited to <= 9007199254740991.\n *\n * @param x 32-bit number to to encode.\n * @returns The NIST specified output of the function.\n */\nfunction right_encode(x: number): packedValue {\n  let byteOffset,\n    byte,\n    numEncodedBytes = 0;\n  /* JavaScript numbers max out at 0x1FFFFFFFFFFFFF (7 bytes) so this will return a maximum of 7 + 1 = 8 bytes */\n  const retVal = [0, 0],\n    x_64 = [x & 0xffffffff, (x / TWO_PWR_32) & 0x1fffff];\n\n  for (byteOffset = 6; byteOffset >= 0; byteOffset--) {\n    /* This will surprisingly work for large shifts because JavaScript masks the shift amount by 0x1F */\n    byte = (x_64[byteOffset >> 2] >>> (8 * byteOffset)) & 0xff;\n\n    /* Starting from the most significant byte of a 64-bit number, start recording the first non-0 byte and then\n       every byte thereafter */\n    if (byte !== 0 || numEncodedBytes !== 0) {\n      retVal[numEncodedBytes >> 2] |= byte << (numEncodedBytes * 8);\n      numEncodedBytes += 1;\n    }\n  }\n  numEncodedBytes = numEncodedBytes !== 0 ? numEncodedBytes : 1;\n  retVal[numEncodedBytes >> 2] |= numEncodedBytes << (numEncodedBytes * 8);\n\n  return { value: numEncodedBytes + 1 > 4 ? retVal : [retVal[0]], binLen: 8 + numEncodedBytes * 8 };\n}\n\n/**\n * Performs NIST encode_string function.\n *\n * @param input Packed array of integers.\n * @returns NIST encode_string output.\n */\nfunction encode_string(input: packedValue): packedValue {\n  return packedLEConcat(left_encode(input[\"binLen\"]), input);\n}\n\n/**\n * Performs NIST byte_pad function.\n *\n * @param packed Packed array of integers.\n * @param outputByteLen Desired length of the output in bytes, assumed to be a multiple of 4.\n * @returns NIST byte_pad output.\n */\nfunction byte_pad(packed: packedValue, outputByteLen: number): number[] {\n  let encodedLen = left_encode(outputByteLen),\n    i;\n\n  encodedLen = packedLEConcat(encodedLen, packed);\n  const outputIntLen = outputByteLen >>> 2,\n    intsToAppend = (outputIntLen - (encodedLen[\"value\"].length % outputIntLen)) % outputIntLen;\n\n  for (i = 0; i < intsToAppend; i++) {\n    encodedLen[\"value\"].push(0);\n  }\n\n  return encodedLen[\"value\"];\n}\n\n/**\n * Parses/validate constructor options for a CSHAKE variant\n *\n * @param options Option given to constructor\n */\nfunction resolveCSHAKEOptions(options: CSHAKEOptionsNoEncodingType): ResolvedCSHAKEOptionsNoEncodingType {\n  const resolvedOptions = options || {};\n\n  return {\n    funcName: parseInputOption(\"funcName\", resolvedOptions[\"funcName\"], 1, { value: [], binLen: 0 }),\n    customization: parseInputOption(\"Customization\", resolvedOptions[\"customization\"], 1, { value: [], binLen: 0 }),\n  };\n}\n\n/**\n * Parses/validate constructor options for a KMAC variant\n *\n * @param options Option given to constructor\n */\nfunction resolveKMACOptions(options: KMACOptionsNoEncodingType): ResolvedKMACOptionsNoEncodingType {\n  const resolvedOptions = options || {};\n\n  return {\n    kmacKey: parseInputOption(\"kmacKey\", resolvedOptions[\"kmacKey\"], 1),\n    /* This is little-endian packed \"KMAC\" */\n    funcName: { value: [0x43414d4b], binLen: 32 },\n    customization: parseInputOption(\"Customization\", resolvedOptions[\"customization\"], 1, { value: [], binLen: 0 }),\n  };\n}\n\nexport default class jsSHA extends jsSHABase<Int_64[][], VariantType> {\n  intermediateState: Int_64[][];\n  variantBlockSize: number;\n  bigEndianMod: -1 | 1;\n  outputBinLen: number;\n  isVariableLen: boolean;\n  HMACSupported: boolean;\n\n  /* eslint-disable-next-line @typescript-eslint/no-explicit-any */\n  converterFunc: (input: any, existingBin: number[], existingBinLen: number) => packedValue;\n  roundFunc: (block: number[], H: Int_64[][]) => Int_64[][];\n  finalizeFunc: (\n    remainder: number[],\n    remainderBinLen: number,\n    processedBinLen: number,\n    H: Int_64[][],\n    outputLen: number\n  ) => number[];\n  stateCloneFunc: (state: Int_64[][]) => Int_64[][];\n  newStateFunc: (variant: VariantType) => Int_64[][];\n  getMAC: ((options: { outputLen: number }) => number[]) | null;\n\n  constructor(variant: FixedLengthVariantType, inputFormat: \"TEXT\", options?: FixedLengthOptionsEncodingType);\n  constructor(\n    variant: FixedLengthVariantType,\n    inputFormat: FormatNoTextType,\n    options?: FixedLengthOptionsNoEncodingType\n  );\n  constructor(variant: \"SHAKE128\" | \"SHAKE256\", inputFormat: \"TEXT\", options?: SHAKEOptionsEncodingType);\n  constructor(variant: \"SHAKE128\" | \"SHAKE256\", inputFormat: FormatNoTextType, options?: SHAKEOptionsNoEncodingType);\n  constructor(variant: \"CSHAKE128\" | \"CSHAKE256\", inputFormat: \"TEXT\", options?: CSHAKEOptionsEncodingType);\n  constructor(variant: \"CSHAKE128\" | \"CSHAKE256\", inputFormat: FormatNoTextType, options?: CSHAKEOptionsNoEncodingType);\n  constructor(variant: \"KMAC128\" | \"KMAC256\", inputFormat: \"TEXT\", options: KMACOptionsEncodingType);\n  constructor(variant: \"KMAC128\" | \"KMAC256\", inputFormat: FormatNoTextType, options: KMACOptionsNoEncodingType);\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  constructor(variant: any, inputFormat: any, options?: any) {\n    let delimiter = 0x06,\n      variantBlockSize = 0;\n    super(variant, inputFormat, options);\n    const resolvedOptions = options || {};\n\n    /* In other variants, this was done after variable initialization but need to do it earlier here becaue we want to\n       avoid KMAC initialization */\n    if (this.numRounds !== 1) {\n      if (resolvedOptions[\"kmacKey\"] || resolvedOptions[\"hmacKey\"]) {\n        throw new Error(mac_rounds_error);\n      } else if (this.shaVariant === \"CSHAKE128\" || this.shaVariant === \"CSHAKE256\") {\n        throw new Error(\"Cannot set numRounds for CSHAKE variants\");\n      }\n    }\n\n    this.bigEndianMod = 1;\n    this.converterFunc = getStrConverter(this.inputFormat, this.utfType, this.bigEndianMod);\n    this.roundFunc = roundSHA3;\n    this.stateCloneFunc = cloneSHA3State;\n    this.newStateFunc = getNewState;\n    this.intermediateState = getNewState(variant);\n\n    this.isVariableLen = false;\n    switch (variant) {\n      case \"SHA3-224\":\n        this.variantBlockSize = variantBlockSize = 1152;\n        this.outputBinLen = 224;\n        this.HMACSupported = true;\n        // eslint-disable-next-line @typescript-eslint/unbound-method\n        this.getMAC = this._getHMAC;\n        break;\n      case \"SHA3-256\":\n        this.variantBlockSize = variantBlockSize = 1088;\n        this.outputBinLen = 256;\n        this.HMACSupported = true;\n        // eslint-disable-next-line @typescript-eslint/unbound-method\n        this.getMAC = this._getHMAC;\n        break;\n      case \"SHA3-384\":\n        this.variantBlockSize = variantBlockSize = 832;\n        this.outputBinLen = 384;\n        this.HMACSupported = true;\n        // eslint-disable-next-line @typescript-eslint/unbound-method\n        this.getMAC = this._getHMAC;\n        break;\n      case \"SHA3-512\":\n        this.variantBlockSize = variantBlockSize = 576;\n        this.outputBinLen = 512;\n        this.HMACSupported = true;\n        // eslint-disable-next-line @typescript-eslint/unbound-method\n        this.getMAC = this._getHMAC;\n        break;\n      case \"SHAKE128\":\n        delimiter = 0x1f;\n        this.variantBlockSize = variantBlockSize = 1344;\n        /* This will be set in getHash */\n        this.outputBinLen = -1;\n        this.isVariableLen = true;\n        this.HMACSupported = false;\n        this.getMAC = null;\n        break;\n      case \"SHAKE256\":\n        delimiter = 0x1f;\n        this.variantBlockSize = variantBlockSize = 1088;\n        /* This will be set in getHash */\n        this.outputBinLen = -1;\n        this.isVariableLen = true;\n        this.HMACSupported = false;\n        this.getMAC = null;\n        break;\n      case \"KMAC128\":\n        delimiter = 0x4;\n        this.variantBlockSize = variantBlockSize = 1344;\n        this._initializeKMAC(options);\n        /* This will be set in getHash */\n        this.outputBinLen = -1;\n        this.isVariableLen = true;\n        this.HMACSupported = false;\n        // eslint-disable-next-line @typescript-eslint/unbound-method\n        this.getMAC = this._getKMAC;\n        break;\n      case \"KMAC256\":\n        delimiter = 0x4;\n        this.variantBlockSize = variantBlockSize = 1088;\n        this._initializeKMAC(options);\n        /* This will be set in getHash */\n        this.outputBinLen = -1;\n        this.isVariableLen = true;\n        this.HMACSupported = false;\n        // eslint-disable-next-line @typescript-eslint/unbound-method\n        this.getMAC = this._getKMAC;\n        break;\n      case \"CSHAKE128\":\n        this.variantBlockSize = variantBlockSize = 1344;\n        delimiter = this._initializeCSHAKE(options);\n        /* This will be set in getHash */\n        this.outputBinLen = -1;\n        this.isVariableLen = true;\n        this.HMACSupported = false;\n        this.getMAC = null;\n        break;\n      case \"CSHAKE256\":\n        this.variantBlockSize = variantBlockSize = 1088;\n        delimiter = this._initializeCSHAKE(options);\n        /* This will be set in getHash */\n        this.outputBinLen = -1;\n        this.isVariableLen = true;\n        this.HMACSupported = false;\n        this.getMAC = null;\n        break;\n      default:\n        throw new Error(sha_variant_error);\n    }\n\n    /* This needs to be down here as CSHAKE can change its delimiter */\n    this.finalizeFunc = function (remainder, remainderBinLen, processedBinLen, state, outputBinLen): number[] {\n      return finalizeSHA3(\n        remainder,\n        remainderBinLen,\n        processedBinLen,\n        state,\n        variantBlockSize,\n        delimiter,\n        outputBinLen\n      );\n    };\n\n    if (resolvedOptions[\"hmacKey\"]) {\n      this._setHMACKey(parseInputOption(\"hmacKey\", resolvedOptions[\"hmacKey\"], this.bigEndianMod));\n    }\n  }\n\n  /**\n   * Initialize CSHAKE variants.\n   *\n   * @param options Options containing CSHAKE params.\n   * @param funcNameOverride Overrides any \"funcName\" present in `options` (used with KMAC)\n   * @returns The delimiter to be used\n   */\n  protected _initializeCSHAKE(options?: CSHAKEOptionsNoEncodingType, funcNameOverride?: packedValue): number {\n    const resolvedOptions = resolveCSHAKEOptions(options || {});\n    if (funcNameOverride) {\n      resolvedOptions[\"funcName\"] = funcNameOverride;\n    }\n    const packedParams = packedLEConcat(\n      encode_string(resolvedOptions[\"funcName\"]),\n      encode_string(resolvedOptions[\"customization\"])\n    );\n\n    /* CSHAKE is defined to be a call to SHAKE iff both the customization and function-name string are both empty.  This\n       can be accomplished by processing nothing in this step. */\n    if (resolvedOptions[\"customization\"][\"binLen\"] !== 0 || resolvedOptions[\"funcName\"][\"binLen\"] !== 0) {\n      const byte_pad_out = byte_pad(packedParams, this.variantBlockSize >>> 3);\n      for (let i = 0; i < byte_pad_out.length; i += this.variantBlockSize >>> 5) {\n        this.intermediateState = this.roundFunc(\n          byte_pad_out.slice(i, i + (this.variantBlockSize >>> 5)),\n          this.intermediateState\n        );\n        this.processedLen += this.variantBlockSize;\n      }\n      return 0x04;\n    } else {\n      return 0x1f;\n    }\n  }\n\n  /**\n   * Initialize KMAC variants.\n   *\n   * @param options Options containing KMAC params.\n   */\n  protected _initializeKMAC(options: KMACOptionsNoEncodingType): void {\n    const resolvedOptions = resolveKMACOptions(options || {});\n\n    this._initializeCSHAKE(options, resolvedOptions[\"funcName\"]);\n    const byte_pad_out = byte_pad(encode_string(resolvedOptions[\"kmacKey\"]), this.variantBlockSize >>> 3);\n    for (let i = 0; i < byte_pad_out.length; i += this.variantBlockSize >>> 5) {\n      this.intermediateState = this.roundFunc(\n        byte_pad_out.slice(i, i + (this.variantBlockSize >>> 5)),\n        this.intermediateState\n      );\n      this.processedLen += this.variantBlockSize;\n    }\n    this.macKeySet = true;\n  }\n\n  /**\n   * Returns the the KMAC in the specified format.\n   *\n   * @param options Hashmap of extra outputs options. `outputLen` must be specified.\n   * @returns The KMAC in the format specified.\n   */\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  protected _getKMAC(options: { outputLen: number }): number[] {\n    const concatedRemainder = packedLEConcat(\n      { value: this.remainder.slice(), binLen: this.remainderLen },\n      right_encode(options[\"outputLen\"])\n    );\n\n    return this.finalizeFunc(\n      concatedRemainder[\"value\"],\n      concatedRemainder[\"binLen\"],\n      this.processedLen,\n      this.stateCloneFunc(this.intermediateState),\n      options[\"outputLen\"]\n    );\n  }\n}\n", "import { sha_variant_error } from \"./common\";\nimport {\n  CSHAKEOptionsEncodingType,\n  CSHAKEOptionsNoEncodingType,\n  SHAKEOptionsEncodingType,\n  SHAKEOptionsNoEncodingType,\n  EncodingType,\n  FixedLengthOptionsEncodingType,\n  FixedLengthOptionsNoEncodingType,\n  FormatNoTextType,\n  KMACOptionsNoEncodingType,\n  KMACOptionsEncodingType,\n} from \"./custom_types\";\nimport jsSHA1 from \"./sha1\";\nimport jsSHA256 from \"./sha256\";\nimport jsSHA512 from \"./sha512\";\nimport jsSHA3 from \"./sha3\";\n\ntype FixedLengthVariantType =\n  | \"SHA-1\"\n  | \"SHA-224\"\n  | \"SHA-256\"\n  | \"SHA-384\"\n  | \"SHA-512\"\n  | \"SHA3-224\"\n  | \"SHA3-256\"\n  | \"SHA3-384\"\n  | \"SHA3-512\";\n\nexport default class jsSHA {\n  private readonly shaObj: jsSHA1 | jsSHA256 | jsSHA512 | jsSHA3;\n  /**\n   * @param variant The desired SHA variant (SHA-1, SHA-224, SHA-256, SHA-384, SHA-512, SHA3-224, SHA3-256, SHA3-256,\n   *   SHA3-384, SHA3-512, SHAKE128, SHAKE256, CSHAKE128, CSHAKE256, KMAC128, or KMAC256) as a string.\n   * @param inputFormat The input format to be used in future `update` calls (TEXT, HEX, B64, BYTES, ARRAYBUFFER,\n   *   or UINT8ARRAY) as a string.\n   * @param options Options in the form of { encoding?: \"UTF8\" | \"UTF16BE\" | \"UTF16LE\"; numRounds?: number }.\n   *   `encoding` is for only TEXT input (defaults to UTF8) and `numRounds` defaults to 1.\n   *   `numRounds` is not valid for any of the MAC or CSHAKE variants.\n   *   * If the variant supports HMAC, `options` may have an additional `hmacKey` key which must be in the form of\n   *     {value: <INPUT>, format: <FORMAT>, encoding?: \"UTF8\" | \"UTF16BE\" | \"UTF16LE\"} where <FORMAT> takes the same\n   *     values as `inputFormat` and <INPUT> can be a `string | ArrayBuffer | Uint8Array` depending on <FORMAT>.\n   *     Supplying this key switches to HMAC calculation and replaces the now deprecated call to `setHMACKey`.\n   *   * If the variant is CSHAKE128 or CSHAKE256, `options` may have two additional keys, `customization` and `funcName`,\n   *     which are the NIST customization and function-name strings.  Both must be in the same form as `hmacKey`.\n   *   * If the variant is KMAC128 or KMAC256, `options` can include the `customization` key from CSHAKE variants and\n   *     *must* have a `kmacKey` key that takes the same form as the `customization` key.\n   */\n  constructor(variant: FixedLengthVariantType, inputFormat: \"TEXT\", options?: FixedLengthOptionsEncodingType);\n  constructor(\n    variant: FixedLengthVariantType,\n    inputFormat: FormatNoTextType,\n    options?: FixedLengthOptionsNoEncodingType\n  );\n  constructor(variant: \"SHAKE128\" | \"SHAKE256\", inputFormat: \"TEXT\", options?: SHAKEOptionsEncodingType);\n  constructor(variant: \"SHAKE128\" | \"SHAKE256\", inputFormat: FormatNoTextType, options?: SHAKEOptionsNoEncodingType);\n  constructor(variant: \"CSHAKE128\" | \"CSHAKE256\", inputFormat: \"TEXT\", options?: CSHAKEOptionsEncodingType);\n  constructor(variant: \"CSHAKE128\" | \"CSHAKE256\", inputFormat: FormatNoTextType, options?: CSHAKEOptionsNoEncodingType);\n  constructor(variant: \"KMAC128\" | \"KMAC256\", inputFormat: \"TEXT\", options: KMACOptionsEncodingType);\n  constructor(variant: \"KMAC128\" | \"KMAC256\", inputFormat: FormatNoTextType, options: KMACOptionsNoEncodingType);\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  constructor(variant: any, inputFormat: any, options?: any) {\n    if (\"SHA-1\" == variant) {\n      this.shaObj = new jsSHA1(variant, inputFormat, options);\n    } else if (\"SHA-224\" == variant || \"SHA-256\" == variant) {\n      this.shaObj = new jsSHA256(variant, inputFormat, options);\n    } else if (\"SHA-384\" == variant || \"SHA-512\" == variant) {\n      this.shaObj = new jsSHA512(variant, inputFormat, options);\n    } else if (\n      \"SHA3-224\" == variant ||\n      \"SHA3-256\" == variant ||\n      \"SHA3-384\" == variant ||\n      \"SHA3-512\" == variant ||\n      \"SHAKE128\" == variant ||\n      \"SHAKE256\" == variant ||\n      \"CSHAKE128\" == variant ||\n      \"CSHAKE256\" == variant ||\n      \"KMAC128\" == variant ||\n      \"KMAC256\" == variant\n    ) {\n      this.shaObj = new jsSHA3(variant, inputFormat, options);\n    } else {\n      throw new Error(sha_variant_error);\n    }\n  }\n\n  /**\n   * Takes `input` and hashes as many blocks as possible. Stores the rest for either a future `update` or `getHash` call.\n   *\n   * @param input The input to be hashed.\n   * @returns A reference to the object.\n   */\n  update(input: string | ArrayBuffer | Uint8Array): this {\n    this.shaObj.update(input);\n\n    return this;\n  }\n\n  /**\n   * Returns the desired SHA or MAC (if a HMAC/KMAC key was specified) hash of the input fed in via `update` calls.\n   *\n   * @param format The desired output formatting (B64, HEX, BYTES, ARRAYBUFFER, or UINT8ARRAY) as a string.\n   * @param options Options in the form of { outputUpper?: boolean; b64Pad?: string; outputLen?: number;  }.\n   *   `outputLen` is required for variable length output variants (this option was previously called `shakeLen` which\n   *    is now deprecated).\n   *   `outputUpper` is only for HEX output (defaults to false) and b64pad is only for B64 output (defaults to \"=\").\n   * @returns The hash in the format specified.\n   */\n  getHash(format: \"HEX\", options?: { outputUpper?: boolean; outputLen?: number; shakeLen?: number }): string;\n  getHash(format: \"B64\", options?: { b64Pad?: string; outputLen?: number; shakeLen?: number }): string;\n  getHash(format: \"BYTES\", options?: { outputLen?: number; shakeLen?: number }): string;\n  getHash(format: \"UINT8ARRAY\", options?: { outputLen?: number; shakeLen?: number }): Uint8Array;\n  getHash(format: \"ARRAYBUFFER\", options?: { outputLen?: number; shakeLen?: number }): ArrayBuffer;\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  getHash(format: any, options?: any): any {\n    return this.shaObj.getHash(format, options);\n  }\n\n  /**\n   * Sets the HMAC key for an eventual `getHMAC` call.  Must be called immediately after jsSHA object instantiation.\n   * Now deprecated in favor of setting the `hmacKey` at object instantiation.\n   *\n   * @param key The key used to calculate the HMAC\n   * @param inputFormat The format of key (HEX, TEXT, B64, BYTES, ARRAYBUFFER, or UINT8ARRAY) as a string.\n   * @param options Options in the form of { encoding?: \"UTF8\" | \"UTF16BE\" | \"UTF16LE }.  `encoding` is only for TEXT\n   *   and defaults to UTF8.\n   */\n  setHMACKey(key: string, inputFormat: \"TEXT\", options?: { encoding?: EncodingType }): void;\n  setHMACKey(key: string, inputFormat: \"B64\" | \"HEX\" | \"BYTES\"): void;\n  setHMACKey(key: ArrayBuffer, inputFormat: \"ARRAYBUFFER\"): void;\n  setHMACKey(key: Uint8Array, inputFormat: \"UINT8ARRAY\"): void;\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  setHMACKey(key: any, inputFormat: any, options?: any): void {\n    this.shaObj.setHMACKey(key, inputFormat, options);\n  }\n\n  /**\n   * Returns the the HMAC in the specified format using the key given by a previous `setHMACKey` call. Now deprecated\n   * in favor of just calling `getHash`.\n   *\n   * @param format The desired output formatting (B64, HEX, BYTES, ARRAYBUFFER, or UINT8ARRAY) as a string.\n   * @param options Options in the form of { outputUpper?: boolean; b64Pad?: string }. `outputUpper` is only for HEX\n   *   output (defaults to false) and `b64pad` is only for B64 output (defaults to \"=\").\n   * @returns The HMAC in the format specified.\n   */\n  getHMAC(format: \"HEX\", options?: { outputUpper?: boolean }): string;\n  getHMAC(format: \"B64\", options?: { b64Pad?: string }): string;\n  getHMAC(format: \"BYTES\"): string;\n  getHMAC(format: \"UINT8ARRAY\"): Uint8Array;\n  getHMAC(format: \"ARRAYBUFFER\"): ArrayBuffer;\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  getHMAC(format: any, options?: any): any {\n    return this.shaObj.getHMAC(format, options);\n  }\n}\n"], "names": ["b64Tab", "arraybuffer_error", "uint8array_error", "uint8array2packed", "arr", "existingPacked", "existingPackedLen", "bigEndianMod", "i", "intOffset", "byteOffset", "packed", "existingByteLen", "shiftModifier", "length", "push", "value", "binLen", "getStrConverter", "format", "utfType", "Error", "str", "existingBin", "existingBinLen", "num", "parseInt", "substr", "isNaN", "hex2packed", "codePnt", "codePntArr", "j", "transposeBytes", "byteCnt", "charCodeAt", "str2packed", "tmpInt", "strPart", "firstEqual", "indexOf", "search", "replace", "char<PERSON>t", "b642packed", "bytes2packed", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ignore", "Uint8Array", "arraybuffer2packed", "getOutputConverter", "outputBinLen", "outputOptions", "binarray", "outputLength", "formatOpts", "srcByte", "hex_tab", "toUpperCase", "packed2hex", "triplet", "int1", "int2", "packed2b64", "String", "fromCharCode", "packed2bytes", "retVal", "a<PERSON><PERSON><PERSON><PERSON>", "packed2arraybuffer", "packed2uint8array", "TWO_PWR_32", "K_sha2", "H_trunc", "H_full", "sha_variant_error", "mac_rounds_error", "packedLEConcat", "a", "b", "arrOffset", "aByteLen", "bByteLen", "leftShiftAmount", "rightShiftAmount", "pop", "concat", "getOutputOpts", "options", "outputUpper", "b64Pad", "outputLen", "lenErrstr", "parseInputOption", "key", "fallback", "errStr", "jsSHABase", "variant", "inputFormat", "inputOptions", "this", "numRounds", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "remainder", "remainderLen", "updateCalled", "processedLen", "macKeySet", "keyWithIPad", "keyWithOPad", "prototype", "update", "srcString", "updateProcessedLen", "variantBlockIntInc", "variantBlockSize", "convertRet", "converterFunc", "chunkBinLen", "chunk", "chunkIntLen", "intermediateState", "roundFunc", "slice", "getHash", "finalizedState", "isVariableLen", "formatFunc", "getMAC", "finalizeFunc", "stateCloneFunc", "newStateFunc", "setHMACKey", "HMACSupported", "keyConverterFunc", "_setHMACKey", "blockByteSize", "lastArrayIndex", "getHMAC", "_getHMAC", "firstHash", "extendStatics", "d", "Object", "setPrototypeOf", "__proto__", "Array", "p", "hasOwnProperty", "call", "__extends", "TypeError", "__", "constructor", "create", "rotl_32", "x", "n", "rotr_32", "shr_32", "parity_32", "y", "z", "ch_32", "maj_32", "sigma0_32", "safeAdd_32_2", "lsw", "safeAdd_32_4", "c", "safeAdd_32_5", "e", "gamma0_32", "sigma1_32", "getNewState", "_variant", "roundSHA1", "block", "H", "T", "t", "W", "finalizeSHA1", "remainderBinLen", "processedBinLen", "offset", "totalLen", "SuppressedError", "jsSHA", "_super", "_this", "resolvedOptions", "state", "getNewState256", "roundSHA256", "f", "g", "h", "T1", "T2", "finalizeSHA256", "Int_64", "msint_32", "lsint_32", "highOrder", "lowOrder", "rotl_64", "tmp", "rotr_64", "shr_64", "ch_64", "maj_64", "sigma0_64", "rotr28", "rotr34", "rotr39", "safeAdd_64_2", "msw", "safeAdd_64_4", "safeAdd_64_5", "xor_64_2", "gamma0_64", "rotr1", "rotr8", "shr7", "sigma1_64", "rotr14", "rotr18", "rotr41", "K_sha512", "getNewState512", "roundSHA512", "rotr19", "rotr61", "shr6", "finalizeSHA512", "rc_sha3", "r_sha3", "cloneSHA3State", "clone", "roundSHA3", "round", "B", "C", "D", "left_encode", "byte", "numEncodedBytes", "x_64", "encode_string", "input", "byte_pad", "outputByteLen", "encodedLen", "outputIntLen", "intsToAppend", "delimiter", "_initializeKMAC", "_getKMAC", "_initializeCSHAKE", "_processedBinLen", "blockSize", "temp", "state_offset", "binaryStringInc", "remainderIntLen", "finalizeSHA3", "funcNameOverride", "funcName", "customization", "resolveCSHAKEOptions", "packedParams", "byte_pad_out", "kmacKey", "resolveKMACOptions", "concated<PERSON><PERSON><PERSON>", "right_encode", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jsSHA1", "jsSHA256", "jsSHA512", "jsSHA3"], "mappings": ";;;;;;;;;;;;;;;;;;;;sOAIA,IAAMA,EAAS,mEAETC,EAAoB,gDACpBC,EAAmB,+CAyPzB,SAASC,EACPC,EACAC,EACAC,EACAC,GAEA,IAAIC,EAAGC,EAAWC,EAGZC,EAASN,GAAkB,CAAC,GAChCO,GAFFN,EAAoBA,GAAqB,KAEC,EACxCO,GAAkC,IAAlBN,EAAsB,EAAI,EAE5C,IAAKC,EAAI,EAAGA,EAAIJ,EAAIU,OAAQN,GAAK,EAE/BC,GADAC,EAAaF,EAAII,KACU,EACvBD,EAAOG,QAAUL,GACnBE,EAAOI,KAAK,GAEdJ,EAAOF,IAAcL,EAAII,IAAO,GAAKK,EAAgBN,GAAgBG,EAAa,IAGpF,MAAO,CAAEM,MAAOL,EAAQM,OAAqB,EAAbb,EAAIU,OAAaR,EACnD,UA4BgBY,EACdC,EACAC,EACAb,GAIA,OAAQa,GACN,IAAK,OAEL,IAAK,UAEL,IAAK,UAEH,MACF,QACE,MAAM,IAAIC,MAAM,8CAIpB,OAAQF,GACN,IAAK,MAOH,OAAO,SAAUG,EAAaC,EAAwBC,GACpD,OA9NR,SACEF,EACAjB,EACAC,EACAC,GAEA,IAAIC,EAAGiB,EAAKhB,EAAWC,EAEvB,GAAI,GAAMY,EAAIR,OAAS,EACrB,MAAM,IAAIO,MAAM,iDAIlB,IAAMV,EAASN,GAAkB,CAAC,GAChCO,GAFFN,EAAoBA,GAAqB,KAEC,EACxCO,GAAkC,IAAlBN,EAAsB,EAAI,EAE5C,IAAKC,EAAI,EAAGA,EAAIc,EAAIR,OAAQN,GAAK,EAAG,CAElC,GADAiB,EAAMC,SAASJ,EAAIK,OAAOnB,EAAG,GAAI,IAC5BoB,MAAMH,GAQT,MAAM,IAAIJ,MAAM,kDALhB,IADAZ,GADAC,GAAcF,IAAM,GAAKI,KACE,EACpBD,EAAOG,QAAUL,GACtBE,EAAOI,KAAK,GAEdJ,EAAOF,IAAcgB,GAAQ,GAAKZ,EAAgBN,GAAgBG,EAAa,GAIlF,CAED,MAAO,CAAEM,MAAOL,EAAQM,OAAqB,EAAbK,EAAIR,OAAaR,EACnD,CA8LeuB,CAAWP,EAAKC,EAAaC,EAAgBjB,EACtD,EACF,IAAK,OAOH,OAAO,SAAUe,EAAaC,EAAwBC,GACpD,OAnUR,SACEF,EACAF,EACAf,EACAC,EACAC,GAEA,IAAIuB,EACFC,EAEAvB,EACAwB,EACAvB,EACAC,EACAG,EACAoB,EANAC,EAAU,EASNvB,EAASN,GAAkB,CAAC,GAChCO,GAFFN,EAAoBA,GAAqB,KAEC,EAE1C,GAAI,SAAWc,EAEb,IADAP,GAAkC,IAAlBN,EAAsB,EAAI,EACrCC,EAAI,EAAGA,EAAIc,EAAIR,OAAQN,GAAK,EAsB/B,IApBAuB,EAAa,GAET,KAHJD,EAAUR,EAAIa,WAAW3B,IAIvBuB,EAAWhB,KAAKe,GACP,KAAQA,GACjBC,EAAWhB,KAAK,IAAQe,IAAY,GACpCC,EAAWhB,KAAK,IAAkB,GAAVe,IACf,MAASA,GAAW,OAAUA,EACvCC,EAAWhB,KAAK,IAAQe,IAAY,GAAK,IAASA,IAAY,EAAK,GAAO,IAAkB,GAAVA,IAElFtB,GAAK,EACLsB,EAAU,QAAuB,KAAVA,IAAoB,GAA2B,KAApBR,EAAIa,WAAW3B,IACjEuB,EAAWhB,KACT,IAAQe,IAAY,GACpB,IAASA,IAAY,GAAM,GAC3B,IAASA,IAAY,EAAK,GAC1B,IAAkB,GAAVA,IAIPE,EAAI,EAAGA,EAAID,EAAWjB,OAAQkB,GAAK,EAAG,CAGzC,IADAvB,GADAC,EAAawB,EAAUtB,KACI,EACpBD,EAAOG,QAAUL,GACtBE,EAAOI,KAAK,GAGdJ,EAAOF,IAAcsB,EAAWC,IAAO,GAAKnB,EAAgBN,GAAgBG,EAAa,IACzFwB,GAAW,CACZ,MASH,IALArB,GAAkC,IAAlBN,EAAsB,EAAI,EAI1C0B,EAAkB,YAAcb,GAA4B,IAAjBb,GAAwB,YAAca,GAA4B,IAAjBb,EACvFC,EAAI,EAAGA,EAAIc,EAAIR,OAAQN,GAAK,EAAG,CASlC,IARAsB,EAAUR,EAAIa,WAAW3B,IACF,IAAnByB,IAEFH,GADAE,EAAc,IAAVF,IACY,EAAMA,IAAY,GAIpCrB,GADAC,EAAawB,EAAUtB,KACI,EACpBD,EAAOG,QAAUL,GACtBE,EAAOI,KAAK,GAEdJ,EAAOF,IAAcqB,GAAY,GAAKjB,EAAgBN,GAAgBG,EAAa,IACnFwB,GAAW,CACZ,CAEH,MAAO,CAAElB,MAAOL,EAAQM,OAAkB,EAAViB,EAAc5B,EAChD,CAmPe8B,CAAWd,EAAKF,EAASG,EAAaC,EAAgBjB,EAC/D,EACF,IAAK,MAOH,OAAO,SAAUe,EAAaC,EAAwBC,GACpD,OAnKR,SACEF,EACAjB,EACAC,EACAC,GAEA,IAEEC,EACAwB,EACAK,EACAC,EACA7B,EACAC,EAPEwB,EAAU,EAURvB,EAASN,GAAkB,CAAC,GAChCO,GAFFN,EAAoBA,GAAqB,KAEC,EACxCO,GAAkC,IAAlBN,EAAsB,EAAI,EAC1CgC,EAAajB,EAAIkB,QAAQ,KAE3B,IAAK,IAAMlB,EAAImB,OAAO,qBACpB,MAAM,IAAIpB,MAAM,uCAIlB,GADAC,EAAMA,EAAIoB,QAAQ,KAAM,KACnB,IAAMH,GAAcA,EAAajB,EAAIR,OACxC,MAAM,IAAIO,MAAM,uCAGlB,IAAKb,EAAI,EAAGA,EAAIc,EAAIR,OAAQN,GAAK,EAAG,CAIlC,IAHA8B,EAAUhB,EAAIK,OAAOnB,EAAG,GACxB6B,EAAS,EAEJL,EAAI,EAAGA,EAAIM,EAAQxB,OAAQkB,GAAK,EAEnCK,GADQrC,EAAOwC,QAAQF,EAAQK,OAAOX,KAClB,GAAK,EAAIA,EAG/B,IAAKA,EAAI,EAAGA,EAAIM,EAAQxB,OAAS,EAAGkB,GAAK,EAAG,CAG1C,IADAvB,GADAC,EAAawB,EAAUtB,KACI,EACpBD,EAAOG,QAAUL,GACtBE,EAAOI,KAAK,GAEdJ,EAAOF,KACH4B,IAAY,GAAS,EAAJL,EAAU,MAAU,GAAKnB,EAAgBN,GAAgBG,EAAa,IAC3FwB,GAAW,CACZ,CACF,CAED,MAAO,CAAElB,MAAOL,EAAQM,OAAkB,EAAViB,EAAc5B,EAChD,CA+GesC,CAAWtB,EAAKC,EAAaC,EAAgBjB,EACtD,EACF,IAAK,QAOH,OAAO,SAAUe,EAAaC,EAAwBC,GACpD,OAjNR,SACEF,EACAjB,EACAC,EACAC,GAEA,IAAIuB,EAAStB,EAAGC,EAAWC,EAGrBC,EAASN,GAAkB,CAAC,GAChCO,GAFFN,EAAoBA,GAAqB,KAEC,EACxCO,GAAkC,IAAlBN,EAAsB,EAAI,EAE5C,IAAKC,EAAI,EAAGA,EAAIc,EAAIR,OAAQN,GAAK,EAC/BsB,EAAUR,EAAIa,WAAW3B,GAGzBC,GADAC,EAAaF,EAAII,KACU,EACvBD,EAAOG,QAAUL,GACnBE,EAAOI,KAAK,GAEdJ,EAAOF,IAAcqB,GAAY,GAAKjB,EAAgBN,GAAgBG,EAAa,IAGrF,MAAO,CAAEM,MAAOL,EAAQM,OAAqB,EAAbK,EAAIR,OAAaR,EACnD,CAwLeuC,CAAavB,EAAKC,EAAaC,EAAgBjB,EACxD,EACF,IAAK,cACH,IACE,IAAIuC,YAAY,EACjB,CAAC,MAAOC,GACP,MAAM,IAAI1B,MAAMpB,EACjB,CAOD,OAAO,SAAUG,EAAkBmB,EAAwBC,GACzD,OA3FR,SACEpB,EACAC,EACAC,EACAC,GAEA,OAAOJ,EAAkB,IAAI6C,WAAW5C,GAAMC,EAAgBC,EAAmBC,EACnF,CAoFe0C,CAAmB7C,EAAKmB,EAAaC,EAAgBjB,EAC9D,EACF,IAAK,aACH,IACE,IAAIyC,WAAW,EAChB,CAAC,MAAOD,GACP,MAAM,IAAI1B,MAAMnB,EACjB,CAOD,OAAO,SAAUE,EAAiBmB,EAAwBC,GACxD,OAAOrB,EAAkBC,EAAKmB,EAAaC,EAAgBjB,EAC7D,EACF,QACE,MAAM,IAAIc,MAAM,oEAEtB,CAgLM,SAAU6B,EAAmB/B,EAAagC,EAAmB5C,EAAmB6C,GACpF,OAAQjC,GACN,IAAK,MACH,OAAO,SAAUkC,GACf,OAtKF,SACJ1C,EACA2C,EACA/C,EACAgD,GAEA,IAEE/C,EACAgD,EAHIC,EAAU,mBACZnC,EAAM,GAIJR,EAASwC,EAAe,EAC5BzC,GAAkC,IAAlBN,EAAsB,EAAI,EAE5C,IAAKC,EAAI,EAAGA,EAAIM,EAAQN,GAAK,EAE3BgD,EAAU7C,EAAOH,IAAM,KAAQ,GAAKK,EAAgBN,GAAgBC,EAAI,IACxEc,GAAOmC,EAAQd,OAAQa,IAAY,EAAK,IAAOC,EAAQd,OAAiB,GAAVa,GAGhE,OAAOD,EAAwB,YAAIjC,EAAIoC,cAAgBpC,CACzD,CAiJeqC,CAAWN,EAAUF,EAAc5C,EAAc6C,EAC1D,EACF,IAAK,MACH,OAAO,SAAUC,GACf,OA1IF,SACJ1C,EACA2C,EACA/C,EACAgD,GAEA,IACE/C,EACAwB,EACA4B,EACAC,EACAC,EALExC,EAAM,GAOJR,EAASwC,EAAe,EAC5BzC,GAAkC,IAAlBN,EAAsB,EAAI,EAE5C,IAAKC,EAAI,EAAGA,EAAIM,EAAQN,GAAK,EAO3B,IANAqD,EAAOrD,EAAI,EAAIM,EAASH,EAAQH,EAAI,IAAO,GAAK,EAChDsD,EAAOtD,EAAI,EAAIM,EAASH,EAAQH,EAAI,IAAO,GAAK,EAChDoD,GACKjD,EAAOH,IAAM,KAAQ,GAAKK,EAAgBN,GAAgBC,EAAI,IAAQ,MAAS,IAC/EqD,IAAU,GAAKhD,EAAgBN,IAAiBC,EAAI,GAAK,IAAQ,MAAS,EAC3EsD,IAAU,GAAKjD,EAAgBN,IAAiBC,EAAI,GAAK,IAAQ,IAChEwB,EAAI,EAAGA,EAAI,EAAGA,GAAK,EAEpBV,GADM,EAAJd,EAAY,EAAJwB,GAASsB,EACZtD,EAAO2C,OAAQiB,IAAa,GAAK,EAAI5B,GAAO,IAE5CuB,EAAmB,OAIhC,OAAOjC,CACT,CA0GeyC,CAAWV,EAAUF,EAAc5C,EAAc6C,EAC1D,EACF,IAAK,QACH,OAAO,SAAUC,GACf,gBApGqB1C,EAAkB2C,EAAsB/C,GACnE,IACEC,EACAgD,EAFElC,EAAM,GAIJR,EAASwC,EAAe,EAC5BzC,GAAkC,IAAlBN,EAAsB,EAAI,EAE5C,IAAKC,EAAI,EAAGA,EAAIM,EAAQN,GAAK,EAC3BgD,EAAW7C,EAAOH,IAAM,KAAQ,GAAKK,EAAgBN,GAAgBC,EAAI,IAAQ,IACjFc,GAAO0C,OAAOC,aAAaT,GAG7B,OAAOlC,CACT,CAsFe4C,CAAab,EAAUF,EAAc5C,EAC9C,EACF,IAAK,cACH,IAEE,IAAIuC,YAAY,EACjB,CAAC,MAAOC,GACP,MAAM,IAAI1B,MAAMpB,EACjB,CACD,OAAO,SAAUoD,GACf,gBAtF2B1C,EAAkB2C,EAAsB/C,GACzE,IAAIC,EACEM,EAASwC,EAAe,EAC5Ba,EAAS,IAAIrB,YAAYhC,GACzBsD,EAAU,IAAIpB,WAAWmB,GACzBtD,GAAkC,IAAlBN,EAAsB,EAAI,EAE5C,IAAKC,EAAI,EAAGA,EAAIM,EAAQN,GAAK,EAC3B4D,EAAQ5D,GAAMG,EAAOH,IAAM,KAAQ,GAAKK,EAAgBN,GAAgBC,EAAI,IAAQ,IAGtF,OAAO2D,CACT,CA0EeE,CAAmBhB,EAAUF,EAAc5C,EACpD,EACF,IAAK,aACH,IAEE,IAAIyC,WAAW,EAChB,CAAC,MAAOD,GACP,MAAM,IAAI1B,MAAMnB,EACjB,CACD,OAAO,SAAUmD,GACf,gBA1E0B1C,EAAkB2C,EAAsB/C,GACxE,IAAIC,EACEM,EAASwC,EAAe,EAC5BzC,GAAkC,IAAlBN,EAAsB,EAAI,EAC1C4D,EAAS,IAAInB,WAAWlC,GAE1B,IAAKN,EAAI,EAAGA,EAAIM,EAAQN,GAAK,EAC3B2D,EAAO3D,GAAMG,EAAOH,IAAM,KAAQ,GAAKK,EAAgBN,GAAgBC,EAAI,IAAQ,IAGrF,OAAO2D,CACT,CA+DeG,CAAkBjB,EAAUF,EAAc5C,EACnD,EACF,QACE,MAAM,IAAIc,MAAM,8DAEtB,CC1lBO,IAAMkD,EAAa,WAGbC,EAAS,CACpB,WAAY,WAAY,WAAY,WAAY,UAAY,WAAY,WAAY,WAAY,WAChG,UAAY,UAAY,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,WAChG,UAAY,UAAY,UAAY,WAAY,WAAY,WAAY,WAAY,WAAY,WAChG,WAAY,WAAY,WAAY,UAAY,UAAY,UAAY,UAAY,WAAY,WAChG,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,WAChG,WAAY,WAAY,UAAY,UAAY,UAAY,UAAY,UAAY,UAAY,WAChG,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,WAChG,YAIWC,EAAU,CAAC,WAAY,UAAY,UAAY,WAAY,WAAY,WAAY,WAAY,YAG/FC,EAAS,CAAC,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,UAAY,YAE9FC,EAAoB,sCACpBC,EAAmB,gCAShB,SAAAC,EAAeC,EAAgBC,GAC7C,IAAIvE,EAAGwE,EACDC,EAAWH,EAAU,SAAM,EAC/BI,EAAWH,EAAU,SAAM,EAC3BI,EAAkBF,GAAY,EAC9BG,EAAoB,EAAIH,GAAa,EAGvC,GAAIA,EAAW,GAAM,EAAG,CACtB,IAAKzE,EAAI,EAAGA,EAAI0E,EAAU1E,GAAK,EAC7BwE,EAAaC,EAAWzE,IAAO,EAE/BsE,EAAS,MAAEE,IAAcD,EAAS,MAAEvE,IAAM,IAAM2E,EAChDL,EAAS,MAAE/D,KAAK,GAChB+D,EAAS,MAAEE,EAAY,IAAMD,EAAS,MAAEvE,IAAM,KAAO4E,EAUvD,OAJKN,EAAS,MAAEhE,QAAU,GAAK,GAAKoE,EAAWD,GAC7CH,EAAS,MAAEO,MAGN,CAAErE,MAAO8D,EAAS,MAAG7D,OAAQ6D,EAAU,OAAIC,EAAU,OAC7D,CACC,MAAO,CAAE/D,MAAO8D,EAAS,MAAEQ,OAAOP,EAAS,OAAI9D,OAAQ6D,EAAU,OAAIC,EAAU,OAEnF,CASM,SAAUQ,EAAcC,GAM5B,IAAMrB,EAAS,CAAEsB,aAAa,EAAOC,OAAQ,IAAKC,WAAY,GAC5DvC,EAAmGoC,GAAW,CAAA,EAC9GI,EAAY,wCAQd,GANAzB,EAAoB,YAAIf,EAA2B,cAAK,EAEpDA,EAAsB,SACxBe,EAAe,OAAIf,EAAsB,QAGvCA,EAAyB,UAAG,CAC9B,GAAIA,EAAyB,UAAI,GAAM,EACrC,MAAM,IAAI/B,MAAMuE,GAElBzB,EAAkB,UAAIf,EAAyB,SAChD,MAAM,GAAIA,EAAwB,SAAG,CACpC,GAAIA,EAAwB,SAAI,GAAM,EACpC,MAAM,IAAI/B,MAAMuE,GAElBzB,EAAkB,UAAIf,EAAwB,QAC/C,CAED,GAAI,kBAAqBe,EAAoB,YAC3C,MAAM,IAAI9C,MAAM,yCAGlB,GAAI,iBAAoB8C,EAAe,OACrC,MAAM,IAAI9C,MAAM,oCAGlB,OAAO8C,CACT,CAUM,SAAU0B,EACdC,EACA9E,EACAT,EACAwF,GAEA,IAAMC,EAASF,EAAM,mCACrB,IAAK9E,EAAO,CACV,IAAK+E,EACH,MAAM,IAAI1E,MAAM2E,GAElB,OAAOD,CACR,CAED,QAA8B,IAAnB/E,EAAa,QAAsBA,EAAc,OAC1D,MAAM,IAAIK,MAAM2E,GAGlB,OAAO9E,EACLF,EAAc,OAGdA,EAAgB,UAAK,OACrBT,EALKW,CAMLF,EAAa,MACjB,CAEA,IAAAiF,EAAA,WA+CE,SAAAA,EAAsBC,EAAcC,EAAkBX,GACpD,IAAMY,EAAeZ,GAAW,GAShC,GARAa,KAAKF,EAAcA,EAEnBE,KAAKjF,EAAUgF,EAAuB,UAAK,OAC3CC,KAAKC,UAAYF,EAAwB,WAAK,EAK1CxE,MAAMyE,KAAKC,YAAcD,KAAKC,YAAc5E,SAAS2E,KAAKC,UAAW,KAAO,EAAID,KAAKC,UACvF,MAAM,IAAIjF,MAAM,iCAGlBgF,KAAKE,EAAaL,EAClBG,KAAKG,EAAY,GACjBH,KAAKI,EAAe,EACpBJ,KAAKK,GAAe,EACpBL,KAAKM,EAAe,EACpBN,KAAKO,GAAY,EACjBP,KAAKQ,EAAc,GACnBR,KAAKS,EAAc,EACpB,CA8MH,OAtMEb,EAAMc,UAAAC,OAAN,SAAOC,GACL,IAAIzG,EACF0G,EAAqB,EACjBC,EAAqBd,KAAKe,IAAqB,EACnDC,EAAahB,KAAKiB,EAAcL,EAAWZ,KAAKG,EAAWH,KAAKI,GAChEc,EAAcF,EAAmB,OACjCG,EAAQH,EAAkB,MAC1BI,EAAcF,IAAgB,EAEhC,IAAK/G,EAAI,EAAGA,EAAIiH,EAAajH,GAAK2G,EAC5BD,EAAqBb,KAAKe,GAAoBG,IAChDlB,KAAKqB,EAAoBrB,KAAKsB,EAAUH,EAAMI,MAAMpH,EAAGA,EAAI2G,GAAqBd,KAAKqB,GACrFR,GAAsBb,KAAKe,GAQ/B,OALAf,KAAKM,GAAgBO,EACrBb,KAAKG,EAAYgB,EAAMI,MAAMV,IAAuB,GACpDb,KAAKI,EAAec,EAAclB,KAAKe,EACvCf,KAAKK,GAAe,EAEbL,MAiBTJ,EAAAc,UAAAc,QAAA,SAAQ1G,EAAaqE,GACnB,IAAIhF,EACFsH,EACA3E,EAAekD,KAAKlD,EAEhBC,EAAgBmC,EAAcC,GAEpC,GAAIa,KAAK0B,EAAe,CACtB,IAAoC,IAAhC3E,EAAyB,UAC3B,MAAM,IAAI/B,MAAM,8CAElB8B,EAAeC,EAAyB,SACzC,CAED,IAAM4E,EAAa9E,EAAmB/B,EAAQgC,EAAckD,KAAK9F,EAAc6C,GAC/E,GAAIiD,KAAKO,GAAaP,KAAK4B,EACzB,OAAOD,EAAW3B,KAAK4B,EAAO7E,IAUhC,IAPA0E,EAAiBzB,KAAK6B,EACpB7B,KAAKG,EAAUoB,QACfvB,KAAKI,EACLJ,KAAKM,EACLN,KAAK8B,EAAe9B,KAAKqB,GACzBvE,GAEG3C,EAAI,EAAGA,EAAI6F,KAAKC,UAAW9F,GAAK,EAE/B6F,KAAK0B,GAAiB5E,EAAe,IAAO,IAC9C2E,EAAeA,EAAehH,OAAS,IAAM,WAAgB,GAAMqC,EAAe,IAEpF2E,EAAiBzB,KAAK6B,EACpBJ,EACA3E,EACA,EACAkD,KAAK+B,EAAa/B,KAAKE,GACvBpD,GAIJ,OAAO6E,EAAWF,IAepB7B,EAAAc,UAAAsB,WAAA,SAAWvC,EAAUK,EAAkBX,GACrC,IAAKa,KAAKiC,EACR,MAAM,IAAIjH,MAAM,iCAGlB,GAAIgF,KAAKK,EACP,MAAM,IAAIrF,MAAM,2CAGlB,IACEkH,EAAmBrH,EAAgBiF,GADlBX,GAAW,IACyC,UAAK,OAAQa,KAAK9F,GAEzF8F,KAAKmC,EAAYD,EAAiBzC,KAQ1BG,EAAWc,UAAAyB,EAArB,SAAsB1C,GACpB,IAEItF,EAFEiI,EAAgBpC,KAAKe,IAAqB,EAC9CsB,EAAiBD,EAAgB,EAAI,EAEvC,GAAuB,IAAnBpC,KAAKC,UACP,MAAM,IAAIjF,MAAMuD,GAGlB,GAAIyB,KAAKO,EACP,MAAM,IAAIvF,MAAM,uBAclB,IATIoH,EAAgB3C,EAAY,OAAI,IAClCA,EAAW,MAAIO,KAAK6B,EAClBpC,EAAW,MACXA,EAAY,OACZ,EACAO,KAAK+B,EAAa/B,KAAKE,GACvBF,KAAKlD,IAGF2C,EAAW,MAAEhF,QAAU4H,GAC5B5C,EAAW,MAAE/E,KAAK,GAGpB,IAAKP,EAAI,EAAGA,GAAKkI,EAAgBlI,GAAK,EACpC6F,KAAKQ,EAAYrG,GAAuB,UAAlBsF,EAAW,MAAEtF,GACnC6F,KAAKS,EAAYtG,GAAuB,WAAlBsF,EAAW,MAAEtF,GAGrC6F,KAAKqB,EAAoBrB,KAAKsB,EAAUtB,KAAKQ,EAAaR,KAAKqB,GAC/DrB,KAAKM,EAAeN,KAAKe,EAEzBf,KAAKO,GAAY,GAgBnBX,EAAAc,UAAA4B,QAAA,SAAQxH,EAAaqE,GACnB,IAAMpC,EAAgBmC,EAAcC,GAGpC,OAFetC,EAAmB/B,EAAQkF,KAAKlD,EAAckD,KAAK9F,EAAc6C,EAEzE4E,CAAW3B,KAAKuC,MAMf3C,EAAAc,UAAA6B,EAAV,WACE,IAAId,EAEJ,IAAKzB,KAAKO,EACR,MAAM,IAAIvF,MAAM,qDAGlB,IAAMwH,EAAYxC,KAAK6B,EACrB7B,KAAKG,EAAUoB,QACfvB,KAAKI,EACLJ,KAAKM,EACLN,KAAK8B,EAAe9B,KAAKqB,GACzBrB,KAAKlD,GAWP,OATA2E,EAAiBzB,KAAKsB,EAAUtB,KAAKS,EAAaT,KAAK+B,EAAa/B,KAAKE,IACzEuB,EAAiBzB,KAAK6B,EACpBW,EACAxC,KAAKlD,EACLkD,KAAKe,EACLU,EACAzB,KAAKlD,IAKV8C,CAAD,IC5ZI6C,EAAgB,SAASC,EAAGhE,GAI5B,OAHA+D,EAAgBE,OAAOC,gBAClB,CAAEC,UAAW,cAAgBC,OAAS,SAAUJ,EAAGhE,GAAKgE,EAAEG,UAAYnE,CAAE,GACzE,SAAUgE,EAAGhE,GAAK,IAAK,IAAIqE,KAAKrE,EAAOiE,OAAOjC,UAAUsC,eAAeC,KAAKvE,EAAGqE,KAAIL,EAAEK,GAAKrE,EAAEqE,KACzFN,EAAcC,EAAGhE,EAC5B,EAEO,SAASwE,EAAUR,EAAGhE,GACzB,GAAiB,mBAANA,GAA0B,OAANA,EAC3B,MAAM,IAAIyE,UAAU,uBAAyBxF,OAAOe,GAAK,iCAE7D,SAAS0E,IAAOpD,KAAKqD,YAAcX,CAAI,CADvCD,EAAcC,EAAGhE,GAEjBgE,EAAEhC,UAAkB,OAANhC,EAAaiE,OAAOW,OAAO5E,IAAM0E,EAAG1C,UAAYhC,EAAEgC,UAAW,IAAI0C,EACnF,CCbgB,SAAAG,EAAQC,EAAWC,GACjC,OAAQD,GAAKC,EAAMD,IAAO,GAAKC,CACjC,CASA,SAASC,EAAQF,EAAWC,GAC1B,OAAQD,IAAMC,EAAMD,GAAM,GAAKC,CACjC,CASA,SAASE,EAAOH,EAAWC,GACzB,OAAOD,IAAMC,CACf,UAUgBG,EAAUJ,EAAWK,EAAWC,GAC9C,OAAON,EAAIK,EAAIC,CACjB,UAUgBC,EAAMP,EAAWK,EAAWC,GAC1C,OAAQN,EAAIK,GAAOL,EAAIM,CACzB,UAUgBE,EAAOR,EAAWK,EAAWC,GAC3C,OAAQN,EAAIK,EAAML,EAAIM,EAAMD,EAAIC,CAClC,CAQM,SAAUG,EAAUT,GACxB,OAAOE,EAAQF,EAAG,GAAKE,EAAQF,EAAG,IAAME,EAAQF,EAAG,GACrD,CAWgB,SAAAU,EAAazF,EAAWC,GACtC,IAAMyF,GAAW,MAAJ1F,IAAmB,MAAJC,GAG5B,OAAe,OAFND,IAAM,KAAOC,IAAM,KAAOyF,IAAQ,MAEjB,GAAa,MAANA,CACnC,CAaM,SAAUC,EAAa3F,EAAWC,EAAW2F,EAAW3B,GAC5D,IAAMyB,GAAW,MAAJ1F,IAAmB,MAAJC,IAAmB,MAAJ2F,IAAmB,MAAJ3B,GAG1D,OAAe,OAFNjE,IAAM,KAAOC,IAAM,KAAO2F,IAAM,KAAO3B,IAAM,KAAOyB,IAAQ,MAE3C,GAAa,MAANA,CACnC,CAcM,SAAUG,EAAa7F,EAAWC,EAAW2F,EAAW3B,EAAW6B,GACvE,IAAMJ,GAAW,MAAJ1F,IAAmB,MAAJC,IAAmB,MAAJ2F,IAAmB,MAAJ3B,IAAmB,MAAJ6B,GAGzE,OAAe,OAFN9F,IAAM,KAAOC,IAAM,KAAO2F,IAAM,KAAO3B,IAAM,KAAO6B,IAAM,KAAOJ,IAAQ,MAExD,GAAa,MAANA,CACnC,CAkBM,SAAUK,EAAUhB,GACxB,OAAOE,EAAQF,EAAG,GAAKE,EAAQF,EAAG,IAAMG,EAAOH,EAAG,EACpD,CAQM,SAAUiB,EAAUjB,GACxB,OAAOE,EAAQF,EAAG,GAAKE,EAAQF,EAAG,IAAME,EAAQF,EAAG,GACrD,CCzJA,SAASkB,EAAYC,GACnB,MAAO,CAAC,WAAY,WAAY,WAAY,UAAY,WAC1D,CASA,SAASC,EAAUC,EAAiBC,GAClC,IAAIrG,EAAGC,EAAG2F,EAAG3B,EAAG6B,EAAGQ,EAAGC,EAChBC,EAAc,GAQpB,IANAxG,EAAIqG,EAAE,GACNpG,EAAIoG,EAAE,GACNT,EAAIS,EAAE,GACNpC,EAAIoC,EAAE,GACNP,EAAIO,EAAE,GAEDE,EAAI,EAAGA,EAAI,GAAIA,GAAK,EAErBC,EAAED,GADAA,EAAI,GACCH,EAAMG,GAENzB,EAAQ0B,EAAED,EAAI,GAAKC,EAAED,EAAI,GAAKC,EAAED,EAAI,IAAMC,EAAED,EAAI,IAAK,GAI5DD,EADEC,EAAI,GACFV,EAAaf,EAAQ9E,EAAG,GAAIsF,EAAMrF,EAAG2F,EAAG3B,GAAI6B,EAAG,WAAYU,EAAED,IACxDA,EAAI,GACTV,EAAaf,EAAQ9E,EAAG,GAAImF,EAAUlF,EAAG2F,EAAG3B,GAAI6B,EAAG,WAAYU,EAAED,IAC5DA,EAAI,GACTV,EAAaf,EAAQ9E,EAAG,GAAIuF,EAAOtF,EAAG2F,EAAG3B,GAAI6B,EAAG,WAAYU,EAAED,IAE9DV,EAAaf,EAAQ9E,EAAG,GAAImF,EAAUlF,EAAG2F,EAAG3B,GAAI6B,EAAG,WAAYU,EAAED,IAGvET,EAAI7B,EACJA,EAAI2B,EACJA,EAAId,EAAQ7E,EAAG,IACfA,EAAID,EACJA,EAAIsG,EASN,OANAD,EAAE,GAAKZ,EAAazF,EAAGqG,EAAE,IACzBA,EAAE,GAAKZ,EAAaxF,EAAGoG,EAAE,IACzBA,EAAE,GAAKZ,EAAaG,EAAGS,EAAE,IACzBA,EAAE,GAAKZ,EAAaxB,EAAGoC,EAAE,IACzBA,EAAE,GAAKZ,EAAaK,EAAGO,EAAE,IAElBA,CACT,CAWA,SAASI,EAAa/E,EAAqBgF,EAAyBC,EAAyBN,GAS3F,IARA,IAAI3K,EAMEkL,EAAiD,IAArCF,EAAkB,KAAQ,GAAM,GAChDG,EAAWH,EAAkBC,EACxBjF,EAAU1F,QAAU4K,GACzBlF,EAAUzF,KAAK,GAiBjB,IAdAyF,EAAUgF,IAAoB,IAAM,KAAS,GAAMA,EAAkB,GAOrEhF,EAAUkF,GAAqB,WAAXC,EAIpBnF,EAAUkF,EAAS,GAAMC,EAAWpH,EAAc,EAG7C/D,EAAI,EAAGA,EAAIgG,EAAU1F,OAAQN,GAAK,GACrC2K,EAAIF,EAAUzE,EAAUoB,MAAMpH,EAAGA,EAAI,IAAK2K,GAG5C,OAAOA,CACT,CF4MkD,mBAApBS,iBAAiCA,gBE1M/D,IAAAC,EAAA,SAAAC,GAmBE,SAAAD,EAAY3F,EAAcC,EAAkBX,GAA5C,IA2BCuG,EAAA1F,KA1BC,GAAI,UAAYH,EACd,MAAM,IAAI7E,MAAMsD,GAGlB,IAAMqH,EAAkBxG,GAAW,UADnCuG,EAAAD,EAAAxC,KAAAjD,KAAMH,EAASC,EAAaX,IAAQa,MAG/BiC,GAAgB,EAErByD,EAAK9D,EAAS8D,EAAKnD,EACnBmD,EAAKxL,GAAgB,EACrBwL,EAAKzE,EAAgBpG,EAAgB6K,EAAK5F,EAAa4F,EAAK3K,EAAS2K,EAAKxL,GAC1EwL,EAAKpE,EAAYsD,EACjBc,EAAK5D,EAAiB,SAAU8D,GAC9B,OAAOA,EAAMrE,OACf,EACAmE,EAAK3D,EAAe2C,EACpBgB,EAAK7D,EAAeqD,EAEpBQ,EAAKrE,EAtIA,CAAC,WAAY,WAAY,WAAY,UAAY,YAuItDqE,EAAK3E,EAAmB,IACxB2E,EAAK5I,EAAe,IACpB4I,EAAKhE,GAAgB,EAEjBiE,EAAyB,SAC3BD,EAAKvD,EAAY3C,EAAiB,UAAWmG,EAAyB,QAAGD,EAAKxL,KAEjF,CACH,OA/CmCgJ,EAA4BsC,EAAAC,GA+C9DD,CAAD,CA/CA,CAAmC5F,GCrFnC,SAASiG,EAAehG,GAStB,MANI,WAAaA,EACNzB,EAAQmD,QAGRlD,EAAOkD,OAGpB,CASA,SAASuE,EAAYjB,EAAiBC,GACpC,IAAIrG,EAAGC,EAAG2F,EAAG3B,EAAG6B,EAAGwB,EAAGC,EAAGC,EAAGC,EAAIC,EAAInB,EFmGZxB,EEjGlByB,EAAc,GAWpB,IATAxG,EAAIqG,EAAE,GACNpG,EAAIoG,EAAE,GACNT,EAAIS,EAAE,GACNpC,EAAIoC,EAAE,GACNP,EAAIO,EAAE,GACNiB,EAAIjB,EAAE,GACNkB,EAAIlB,EAAE,GACNmB,EAAInB,EAAE,GAEDE,EAAI,EAAGA,EAAI,GAAIA,GAAK,EAErBC,EAAED,GADAA,EAAI,GACCH,EAAMG,GAENZ,EFmFJV,EADiBF,EElFUyB,EAAED,EAAI,GFmFtB,IAAMtB,EAAQF,EAAG,IAAMG,EAAOH,EAAG,IEnFNyB,EAAED,EAAI,GAAIR,EAAUS,EAAED,EAAI,KAAMC,EAAED,EAAI,KAEjFkB,EAAK5B,EAAa2B,EAAGxB,EAAUF,GAAIR,EAAMQ,EAAGwB,EAAGC,GAAI7H,EAAO6G,GAAIC,EAAED,IAChEmB,EAAKjC,EAAaD,EAAUxF,GAAIuF,EAAOvF,EAAGC,EAAG2F,IAC7C4B,EAAID,EACJA,EAAID,EACJA,EAAIxB,EACJA,EAAIL,EAAaxB,EAAGwD,GACpBxD,EAAI2B,EACJA,EAAI3F,EACJA,EAAID,EACJA,EAAIyF,EAAagC,EAAIC,GAYvB,OATArB,EAAE,GAAKZ,EAAazF,EAAGqG,EAAE,IACzBA,EAAE,GAAKZ,EAAaxF,EAAGoG,EAAE,IACzBA,EAAE,GAAKZ,EAAaG,EAAGS,EAAE,IACzBA,EAAE,GAAKZ,EAAaxB,EAAGoC,EAAE,IACzBA,EAAE,GAAKZ,EAAaK,EAAGO,EAAE,IACzBA,EAAE,GAAKZ,EAAa6B,EAAGjB,EAAE,IACzBA,EAAE,GAAKZ,EAAa8B,EAAGlB,EAAE,IACzBA,EAAE,GAAKZ,EAAa+B,EAAGnB,EAAE,IAElBA,CACT,CAyDA,IAAAU,EAAA,SAAAC,GAmBE,SAAAD,EAAY3F,EAAcC,EAAkBX,GAA5C,IA8BCuG,EAAA1F,KA7BC,GAAM,YAAcH,GAAW,YAAcA,EAC3C,MAAM,IAAI7E,MAAMsD,GAGlB,IAAMqH,EAAkBxG,GAAW,UADnCuG,EAAAD,EAAAxC,KAAAjD,KAAMH,EAASC,EAAaX,IAAQa,MAI/B4B,EAAS8D,EAAKnD,EACnBmD,EAAKzD,GAAgB,EACrByD,EAAKxL,GAAgB,EACrBwL,EAAKzE,EAAgBpG,EAAgB6K,EAAK5F,EAAa4F,EAAK3K,EAAS2K,EAAKxL,GAC1EwL,EAAKpE,EAAYwE,EACjBJ,EAAK5D,EAAiB,SAAU8D,GAC9B,OAAOA,EAAMrE,OACf,EAEAmE,EAAK3D,EAAe8D,EACpBH,EAAK7D,EAAe,SAAU1B,EAAWgF,EAAiBC,EAAiBN,GACzE,OAnFN,SACE3E,EACAgF,EACAC,EACAN,EACAjF,GAYA,IAVA,IAAI1F,EAMEkL,EAAiD,IAArCF,EAAkB,KAAQ,GAAM,GAEhDG,EAAWH,EAAkBC,EAExBjF,EAAU1F,QAAU4K,GACzBlF,EAAUzF,KAAK,GAcjB,IAXAyF,EAAUgF,IAAoB,IAAM,KAAS,GAAMA,EAAkB,GAKrEhF,EAAUkF,GAAqB,WAAXC,EAGpBnF,EAAUkF,EAAS,GAAMC,EAAWpH,EAAc,EAG7C/D,EAAI,EAAGA,EAAIgG,EAAU1F,OAAQN,GAlBd,GAmBlB2K,EAAIgB,EAAY3F,EAAUoB,MAAMpH,EAAGA,EAnBjB,IAmBuC2K,GAU3D,MAPI,YAAcjF,EACP,CAACiF,EAAE,GAAIA,EAAE,GAAIA,EAAE,GAAIA,EAAE,GAAIA,EAAE,GAAIA,EAAE,GAAIA,EAAE,IAGvCA,CAIb,CAuCasB,CAAejG,EAAWgF,EAAiBC,EAAiBN,EAAGjF,EACxE,EAEA6F,EAAKrE,EAAoBwE,EAAehG,GACxC6F,EAAK3E,EAAmB,IACxB2E,EAAK5I,EAAe,YAAc+C,EAAU,IAAM,IAClD6F,EAAKhE,GAAgB,EAEjBiE,EAAyB,SAC3BD,EAAKvD,EAAY3C,EAAiB,UAAWmG,EAAyB,QAAGD,EAAKxL,KAEjF,CACH,OAlDmCgJ,EAAgCsC,EAAAC,GAkDlED,CAAD,CAlDA,CAAmC5F,GCtInCyG,EAOE,SAAYC,EAAkBC,GAC5BvG,KAAKwG,EAAYF,EACjBtG,KAAKyG,EAAWF,CACjB,EAYa,SAAAG,EAAQlD,EAAWC,GACjC,IAAIkD,EACJ,OAAIlD,EAAI,IACNkD,EAAM,GAAKlD,EACJ,IAAI4C,EAAQ7C,EAAEiD,GAAYhD,EAAMD,EAAEgD,IAAcG,EAAOnD,EAAEgD,GAAa/C,EAAMD,EAAEiD,IAAaE,IACzF,IAAMlD,GACfkD,EAAM,GAAKlD,EACJ,IAAI4C,EAAQ7C,EAAEgD,GAAa/C,EAAMD,EAAEiD,IAAaE,EAAOnD,EAAEiD,GAAYhD,EAAMD,EAAEgD,IAAcG,IAE3FnD,CAEX,CAWA,SAASoD,EAAQpD,EAAWC,GAC1B,IAAIkD,EACJ,OAAIlD,EAAI,IACNkD,EAAM,GAAKlD,EACJ,IAAI4C,EAAQ7C,EAAEgD,IAAc/C,EAAMD,EAAEiD,GAAYE,EAAOnD,EAAEiD,IAAahD,EAAMD,EAAEgD,GAAaG,KAElGA,EAAM,GAAKlD,EACJ,IAAI4C,EAAQ7C,EAAEiD,IAAahD,EAAMD,EAAEgD,GAAaG,EAAOnD,EAAEgD,IAAc/C,EAAMD,EAAEiD,GAAYE,GAEtG,CAWA,SAASE,EAAOrD,EAAWC,GACzB,OAAO,IAAI4C,EAAO7C,EAAEgD,IAAc/C,EAAID,EAAEiD,IAAahD,EAAMD,EAAEgD,GAAc,GAAK/C,EAClF,UAUgBqD,EAAMtD,EAAWK,EAAWC,GAC1C,OAAO,IAAIuC,EACR7C,EAAEgD,EAAY3C,EAAE2C,GAAehD,EAAEgD,EAAY1C,EAAE0C,EAC/ChD,EAAEiD,EAAW5C,EAAE4C,GAAcjD,EAAEiD,EAAW3C,EAAE2C,EAEjD,UAUgBM,EAAOvD,EAAWK,EAAWC,GAC3C,OAAO,IAAIuC,EACR7C,EAAEgD,EAAY3C,EAAE2C,EAAchD,EAAEgD,EAAY1C,EAAE0C,EAAc3C,EAAE2C,EAAY1C,EAAE0C,EAC5EhD,EAAEiD,EAAW5C,EAAE4C,EAAajD,EAAEiD,EAAW3C,EAAE2C,EAAa5C,EAAE4C,EAAW3C,EAAE2C,EAE5E,CAQM,SAAUO,EAAUxD,GACxB,IAAMyD,EAASL,EAAQpD,EAAG,IACxB0D,EAASN,EAAQpD,EAAG,IACpB2D,EAASP,EAAQpD,EAAG,IAEtB,OAAO,IAAI6C,EACTY,EAAOT,EAAYU,EAAOV,EAAYW,EAAOX,EAC7CS,EAAOR,EAAWS,EAAOT,EAAWU,EAAOV,EAE/C,CASgB,SAAAW,EAAa5D,EAAWK,GACtC,IAAIM,EAAKkD,EAETlD,GAAoB,MAAbX,EAAEiD,IAAmC,MAAb5C,EAAE4C,GAEjC,IAAMA,GAAmB,OADzBY,GAAO7D,EAAEiD,IAAa,KAAO5C,EAAE4C,IAAa,KAAOtC,IAAQ,OACvB,GAAa,MAANA,EAM3C,OAJAA,GAAqB,MAAdX,EAAEgD,IAAqC,MAAd3C,EAAE2C,IAAuBa,IAAQ,IACjEA,GAAO7D,EAAEgD,IAAc,KAAO3C,EAAE2C,IAAc,KAAOrC,IAAQ,IAGtD,IAAIkC,GAFe,MAANgB,IAAiB,GAAa,MAANlD,EAEfsC,EAC/B,CAWM,SAAUa,EAAa7I,EAAWC,EAAW2F,EAAW3B,GAC5D,IAAIyB,EAAKkD,EAETlD,GAAoB,MAAb1F,EAAEgI,IAAmC,MAAb/H,EAAE+H,IAAmC,MAAbpC,EAAEoC,IAAmC,MAAb/D,EAAE+D,GAEjF,IAAMA,GAAmB,OADzBY,GAAO5I,EAAEgI,IAAa,KAAO/H,EAAE+H,IAAa,KAAOpC,EAAEoC,IAAa,KAAO/D,EAAE+D,IAAa,KAAOtC,IAAQ,OACnE,GAAa,MAANA,EAO3C,OALAA,GACiB,MAAd1F,EAAE+H,IAAqC,MAAd9H,EAAE8H,IAAqC,MAAdnC,EAAEmC,IAAqC,MAAd9D,EAAE8D,IAAuBa,IAAQ,IAC/GA,GAAO5I,EAAE+H,IAAc,KAAO9H,EAAE8H,IAAc,KAAOnC,EAAEmC,IAAc,KAAO9D,EAAE8D,IAAc,KAAOrC,IAAQ,IAGpG,IAAIkC,GAFe,MAANgB,IAAiB,GAAa,MAANlD,EAEfsC,EAC/B,CAYM,SAAUc,EAAa9I,EAAWC,EAAW2F,EAAW3B,EAAW6B,GACvE,IAAIJ,EAAKkD,EAETlD,GACgB,MAAb1F,EAAEgI,IACW,MAAb/H,EAAE+H,IACW,MAAbpC,EAAEoC,IACW,MAAb/D,EAAE+D,IACW,MAAblC,EAAEkC,GAQL,IAAMA,GAAmB,OAPzBY,GACG5I,EAAEgI,IAAa,KACf/H,EAAE+H,IAAa,KACfpC,EAAEoC,IAAa,KACf/D,EAAE+D,IAAa,KACflC,EAAEkC,IAAa,KACftC,IAAQ,OACyB,GAAa,MAANA,EAkB3C,OAhBAA,GACiB,MAAd1F,EAAE+H,IACY,MAAd9H,EAAE8H,IACY,MAAdnC,EAAEmC,IACY,MAAd9D,EAAE8D,IACY,MAAdjC,EAAEiC,IACFa,IAAQ,IACXA,GACG5I,EAAE+H,IAAc,KAChB9H,EAAE8H,IAAc,KAChBnC,EAAEmC,IAAc,KAChB9D,EAAE8D,IAAc,KAChBjC,EAAEiC,IAAc,KAChBrC,IAAQ,IAGJ,IAAIkC,GAFe,MAANgB,IAAiB,GAAa,MAANlD,EAEfsC,EAC/B,CASgB,SAAAe,EAAS/I,EAAWC,GAClC,OAAO,IAAI2H,EAAO5H,EAAE+H,EAAY9H,EAAE8H,EAAW/H,EAAEgI,EAAW/H,EAAE+H,EAC9D,CA0CM,SAAUgB,EAAUjE,GACxB,IAAMkE,EAAQd,EAAQpD,EAAG,GACvBmE,EAAQf,EAAQpD,EAAG,GACnBoE,EAAOf,EAAOrD,EAAG,GAEnB,OAAO,IAAI6C,EACTqB,EAAMlB,EAAYmB,EAAMnB,EAAYoB,EAAKpB,EACzCkB,EAAMjB,EAAWkB,EAAMlB,EAAWmB,EAAKnB,EAE3C,CAQM,SAAUoB,EAAUrE,GACxB,IAAMsE,EAASlB,EAAQpD,EAAG,IACxBuE,EAASnB,EAAQpD,EAAG,IACpBwE,EAASpB,EAAQpD,EAAG,IAEtB,OAAO,IAAI6C,EACTyB,EAAOtB,EAAYuB,EAAOvB,EAAYwB,EAAOxB,EAC7CsB,EAAOrB,EAAWsB,EAAOtB,EAAWuB,EAAOvB,EAE/C,CCjRA,IAAMwB,EAAW,CACf,IAAI5B,EAAOlI,EAAO,GAAI,YACtB,IAAIkI,EAAOlI,EAAO,GAAI,WACtB,IAAIkI,EAAOlI,EAAO,GAAI,YACtB,IAAIkI,EAAOlI,EAAO,GAAI,YACtB,IAAIkI,EAAOlI,EAAO,GAAI,YACtB,IAAIkI,EAAOlI,EAAO,GAAI,YACtB,IAAIkI,EAAOlI,EAAO,GAAI,YACtB,IAAIkI,EAAOlI,EAAO,GAAI,YACtB,IAAIkI,EAAOlI,EAAO,GAAI,YACtB,IAAIkI,EAAOlI,EAAO,GAAI,YACtB,IAAIkI,EAAOlI,EAAO,IAAK,YACvB,IAAIkI,EAAOlI,EAAO,IAAK,YACvB,IAAIkI,EAAOlI,EAAO,IAAK,YACvB,IAAIkI,EAAOlI,EAAO,IAAK,WACvB,IAAIkI,EAAOlI,EAAO,IAAK,WACvB,IAAIkI,EAAOlI,EAAO,IAAK,YACvB,IAAIkI,EAAOlI,EAAO,IAAK,YACvB,IAAIkI,EAAOlI,EAAO,IAAK,WACvB,IAAIkI,EAAOlI,EAAO,IAAK,YACvB,IAAIkI,EAAOlI,EAAO,IAAK,YACvB,IAAIkI,EAAOlI,EAAO,IAAK,YACvB,IAAIkI,EAAOlI,EAAO,IAAK,YACvB,IAAIkI,EAAOlI,EAAO,IAAK,YACvB,IAAIkI,EAAOlI,EAAO,IAAK,YACvB,IAAIkI,EAAOlI,EAAO,IAAK,YACvB,IAAIkI,EAAOlI,EAAO,IAAK,WACvB,IAAIkI,EAAOlI,EAAO,IAAK,YACvB,IAAIkI,EAAOlI,EAAO,IAAK,YACvB,IAAIkI,EAAOlI,EAAO,IAAK,YACvB,IAAIkI,EAAOlI,EAAO,IAAK,YACvB,IAAIkI,EAAOlI,EAAO,IAAK,YACvB,IAAIkI,EAAOlI,EAAO,IAAK,WACvB,IAAIkI,EAAOlI,EAAO,IAAK,YACvB,IAAIkI,EAAOlI,EAAO,IAAK,YACvB,IAAIkI,EAAOlI,EAAO,IAAK,YACvB,IAAIkI,EAAOlI,EAAO,IAAK,YACvB,IAAIkI,EAAOlI,EAAO,IAAK,YACvB,IAAIkI,EAAOlI,EAAO,IAAK,YACvB,IAAIkI,EAAOlI,EAAO,IAAK,YACvB,IAAIkI,EAAOlI,EAAO,IAAK,WACvB,IAAIkI,EAAOlI,EAAO,IAAK,YACvB,IAAIkI,EAAOlI,EAAO,IAAK,YACvB,IAAIkI,EAAOlI,EAAO,IAAK,YACvB,IAAIkI,EAAOlI,EAAO,IAAK,WACvB,IAAIkI,EAAOlI,EAAO,IAAK,YACvB,IAAIkI,EAAOlI,EAAO,IAAK,YACvB,IAAIkI,EAAOlI,EAAO,IAAK,YACvB,IAAIkI,EAAOlI,EAAO,IAAK,WACvB,IAAIkI,EAAOlI,EAAO,IAAK,YACvB,IAAIkI,EAAOlI,EAAO,IAAK,YACvB,IAAIkI,EAAOlI,EAAO,IAAK,YACvB,IAAIkI,EAAOlI,EAAO,IAAK,YACvB,IAAIkI,EAAOlI,EAAO,IAAK,YACvB,IAAIkI,EAAOlI,EAAO,IAAK,YACvB,IAAIkI,EAAOlI,EAAO,IAAK,YACvB,IAAIkI,EAAOlI,EAAO,IAAK,YACvB,IAAIkI,EAAOlI,EAAO,IAAK,YACvB,IAAIkI,EAAOlI,EAAO,IAAK,YACvB,IAAIkI,EAAOlI,EAAO,IAAK,YACvB,IAAIkI,EAAOlI,EAAO,IAAK,WACvB,IAAIkI,EAAOlI,EAAO,IAAK,WACvB,IAAIkI,EAAOlI,EAAO,IAAK,YACvB,IAAIkI,EAAOlI,EAAO,IAAK,YACvB,IAAIkI,EAAOlI,EAAO,IAAK,YACvB,IAAIkI,EAAO,WAAY,YACvB,IAAIA,EAAO,WAAY,WACvB,IAAIA,EAAO,WAAY,YACvB,IAAIA,EAAO,WAAY,YACvB,IAAIA,EAAO,UAAY,YACvB,IAAIA,EAAO,UAAY,YACvB,IAAIA,EAAO,UAAY,YACvB,IAAIA,EAAO,UAAY,WACvB,IAAIA,EAAO,UAAY,WACvB,IAAIA,EAAO,UAAY,YACvB,IAAIA,EAAO,WAAY,WACvB,IAAIA,EAAO,WAAY,YACvB,IAAIA,EAAO,WAAY,YACvB,IAAIA,EAAO,WAAY,YACvB,IAAIA,EAAO,WAAY,WACvB,IAAIA,EAAO,WAAY,aASzB,SAAS6B,EAAerI,GACtB,MAAI,YAAcA,EACT,CACL,IAAIwG,EAAO,WAAYjI,EAAQ,IAC/B,IAAIiI,EAAO,WAAajI,EAAQ,IAChC,IAAIiI,EAAO,WAAYjI,EAAQ,IAC/B,IAAIiI,EAAO,UAAajI,EAAQ,IAChC,IAAIiI,EAAO,WAAYjI,EAAQ,IAC/B,IAAIiI,EAAO,YAAajI,EAAQ,IAChC,IAAIiI,EAAO,WAAYjI,EAAQ,IAC/B,IAAIiI,EAAO,WAAajI,EAAQ,KAI3B,CACL,IAAIiI,EAAOhI,EAAO,GAAI,YACtB,IAAIgI,EAAOhI,EAAO,GAAI,YACtB,IAAIgI,EAAOhI,EAAO,GAAI,YACtB,IAAIgI,EAAOhI,EAAO,GAAI,YACtB,IAAIgI,EAAOhI,EAAO,GAAI,YACtB,IAAIgI,EAAOhI,EAAO,GAAI,WACtB,IAAIgI,EAAOhI,EAAO,GAAI,YACtB,IAAIgI,EAAOhI,EAAO,GAAI,WAG5B,CASA,SAAS8J,EAAYtD,EAAiBC,GACpC,IAAIrG,EAAGC,EAAG2F,EAAG3B,EAAG6B,EAAGwB,EAAGC,EAAGC,EAAGC,EAAIC,EAAInB,EAAGK,ED0Gf7B,EAClB4E,EACJC,EACAC,EC3GIrD,EAAc,GAWpB,IATAxG,EAAIqG,EAAE,GACNpG,EAAIoG,EAAE,GACNT,EAAIS,EAAE,GACNpC,EAAIoC,EAAE,GACNP,EAAIO,EAAE,GACNiB,EAAIjB,EAAE,GACNkB,EAAIlB,EAAE,GACNmB,EAAInB,EAAE,GAEDE,EAAI,EAAGA,EAAI,GAAIA,GAAK,EACnBA,EAAI,IACNK,EAAa,EAAJL,EACTC,EAAED,GAAK,IAAIqB,EAAOxB,EAAMQ,GAASR,EAAMQ,EAAS,KAEhDJ,EAAED,GAAKsC,GDwFa9D,ECxFUyB,EAAED,EAAI,GDyFlCoD,SACJC,SACAC,SAFIF,EAASxB,EAAQpD,EAAG,IACxB6E,EAASzB,EAAQpD,EAAG,IACpB8E,EAAOzB,EAAOrD,EAAG,GAEZ,IAAI6C,EACT+B,EAAO5B,EAAY6B,EAAO7B,EAAY8B,EAAK9B,EAC3C4B,EAAO3B,EAAW4B,EAAO5B,EAAW6B,EAAK7B,IC/FExB,EAAED,EAAI,GAAIyC,EAAUxC,EAAED,EAAI,KAAMC,EAAED,EAAI,KAEjFkB,EAAKqB,EAAatB,EAAG4B,EAAUtD,GAAIuC,EAAMvC,EAAGwB,EAAGC,GAAIiC,EAASjD,GAAIC,EAAED,IAClEmB,EAAKiB,EAAaJ,EAAUvI,GAAIsI,EAAOtI,EAAGC,EAAG2F,IAC7C4B,EAAID,EACJA,EAAID,EACJA,EAAIxB,EACJA,EAAI6C,EAAa1E,EAAGwD,GACpBxD,EAAI2B,EACJA,EAAI3F,EACJA,EAAID,EACJA,EAAI2I,EAAalB,EAAIC,GAYvB,OATArB,EAAE,GAAKsC,EAAa3I,EAAGqG,EAAE,IACzBA,EAAE,GAAKsC,EAAa1I,EAAGoG,EAAE,IACzBA,EAAE,GAAKsC,EAAa/C,EAAGS,EAAE,IACzBA,EAAE,GAAKsC,EAAa1E,EAAGoC,EAAE,IACzBA,EAAE,GAAKsC,EAAa7C,EAAGO,EAAE,IACzBA,EAAE,GAAKsC,EAAarB,EAAGjB,EAAE,IACzBA,EAAE,GAAKsC,EAAapB,EAAGlB,EAAE,IACzBA,EAAE,GAAKsC,EAAanB,EAAGnB,EAAE,IAElBA,CACT,CAwFA,IAAAU,EAAA,SAAAC,GAmBE,SAAAD,EAAY3F,EAAcC,EAAkBX,GAA5C,IA6BCuG,EAAA1F,KA5BC,GAAM,YAAcH,GAAW,YAAcA,EAC3C,MAAM,IAAI7E,MAAMsD,GAGlB,IAAMqH,EAAkBxG,GAAW,UADnCuG,EAAAD,EAAAxC,KAAAjD,KAAMH,EAASC,EAAaX,IAAQa,MAI/B4B,EAAS8D,EAAKnD,EACnBmD,EAAKzD,GAAgB,EACrByD,EAAKxL,GAAgB,EACrBwL,EAAKzE,EAAgBpG,EAAgB6K,EAAK5F,EAAa4F,EAAK3K,EAAS2K,EAAKxL,GAC1EwL,EAAKpE,EAAY6G,EACjBzC,EAAK5D,EAAiB,SAAU8D,GAC9B,OAAOA,EAAMrE,OACf,EACAmE,EAAK3D,EAAemG,EACpBxC,EAAK7D,EAAe,SAAU1B,EAAWgF,EAAiBC,EAAiBN,GACzE,OAjHN,SACE3E,EACAgF,EACAC,EACAN,EACAjF,GAYA,IAVA,IAAI1F,EAMEkL,EAAmD,IAAvCF,EAAkB,MAAS,IAAO,GAElDG,EAAWH,EAAkBC,EAExBjF,EAAU1F,QAAU4K,GACzBlF,EAAUzF,KAAK,GAcjB,IAXAyF,EAAUgF,IAAoB,IAAM,KAAS,GAAMA,EAAkB,GAKrEhF,EAAUkF,GAAqB,WAAXC,EAGpBnF,EAAUkF,EAAS,GAAMC,EAAWpH,EAAc,EAG7C/D,EAAI,EAAGA,EAAIgG,EAAU1F,OAAQN,GAlBd,GAmBlB2K,EAAIqD,EAAYhI,EAAUoB,MAAMpH,EAAGA,EAnBjB,IAmBuC2K,GAwC3D,MArCI,YAAcjF,EAEP,CACPiF,EAAE,GAAG0B,EACL1B,EAAE,GAAG2B,EACL3B,EAAE,GAAG0B,EACL1B,EAAE,GAAG2B,EACL3B,EAAE,GAAG0B,EACL1B,EAAE,GAAG2B,EACL3B,EAAE,GAAG0B,EACL1B,EAAE,GAAG2B,EACL3B,EAAE,GAAG0B,EACL1B,EAAE,GAAG2B,EACL3B,EAAE,GAAG0B,EACL1B,EAAE,GAAG2B,GAIE,CACP3B,EAAE,GAAG0B,EACL1B,EAAE,GAAG2B,EACL3B,EAAE,GAAG0B,EACL1B,EAAE,GAAG2B,EACL3B,EAAE,GAAG0B,EACL1B,EAAE,GAAG2B,EACL3B,EAAE,GAAG0B,EACL1B,EAAE,GAAG2B,EACL3B,EAAE,GAAG0B,EACL1B,EAAE,GAAG2B,EACL3B,EAAE,GAAG0B,EACL1B,EAAE,GAAG2B,EACL3B,EAAE,GAAG0B,EACL1B,EAAE,GAAG2B,EACL3B,EAAE,GAAG0B,EACL1B,EAAE,GAAG2B,EAIX,CAuCa8B,CAAepI,EAAWgF,EAAiBC,EAAiBN,EAAGjF,EACxE,EAEA6F,EAAKrE,EAAoB6G,EAAerI,GACxC6F,EAAK3E,EAAmB,KACxB2E,EAAK5I,EAAe,YAAc+C,EAAU,IAAM,IAClD6F,EAAKhE,GAAgB,EAEjBiE,EAAyB,SAC3BD,EAAKvD,EAAY3C,EAAiB,UAAWmG,EAAyB,QAAGD,EAAKxL,KAEjF,CACH,OAjDmCgJ,EAAgCsC,EAAAC,GAiDlED,CAAD,CAjDA,CAAmC5F,GC/P7B4I,GAAU,CACd,IAAInC,EAAO,EAAY,GACvB,IAAIA,EAAO,EAAY,OACvB,IAAIA,EAAO,WAAY,OACvB,IAAIA,EAAO,WAAY,YACvB,IAAIA,EAAO,EAAY,OACvB,IAAIA,EAAO,EAAY,YACvB,IAAIA,EAAO,WAAY,YACvB,IAAIA,EAAO,WAAY,OACvB,IAAIA,EAAO,EAAY,KACvB,IAAIA,EAAO,EAAY,KACvB,IAAIA,EAAO,EAAY,YACvB,IAAIA,EAAO,EAAY,YACvB,IAAIA,EAAO,EAAY,YACvB,IAAIA,EAAO,WAAY,KACvB,IAAIA,EAAO,WAAY,OACvB,IAAIA,EAAO,WAAY,OACvB,IAAIA,EAAO,WAAY,OACvB,IAAIA,EAAO,WAAY,KACvB,IAAIA,EAAO,EAAY,OACvB,IAAIA,EAAO,WAAY,YACvB,IAAIA,EAAO,WAAY,YACvB,IAAIA,EAAO,WAAY,OACvB,IAAIA,EAAO,EAAY,YACvB,IAAIA,EAAO,WAAY,aAGnBoC,GAAS,CACb,CAAC,EAAG,GAAI,EAAG,GAAI,IACf,CAAC,EAAG,GAAI,GAAI,GAAI,GAChB,CAAC,GAAI,EAAG,GAAI,GAAI,IAChB,CAAC,GAAI,GAAI,GAAI,GAAI,IACjB,CAAC,GAAI,GAAI,GAAI,EAAG,KASlB,SAAS/D,GAAYC,GACnB,IAAIxK,EACE2D,EAAS,GAEf,IAAK3D,EAAI,EAAGA,EAAI,EAAGA,GAAK,EACtB2D,EAAO3D,GAAK,CAAC,IAAIkM,EAAO,EAAG,GAAI,IAAIA,EAAO,EAAG,GAAI,IAAIA,EAAO,EAAG,GAAI,IAAIA,EAAO,EAAG,GAAI,IAAIA,EAAO,EAAG,IAGrG,OAAOvI,CACT,CAQA,SAAS4K,GAAe9C,GACtB,IAAIzL,EACEwO,EAAQ,GACd,IAAKxO,EAAI,EAAGA,EAAI,EAAGA,GAAK,EACtBwO,EAAMxO,GAAKyL,EAAMzL,GAAGoH,QAGtB,OAAOoH,CACT,CASA,SAASC,GAAU/D,EAAwBe,GACzC,IAAIiD,EAAOrF,EAAGK,EAAGiF,EF8IMrK,EAAWC,EAAW2F,EAAW3B,EAAW6B,EE7I7DwE,EAAI,GACRC,EAAI,GAEN,GAAI,OAASnE,EACX,IAAKrB,EAAI,EAAGA,EAAIqB,EAAMpK,OAAQ+I,GAAK,EACjCoC,GAAOpC,IAAM,GAAK,IAAKA,IAAM,GAAK,EAAK,GAAKgE,EAC1C5B,GAAOpC,IAAM,GAAK,IAAKA,IAAM,GAAK,EAAK,GACvC,IAAI6C,EAAOxB,EAAMrB,EAAI,GAAIqB,EAAMrB,KAKrC,IAAKqF,EAAQ,EAAGA,EAAQ,GAAIA,GAAS,EAAG,CAKtC,IAHAC,EAAIpE,KAGClB,EAAI,EAAGA,EAAI,EAAGA,GAAK,EACtBuF,EAAEvF,IF2HiB/E,EE3HHmH,EAAMpC,GAAG,GF2HK9E,EE3HDkH,EAAMpC,GAAG,GF2HGa,EE3HCuB,EAAMpC,GAAG,GF2HCd,EE3HGkD,EAAMpC,GAAG,GF2HDe,EE3HKqB,EAAMpC,GAAG,GF4H1E,IAAI6C,EACT5H,EAAE+H,EAAY9H,EAAE8H,EAAYnC,EAAEmC,EAAY9D,EAAE8D,EAAYjC,EAAEiC,EAC1D/H,EAAEgI,EAAW/H,EAAE+H,EAAWpC,EAAEoC,EAAW/D,EAAE+D,EAAWlC,EAAEkC,IE5HtD,IAAKjD,EAAI,EAAGA,EAAI,EAAGA,GAAK,EACtBwF,EAAExF,GAAKgE,EAASuB,GAAGvF,EAAI,GAAK,GAAIkD,EAAQqC,GAAGvF,EAAI,GAAK,GAAI,IAE1D,IAAKA,EAAI,EAAGA,EAAI,EAAGA,GAAK,EACtB,IAAKK,EAAI,EAAGA,EAAI,EAAGA,GAAK,EACtB+B,EAAMpC,GAAGK,GAAK2D,EAAS5B,EAAMpC,GAAGK,GAAImF,EAAExF,IAK1C,IAAKA,EAAI,EAAGA,EAAI,EAAGA,GAAK,EACtB,IAAKK,EAAI,EAAGA,EAAI,EAAGA,GAAK,EACtBiF,EAAEjF,IAAI,EAAIL,EAAI,EAAIK,GAAK,GAAK6C,EAAQd,EAAMpC,GAAGK,GAAI4E,GAAOjF,GAAGK,IAK/D,IAAKL,EAAI,EAAGA,EAAI,EAAGA,GAAK,EACtB,IAAKK,EAAI,EAAGA,EAAI,EAAGA,GAAK,EACtB+B,EAAMpC,GAAGK,GAAK2D,EACZsB,EAAEtF,GAAGK,GACL,IAAIwC,GACDyC,GAAGtF,EAAI,GAAK,GAAGK,GAAG2C,EAAYsC,GAAGtF,EAAI,GAAK,GAAGK,GAAG2C,GAChDsC,GAAGtF,EAAI,GAAK,GAAGK,GAAG4C,EAAWqC,GAAGtF,EAAI,GAAK,GAAGK,GAAG4C,IAOxDb,EAAM,GAAG,GAAK4B,EAAS5B,EAAM,GAAG,GAAI4C,GAAQK,GAC7C,CAED,OAAOjD,CACT,CA6EA,SAASqD,GAAYzF,GACnB,IAAInJ,EACF6O,EACAC,EAAkB,EAEdrL,EAAS,CAAC,EAAG,GACjBsL,EAAO,CAAK,WAAJ5F,EAAiBA,EAAItF,EAAc,SAE7C,IAAK7D,EAAa,EAAGA,GAAc,EAAGA,IAMvB,KAJb6O,EAAQE,EAAK/O,GAAc,KAAQ,EAAIA,EAAe,MAIhB,IAApB8O,IAChBrL,EAAQqL,EAAkB,GAAM,IAAMD,GAAiC,GAAvBC,EAAkB,GAClEA,GAAmB,GAMvB,OAHAA,EAAsC,IAApBA,EAAwBA,EAAkB,EAC5DrL,EAAO,IAAMqL,EAEN,CAAExO,MAAOwO,EAAkB,EAAI,EAAIrL,EAAS,CAACA,EAAO,IAAKlD,OAAQ,EAAsB,EAAlBuO,EAC9E,CAuCA,SAASE,GAAcC,GACrB,OAAO9K,EAAeyK,GAAYK,EAAc,QAAIA,EACtD,CASA,SAASC,GAASjP,EAAqBkP,GACrC,IACErP,EADEsP,EAAaR,GAAYO,GAIvBE,EAAeF,IAAkB,EACrCG,GAAgBD,GAFlBD,EAAajL,EAAeiL,EAAYnP,IAEY,MAAEG,OAASiP,GAAiBA,EAEhF,IAAKvP,EAAI,EAAGA,EAAIwP,EAAcxP,IAC5BsP,EAAkB,MAAE/O,KAAK,GAG3B,OAAO+O,EAAkB,KAC3B,CAgCA,IAAAjE,GAAA,SAAAC,GAmCE,SAAAD,EAAY3F,EAAcC,EAAkBX,GAA5C,IAmICuG,EAAA1F,KAlIK4J,EAAY,EACd7I,EAAmB,EAEf4E,EAAkBxG,GAAW,GAInC,GAAuB,KALvBuG,EAAAD,EAAAxC,KAAAjD,KAAMH,EAASC,EAAaX,IAAQa,MAK3BC,UAAiB,CACxB,GAAI0F,EAAyB,SAAKA,EAAyB,QACzD,MAAM,IAAI3K,MAAMuD,GACX,GAAwB,cAApBmH,EAAKxF,GAAkD,cAApBwF,EAAKxF,EACjD,MAAM,IAAIlF,MAAM,2CAEnB,CAUD,OARA0K,EAAKxL,EAAe,EACpBwL,EAAKzE,EAAgBpG,EAAgB6K,EAAK5F,EAAa4F,EAAK3K,EAAS2K,EAAKxL,GAC1EwL,EAAKpE,EAAYsH,GACjBlD,EAAK5D,EAAiB4G,GACtBhD,EAAK3D,EAAe2C,GACpBgB,EAAKrE,EAAoBqD,KAEzBgB,EAAKhE,GAAgB,EACb7B,GACN,IAAK,WACH6F,EAAK3E,EAAmBA,EAAmB,KAC3C2E,EAAK5I,EAAe,IACpB4I,EAAKzD,GAAgB,EAErByD,EAAK9D,EAAS8D,EAAKnD,EACnB,MACF,IAAK,WACHmD,EAAK3E,EAAmBA,EAAmB,KAC3C2E,EAAK5I,EAAe,IACpB4I,EAAKzD,GAAgB,EAErByD,EAAK9D,EAAS8D,EAAKnD,EACnB,MACF,IAAK,WACHmD,EAAK3E,EAAmBA,EAAmB,IAC3C2E,EAAK5I,EAAe,IACpB4I,EAAKzD,GAAgB,EAErByD,EAAK9D,EAAS8D,EAAKnD,EACnB,MACF,IAAK,WACHmD,EAAK3E,EAAmBA,EAAmB,IAC3C2E,EAAK5I,EAAe,IACpB4I,EAAKzD,GAAgB,EAErByD,EAAK9D,EAAS8D,EAAKnD,EACnB,MACF,IAAK,WACHqH,EAAY,GACZlE,EAAK3E,EAAmBA,EAAmB,KAE3C2E,EAAK5I,GAAgB,EACrB4I,EAAKhE,GAAgB,EACrBgE,EAAKzD,GAAgB,EACrByD,EAAK9D,EAAS,KACd,MACF,IAAK,WACHgI,EAAY,GACZlE,EAAK3E,EAAmBA,EAAmB,KAE3C2E,EAAK5I,GAAgB,EACrB4I,EAAKhE,GAAgB,EACrBgE,EAAKzD,GAAgB,EACrByD,EAAK9D,EAAS,KACd,MACF,IAAK,UACHgI,EAAY,EACZlE,EAAK3E,EAAmBA,EAAmB,KAC3C2E,EAAKmE,EAAgB1K,GAErBuG,EAAK5I,GAAgB,EACrB4I,EAAKhE,GAAgB,EACrBgE,EAAKzD,GAAgB,EAErByD,EAAK9D,EAAS8D,EAAKoE,EACnB,MACF,IAAK,UACHF,EAAY,EACZlE,EAAK3E,EAAmBA,EAAmB,KAC3C2E,EAAKmE,EAAgB1K,GAErBuG,EAAK5I,GAAgB,EACrB4I,EAAKhE,GAAgB,EACrBgE,EAAKzD,GAAgB,EAErByD,EAAK9D,EAAS8D,EAAKoE,EACnB,MACF,IAAK,YACHpE,EAAK3E,EAAmBA,EAAmB,KAC3C6I,EAAYlE,EAAKqE,EAAkB5K,GAEnCuG,EAAK5I,GAAgB,EACrB4I,EAAKhE,GAAgB,EACrBgE,EAAKzD,GAAgB,EACrByD,EAAK9D,EAAS,KACd,MACF,IAAK,YACH8D,EAAK3E,EAAmBA,EAAmB,KAC3C6I,EAAYlE,EAAKqE,EAAkB5K,GAEnCuG,EAAK5I,GAAgB,EACrB4I,EAAKhE,GAAgB,EACrBgE,EAAKzD,GAAgB,EACrByD,EAAK9D,EAAS,KACd,MACF,QACE,MAAM,IAAI5G,MAAMsD,UAIpBoH,EAAK7D,EAAe,SAAU1B,EAAWgF,EAAiBC,EAAiBQ,EAAO9I,GAChF,OA7UN,SACEqD,EACAgF,EACA6E,EACApE,EACAqE,EACAL,EACAtK,GAEA,IAAInF,EAEF+P,EADAC,EAAe,EAEXrM,EAAS,GACbsM,EAAkBH,IAAc,EAChCI,EAAkBlF,IAAoB,EAKxC,IAAKhL,EAAI,EAAGA,EAAIkQ,GAAmBlF,GAAmB8E,EAAW9P,GAAKiQ,EACpExE,EAAQgD,GAAUzI,EAAUoB,MAAMpH,EAAGA,EAAIiQ,GAAkBxE,GAC3DT,GAAmB8E,EAOrB,IAJA9J,EAAYA,EAAUoB,MAAMpH,GAC5BgL,GAAoC8E,EAG7B9J,EAAU1F,OAAS2P,GACxBjK,EAAUzF,KAAK,GAUjB,IALAyF,GADAhG,EAAIgL,IAAoB,IACT,IAAMyE,GAAmBzP,EAAI,EAAT,EAEnCgG,EAAUiK,EAAkB,IAAM,WAClCxE,EAAQgD,GAAUzI,EAAWyF,GAEN,GAAhB9H,EAAOrD,OAAc6E,IAC1B4K,EAAOtE,EAAMuE,EAAe,GAAIA,EAAe,EAAK,GACpDrM,EAAOpD,KAAKwP,EAAKzD,KACG,GAAhB3I,EAAOrD,QAAe6E,KAG1BxB,EAAOpD,KAAKwP,EAAK1D,GAGb,GAAsB,IAF1B2D,GAAgB,GAEgBF,IAC9BrB,GAAU,KAAMhD,GAChBuE,EAAe,GAInB,OAAOrM,CACT,CAsRawM,CACLnK,EACAgF,EACAC,EACAQ,EACA7E,EACA6I,EACA9M,EAEJ,EAEI6I,EAAyB,SAC3BD,EAAKvD,EAAY3C,EAAiB,UAAWmG,EAAyB,QAAGD,EAAKxL,KAEjF,CA6EH,OAnPmCgJ,EAAkCsC,EAAAC,GA+KzDD,EAAA9E,UAAAqJ,EAAV,SAA4B5K,EAAuCoL,GACjE,IAAM5E,EAzMV,SAA8BxG,GAC5B,IAAMwG,EAAkBxG,GAAW,GAEnC,MAAO,CACLqL,SAAUhL,EAAiB,WAAYmG,EAA0B,SAAG,EAAG,CAAEhL,MAAO,GAAIC,OAAQ,IAC5F6P,cAAejL,EAAiB,gBAAiBmG,EAA+B,cAAG,EAAG,CAAEhL,MAAO,GAAIC,OAAQ,IAE/G,CAkM4B8P,CAAqBvL,GAAW,CAAE,GACtDoL,IACF5E,EAA0B,SAAI4E,GAEhC,IAAMI,EAAenM,EACnB6K,GAAc1D,EAA0B,UACxC0D,GAAc1D,EAA+B,gBAK/C,GAAmD,IAA/CA,EAA+B,cAAU,QAAqD,IAA1CA,EAA0B,SAAU,OAAS,CAEnG,IADA,IAAMiF,EAAerB,GAASoB,EAAc3K,KAAKe,IAAqB,GAC7D5G,EAAI,EAAGA,EAAIyQ,EAAanQ,OAAQN,GAAK6F,KAAKe,IAAqB,EACtEf,KAAKqB,EAAoBrB,KAAKsB,EAC5BsJ,EAAarJ,MAAMpH,EAAGA,GAAK6F,KAAKe,IAAqB,IACrDf,KAAKqB,GAEPrB,KAAKM,GAAgBN,KAAKe,EAE5B,OAAO,CACR,CACC,OAAO,IASDyE,EAAe9E,UAAAmJ,EAAzB,SAA0B1K,GACxB,IAAMwG,EA3NV,SAA4BxG,GAC1B,IAAMwG,EAAkBxG,GAAW,GAEnC,MAAO,CACL0L,QAASrL,EAAiB,UAAWmG,EAAyB,QAAG,GAEjE6E,SAAU,CAAE7P,MAAO,CAAC,YAAaC,OAAQ,IACzC6P,cAAejL,EAAiB,gBAAiBmG,EAA+B,cAAG,EAAG,CAAEhL,MAAO,GAAIC,OAAQ,IAE/G,CAkN4BkQ,CAAmB3L,GAAW,CAAE,GAExDa,KAAK+J,EAAkB5K,EAASwG,EAA0B,UAE1D,IADA,IAAMiF,EAAerB,GAASF,GAAc1D,EAAyB,SAAI3F,KAAKe,IAAqB,GAC1F5G,EAAI,EAAGA,EAAIyQ,EAAanQ,OAAQN,GAAK6F,KAAKe,IAAqB,EACtEf,KAAKqB,EAAoBrB,KAAKsB,EAC5BsJ,EAAarJ,MAAMpH,EAAGA,GAAK6F,KAAKe,IAAqB,IACrDf,KAAKqB,GAEPrB,KAAKM,GAAgBN,KAAKe,EAE5Bf,KAAKO,GAAY,GAUTiF,EAAQ9E,UAAAoJ,EAAlB,SAAmB3K,GACjB,IAAM4L,EAAoBvM,EACxB,CAAE7D,MAAOqF,KAAKG,EAAUoB,QAAS3G,OAAQoF,KAAKI,GA9TpD,SAAsBoD,GACpB,IAAInJ,EACF6O,EACAC,EAAkB,EAEdrL,EAAS,CAAC,EAAG,GACjBsL,EAAO,CAAK,WAAJ5F,EAAiBA,EAAItF,EAAc,SAE7C,IAAK7D,EAAa,EAAGA,GAAc,EAAGA,IAMvB,IAJb6O,EAAQE,EAAK/O,GAAc,KAAQ,EAAIA,EAAe,MAIhB,IAApB8O,IAChBrL,EAAOqL,GAAmB,IAAMD,GAA2B,EAAlBC,EACzCA,GAAmB,GAMvB,OAFArL,GADAqL,EAAsC,IAApBA,EAAwBA,EAAkB,IAClC,IAAMA,GAAsC,EAAlBA,EAE7C,CAAExO,MAAOwO,EAAkB,EAAI,EAAIrL,EAAS,CAACA,EAAO,IAAKlD,OAAQ,EAAsB,EAAlBuO,EAC9E,CAwSM6B,CAAa7L,EAAmB,YAGlC,OAAOa,KAAK6B,EACVkJ,EAAyB,MACzBA,EAA0B,OAC1B/K,KAAKM,EACLN,KAAK8B,EAAe9B,KAAKqB,GACzBlC,EAAmB,YAGxBqG,CAAD,CAnPA,CAAmC5F,UC/TnC,WAgCE,SAAA4F,EAAY3F,EAAcC,EAAkBX,GAC1C,GAAI,SAAWU,EACbG,KAAKiL,EAAS,IAAIC,EAAOrL,EAASC,EAAaX,QAC1C,GAAI,WAAaU,GAAW,WAAaA,EAC9CG,KAAKiL,EAAS,IAAIE,EAAStL,EAASC,EAAaX,QAC5C,GAAI,WAAaU,GAAW,WAAaA,EAC9CG,KAAKiL,EAAS,IAAIG,EAASvL,EAASC,EAAaX,OAC5C,IACL,YAAcU,GACd,YAAcA,GACd,YAAcA,GACd,YAAcA,GACd,YAAcA,GACd,YAAcA,GACd,aAAeA,GACf,aAAeA,GACf,WAAaA,GACb,WAAaA,EAIb,MAAM,IAAI7E,MAAMsD,GAFhB0B,KAAKiL,EAAS,IAAII,GAAOxL,EAASC,EAAaX,EAGhD,CACF,CAsEH,OA9DEqG,EAAM9E,UAAAC,OAAN,SAAO2I,GAGL,OAFAtJ,KAAKiL,EAAOtK,OAAO2I,GAEZtJ,MAmBTwF,EAAA9E,UAAAc,QAAA,SAAQ1G,EAAaqE,GACnB,OAAOa,KAAKiL,EAAOzJ,QAAQ1G,EAAQqE,IAiBrCqG,EAAA9E,UAAAsB,WAAA,SAAWvC,EAAUK,EAAkBX,GACrCa,KAAKiL,EAAOjJ,WAAWvC,EAAKK,EAAaX,IAkB3CqG,EAAA9E,UAAA4B,QAAA,SAAQxH,EAAaqE,GACnB,OAAOa,KAAKiL,EAAO3I,QAAQxH,EAAQqE,IAEtCqG,CAAD", "x_google_ignoreList": [2]}