/**
 * A JavaScript implementation of the SHA family of hashes - defined in FIPS PUB 180-4, FIPS PUB 202,
 * and SP 800-185 - as well as the corresponding HMAC implementation as defined in FIPS PUB 198-1.
 *
 * Copyright 2008-2023 <PERSON>, 1998-2009 <PERSON> & Contributors
 * Distributed under the BSD License
 * See http://caligatio.github.com/jsSHA/ for more information
 *
 * Two ECMAScript polyfill functions carry the following license:
 *
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except in compliance with
 * the License. You may obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0
 *
 * THIS CODE IS PROVIDED ON AN *AS IS* BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED,
 * INCLUDING WITHOUT LIMITATION ANY IMPLIED WARRANTIES OR CONDITIONS OF TITLE, FITNESS FOR A PARTICULAR PURPOSE,
 * MERCHANTABLITY OR NON-INFRINGEMENT.
 *
 * See the Apache Version 2.0 License for specific language governing permissions and limitations under the License.
 */
!function(r,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):(r="undefined"!=typeof globalThis?globalThis:r||self).jsSHA=t()}(this,(function(){"use strict";var r=function(t,n){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,t){r.__proto__=t}||function(r,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(r[n]=t[n])},r(t,n)};"function"==typeof SuppressedError&&SuppressedError;var t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",n="ARRAYBUFFER not supported by this environment",i="UINT8ARRAY not supported by this environment";function e(r,t,n,i){var e,o,u,s=t||[0],f=(n=n||0)>>>3,h=-1===i?3:0;for(e=0;e<r.length;e+=1)o=(u=e+f)>>>2,s.length<=o&&s.push(0),s[o]|=r[e]<<8*(h+i*(u%4));return{value:s,binLen:8*r.length+n}}function o(r,o,u){switch(o){case"UTF8":case"UTF16BE":case"UTF16LE":break;default:throw new Error("encoding must be UTF8, UTF16BE, or UTF16LE")}switch(r){case"HEX":return function(r,t,n){return function(r,t,n,i){var e,o,u,s;if(0!=r.length%2)throw new Error("String of HEX type must be in byte increments");var f=t||[0],h=(n=n||0)>>>3,a=-1===i?3:0;for(e=0;e<r.length;e+=2){if(o=parseInt(r.substr(e,2),16),isNaN(o))throw new Error("String of HEX type contains invalid characters");for(u=(s=(e>>>1)+h)>>>2;f.length<=u;)f.push(0);f[u]|=o<<8*(a+i*(s%4))}return{value:f,binLen:4*r.length+n}}(r,t,n,u)};case"TEXT":return function(r,t,n){return function(r,t,n,i,e){var o,u,s,f,h,a,c,w,E=0,v=n||[0],l=(i=i||0)>>>3;if("UTF8"===t)for(c=-1===e?3:0,s=0;s<r.length;s+=1)for(u=[],128>(o=r.charCodeAt(s))?u.push(o):2048>o?(u.push(192|o>>>6),u.push(128|63&o)):55296>o||57344<=o?u.push(224|o>>>12,128|o>>>6&63,128|63&o):(s+=1,o=65536+((1023&o)<<10|1023&r.charCodeAt(s)),u.push(240|o>>>18,128|o>>>12&63,128|o>>>6&63,128|63&o)),f=0;f<u.length;f+=1){for(h=(a=E+l)>>>2;v.length<=h;)v.push(0);v[h]|=u[f]<<8*(c+e*(a%4)),E+=1}else for(c=-1===e?2:0,w="UTF16LE"===t&&1!==e||"UTF16LE"!==t&&1===e,s=0;s<r.length;s+=1){for(o=r.charCodeAt(s),!0===w&&(o=(f=255&o)<<8|o>>>8),h=(a=E+l)>>>2;v.length<=h;)v.push(0);v[h]|=o<<8*(c+e*(a%4)),E+=2}return{value:v,binLen:8*E+i}}(r,o,t,n,u)};case"B64":return function(r,n,i){return function(r,n,i,e){var o,u,s,f,h,a,c=0,w=n||[0],E=(i=i||0)>>>3,v=-1===e?3:0,l=r.indexOf("=");if(-1===r.search(/^[a-zA-Z0-9=+/]+$/))throw new Error("Invalid character in base-64 string");if(r=r.replace(/=/g,""),-1!==l&&l<r.length)throw new Error("Invalid '=' found in base-64 string");for(o=0;o<r.length;o+=4){for(f=r.substr(o,4),s=0,u=0;u<f.length;u+=1)s|=t.indexOf(f.charAt(u))<<18-6*u;for(u=0;u<f.length-1;u+=1){for(h=(a=c+E)>>>2;w.length<=h;)w.push(0);w[h]|=(s>>>16-8*u&255)<<8*(v+e*(a%4)),c+=1}}return{value:w,binLen:8*c+i}}(r,n,i,u)};case"BYTES":return function(r,t,n){return function(r,t,n,i){var e,o,u,s,f=t||[0],h=(n=n||0)>>>3,a=-1===i?3:0;for(o=0;o<r.length;o+=1)e=r.charCodeAt(o),u=(s=o+h)>>>2,f.length<=u&&f.push(0),f[u]|=e<<8*(a+i*(s%4));return{value:f,binLen:8*r.length+n}}(r,t,n,u)};case"ARRAYBUFFER":try{new ArrayBuffer(0)}catch(r){throw new Error(n)}return function(r,t,n){return function(r,t,n,i){return e(new Uint8Array(r),t,n,i)}(r,t,n,u)};case"UINT8ARRAY":try{new Uint8Array(0)}catch(r){throw new Error(i)}return function(r,t,n){return e(r,t,n,u)};default:throw new Error("format must be HEX, TEXT, B64, BYTES, ARRAYBUFFER, or UINT8ARRAY")}}function u(r,e,o,u){switch(r){case"HEX":return function(r){return function(r,t,n,i){var e,o,u="0123456789abcdef",s="",f=t/8,h=-1===n?3:0;for(e=0;e<f;e+=1)o=r[e>>>2]>>>8*(h+n*(e%4)),s+=u.charAt(o>>>4&15)+u.charAt(15&o);return i.outputUpper?s.toUpperCase():s}(r,e,o,u)};case"B64":return function(r){return function(r,n,i,e){var o,u,s,f,h,a="",c=n/8,w=-1===i?3:0;for(o=0;o<c;o+=3)for(f=o+1<c?r[o+1>>>2]:0,h=o+2<c?r[o+2>>>2]:0,s=(r[o>>>2]>>>8*(w+i*(o%4))&255)<<16|(f>>>8*(w+i*((o+1)%4))&255)<<8|h>>>8*(w+i*((o+2)%4))&255,u=0;u<4;u+=1)a+=8*o+6*u<=n?t.charAt(s>>>6*(3-u)&63):e.b64Pad;return a}(r,e,o,u)};case"BYTES":return function(r){return function(r,t,n){var i,e,o="",u=t/8,s=-1===n?3:0;for(i=0;i<u;i+=1)e=r[i>>>2]>>>8*(s+n*(i%4))&255,o+=String.fromCharCode(e);return o}(r,e,o)};case"ARRAYBUFFER":try{new ArrayBuffer(0)}catch(r){throw new Error(n)}return function(r){return function(r,t,n){var i,e=t/8,o=new ArrayBuffer(e),u=new Uint8Array(o),s=-1===n?3:0;for(i=0;i<e;i+=1)u[i]=r[i>>>2]>>>8*(s+n*(i%4))&255;return o}(r,e,o)};case"UINT8ARRAY":try{new Uint8Array(0)}catch(r){throw new Error(i)}return function(r){return function(r,t,n){var i,e=t/8,o=-1===n?3:0,u=new Uint8Array(e);for(i=0;i<e;i+=1)u[i]=r[i>>>2]>>>8*(o+n*(i%4))&255;return u}(r,e,o)};default:throw new Error("format must be HEX, B64, BYTES, ARRAYBUFFER, or UINT8ARRAY")}}function s(r){var t={outputUpper:!1,b64Pad:"=",outputLen:-1},n=r||{},i="Output length must be a multiple of 8";if(t.outputUpper=n.outputUpper||!1,n.b64Pad&&(t.b64Pad=n.b64Pad),n.outputLen){if(n.outputLen%8!=0)throw new Error(i);t.outputLen=n.outputLen}else if(n.shakeLen){if(n.shakeLen%8!=0)throw new Error(i);t.outputLen=n.shakeLen}if("boolean"!=typeof t.outputUpper)throw new Error("Invalid outputUpper formatting option");if("string"!=typeof t.b64Pad)throw new Error("Invalid b64Pad formatting option");return t}function f(r,t){return r<<t|r>>>32-t}function h(r,t,n){return r^t^n}function a(r,t,n){return r&t^r&n^t&n}function c(r,t){var n=(65535&r)+(65535&t);return(65535&(r>>>16)+(t>>>16)+(n>>>16))<<16|65535&n}function w(r,t,n,i,e){var o=(65535&r)+(65535&t)+(65535&n)+(65535&i)+(65535&e);return(65535&(r>>>16)+(t>>>16)+(n>>>16)+(i>>>16)+(e>>>16)+(o>>>16))<<16|65535&o}function E(r){return[1732584193,4023233417,2562383102,271733878,3285377520]}function v(r,t){var n,i,e,o,u,s,E,v,l=[];for(n=t[0],i=t[1],e=t[2],o=t[3],u=t[4],E=0;E<80;E+=1)l[E]=E<16?r[E]:f(l[E-3]^l[E-8]^l[E-14]^l[E-16],1),s=E<20?w(f(n,5),(v=i)&e^~v&o,u,1518500249,l[E]):E<40?w(f(n,5),h(i,e,o),u,1859775393,l[E]):E<60?w(f(n,5),a(i,e,o),u,2400959708,l[E]):w(f(n,5),h(i,e,o),u,3395469782,l[E]),u=o,o=e,e=f(i,30),i=n,n=s;return t[0]=c(n,t[0]),t[1]=c(i,t[1]),t[2]=c(e,t[2]),t[3]=c(o,t[3]),t[4]=c(u,t[4]),t}function l(r,t,n,i){for(var e,o=15+(t+65>>>9<<4),u=t+n;r.length<=o;)r.push(0);for(r[t>>>5]|=128<<24-t%32,r[o]=4294967295&u,r[o-1]=u/4294967296|0,e=0;e<r.length;e+=16)i=v(r.slice(e,e+16),i);return i}return function(t){function n(r,n,i){var e=this;if("SHA-1"!==r)throw new Error("Chosen SHA variant is not supported");var u=i||{};return(e=t.call(this,r,n,i)||this).t=!0,e.i=e.o,e.u=-1,e.h=o(e.v,e.l,e.u),e.p=v,e.A=function(r){return r.slice()},e.U=E,e.T=l,e.R=[1732584193,4023233417,2562383102,271733878,3285377520],e.m=512,e.F=160,e.g=!1,u.hmacKey&&e.B(function(r,t,n,i){var e=r+" must include a value and format";if(!t){if(!i)throw new Error(e);return i}if(void 0===t.value||!t.format)throw new Error(e);return o(t.format,t.encoding||"UTF8",n)(t.value)}("hmacKey",u.hmacKey,e.u)),e}return function(t,n){if("function"!=typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function i(){this.constructor=t}r(t,n),t.prototype=null===n?Object.create(n):(i.prototype=n.prototype,new i)}(n,t),n}(function(){function r(r,t,n){var i=n||{};if(this.v=t,this.l=i.encoding||"UTF8",this.numRounds=i.numRounds||1,isNaN(this.numRounds)||this.numRounds!==parseInt(this.numRounds,10)||1>this.numRounds)throw new Error("numRounds must a integer >= 1");this.Y=r,this.S=[],this.C=0,this.I=!1,this.H=0,this.L=!1,this.N=[],this.X=[]}return r.prototype.update=function(r){var t,n=0,i=this.m>>>5,e=this.h(r,this.S,this.C),o=e.binLen,u=e.value,s=o>>>5;for(t=0;t<s;t+=i)n+this.m<=o&&(this.R=this.p(u.slice(t,t+i),this.R),n+=this.m);return this.H+=n,this.S=u.slice(n>>>5),this.C=o%this.m,this.I=!0,this},r.prototype.getHash=function(r,t){var n,i,e=this.F,o=s(t);if(this.g){if(-1===o.outputLen)throw new Error("Output length must be specified in options");e=o.outputLen}var f=u(r,e,this.u,o);if(this.L&&this.i)return f(this.i(o));for(i=this.T(this.S.slice(),this.C,this.H,this.A(this.R),e),n=1;n<this.numRounds;n+=1)this.g&&e%32!=0&&(i[i.length-1]&=16777215>>>24-e%32),i=this.T(i,e,0,this.U(this.Y),e);return f(i)},r.prototype.setHMACKey=function(r,t,n){if(!this.t)throw new Error("Variant does not support HMAC");if(this.I)throw new Error("Cannot set MAC key after calling update");var i=o(t,(n||{}).encoding||"UTF8",this.u);this.B(i(r))},r.prototype.B=function(r){var t,n=this.m>>>3,i=n/4-1;if(1!==this.numRounds)throw new Error("Cannot set numRounds with MAC");if(this.L)throw new Error("MAC key already set");for(n<r.binLen/8&&(r.value=this.T(r.value,r.binLen,0,this.U(this.Y),this.F));r.value.length<=i;)r.value.push(0);for(t=0;t<=i;t+=1)this.N[t]=909522486^r.value[t],this.X[t]=1549556828^r.value[t];this.R=this.p(this.N,this.R),this.H=this.m,this.L=!0},r.prototype.getHMAC=function(r,t){var n=s(t);return u(r,this.F,this.u,n)(this.o())},r.prototype.o=function(){var r;if(!this.L)throw new Error("Cannot call getHMAC without first setting MAC key");var t=this.T(this.S.slice(),this.C,this.H,this.A(this.R),this.F);return r=this.p(this.X,this.U(this.Y)),r=this.T(t,this.F,this.m,r,this.F)},r}())}));
