/**
 * A JavaScript implementation of the SHA family of hashes - defined in FIPS PUB 180-4, FIPS PUB 202,
 * and SP 800-185 - as well as the corresponding HMAC implementation as defined in FIPS PUB 198-1.
 *
 * Copyright 2008-2023 <PERSON>, 1998-2009 <PERSON> & Contributors
 * Distributed under the BSD License
 * See http://caligatio.github.com/jsSHA/ for more information
 */
const n="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",t="ARRAYBUFFER not supported by this environment",e="UINT8ARRAY not supported by this environment";function r(n,t,e,r){let i,s,o;const w=t||[0],h=(e=e||0)>>>3,u=-1===r?3:0;for(i=0;i<n.length;i+=1)o=i+h,s=o>>>2,w.length<=s&&w.push(0),w[s]|=n[i]<<8*(u+r*(o%4));return{value:w,binLen:8*n.length+e}}function i(i,s,o){switch(s){case"UTF8":case"UTF16BE":case"UTF16LE":break;default:throw new Error("encoding must be UTF8, UTF16BE, or UTF16LE")}switch(i){case"HEX":return function(n,t,e){return function(n,t,e,r){let i,s,o,w;if(0!=n.length%2)throw new Error("String of HEX type must be in byte increments");const h=t||[0],u=(e=e||0)>>>3,c=-1===r?3:0;for(i=0;i<n.length;i+=2){if(s=parseInt(n.substr(i,2),16),isNaN(s))throw new Error("String of HEX type contains invalid characters");for(w=(i>>>1)+u,o=w>>>2;h.length<=o;)h.push(0);h[o]|=s<<8*(c+r*(w%4))}return{value:h,binLen:4*n.length+e}}(n,t,e,o)};case"TEXT":return function(n,t,e){return function(n,t,e,r,i){let s,o,w,h,u,c,f,a,l=0;const E=e||[0],A=(r=r||0)>>>3;if("UTF8"===t)for(f=-1===i?3:0,w=0;w<n.length;w+=1)for(s=n.charCodeAt(w),o=[],128>s?o.push(s):2048>s?(o.push(192|s>>>6),o.push(128|63&s)):55296>s||57344<=s?o.push(224|s>>>12,128|s>>>6&63,128|63&s):(w+=1,s=65536+((1023&s)<<10|1023&n.charCodeAt(w)),o.push(240|s>>>18,128|s>>>12&63,128|s>>>6&63,128|63&s)),h=0;h<o.length;h+=1){for(c=l+A,u=c>>>2;E.length<=u;)E.push(0);E[u]|=o[h]<<8*(f+i*(c%4)),l+=1}else for(f=-1===i?2:0,a="UTF16LE"===t&&1!==i||"UTF16LE"!==t&&1===i,w=0;w<n.length;w+=1){for(s=n.charCodeAt(w),!0===a&&(h=255&s,s=h<<8|s>>>8),c=l+A,u=c>>>2;E.length<=u;)E.push(0);E[u]|=s<<8*(f+i*(c%4)),l+=2}return{value:E,binLen:8*l+r}}(n,s,t,e,o)};case"B64":return function(t,e,r){return function(t,e,r,i){let s,o,w,h,u,c,f,a=0;const l=e||[0],E=(r=r||0)>>>3,A=-1===i?3:0,p=t.indexOf("=");if(-1===t.search(/^[a-zA-Z0-9=+/]+$/))throw new Error("Invalid character in base-64 string");if(t=t.replace(/=/g,""),-1!==p&&p<t.length)throw new Error("Invalid '=' found in base-64 string");for(o=0;o<t.length;o+=4){for(u=t.substr(o,4),h=0,w=0;w<u.length;w+=1)s=n.indexOf(u.charAt(w)),h|=s<<18-6*w;for(w=0;w<u.length-1;w+=1){for(f=a+E,c=f>>>2;l.length<=c;)l.push(0);l[c]|=(h>>>16-8*w&255)<<8*(A+i*(f%4)),a+=1}}return{value:l,binLen:8*a+r}}(t,e,r,o)};case"BYTES":return function(n,t,e){return function(n,t,e,r){let i,s,o,w;const h=t||[0],u=(e=e||0)>>>3,c=-1===r?3:0;for(s=0;s<n.length;s+=1)i=n.charCodeAt(s),w=s+u,o=w>>>2,h.length<=o&&h.push(0),h[o]|=i<<8*(c+r*(w%4));return{value:h,binLen:8*n.length+e}}(n,t,e,o)};case"ARRAYBUFFER":try{new ArrayBuffer(0)}catch(n){throw new Error(t)}return function(n,t,e){return function(n,t,e,i){return r(new Uint8Array(n),t,e,i)}(n,t,e,o)};case"UINT8ARRAY":try{new Uint8Array(0)}catch(n){throw new Error(e)}return function(n,t,e){return r(n,t,e,o)};default:throw new Error("format must be HEX, TEXT, B64, BYTES, ARRAYBUFFER, or UINT8ARRAY")}}function s(r,i,s,o){switch(r){case"HEX":return function(n){return function(n,t,e,r){const i="0123456789abcdef";let s,o,w="";const h=t/8,u=-1===e?3:0;for(s=0;s<h;s+=1)o=n[s>>>2]>>>8*(u+e*(s%4)),w+=i.charAt(o>>>4&15)+i.charAt(15&o);return r.outputUpper?w.toUpperCase():w}(n,i,s,o)};case"B64":return function(t){return function(t,e,r,i){let s,o,w,h,u,c="";const f=e/8,a=-1===r?3:0;for(s=0;s<f;s+=3)for(h=s+1<f?t[s+1>>>2]:0,u=s+2<f?t[s+2>>>2]:0,w=(t[s>>>2]>>>8*(a+r*(s%4))&255)<<16|(h>>>8*(a+r*((s+1)%4))&255)<<8|u>>>8*(a+r*((s+2)%4))&255,o=0;o<4;o+=1)c+=8*s+6*o<=e?n.charAt(w>>>6*(3-o)&63):i.b64Pad;return c}(t,i,s,o)};case"BYTES":return function(n){return function(n,t,e){let r,i,s="";const o=t/8,w=-1===e?3:0;for(r=0;r<o;r+=1)i=n[r>>>2]>>>8*(w+e*(r%4))&255,s+=String.fromCharCode(i);return s}(n,i,s)};case"ARRAYBUFFER":try{new ArrayBuffer(0)}catch(n){throw new Error(t)}return function(n){return function(n,t,e){let r;const i=t/8,s=new ArrayBuffer(i),o=new Uint8Array(s),w=-1===e?3:0;for(r=0;r<i;r+=1)o[r]=n[r>>>2]>>>8*(w+e*(r%4))&255;return s}(n,i,s)};case"UINT8ARRAY":try{new Uint8Array(0)}catch(n){throw new Error(e)}return function(n){return function(n,t,e){let r;const i=t/8,s=-1===e?3:0,o=new Uint8Array(i);for(r=0;r<i;r+=1)o[r]=n[r>>>2]>>>8*(s+e*(r%4))&255;return o}(n,i,s)};default:throw new Error("format must be HEX, B64, BYTES, ARRAYBUFFER, or UINT8ARRAY")}}const o=[1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298],w=[3238371032,914150663,812702999,4144912697,4290775857,1750603025,1694076839,3204075428],h=[1779033703,3144134277,1013904242,2773480762,1359893119,2600822924,528734635,1541459225];function u(n){const t={outputUpper:!1,b64Pad:"=",outputLen:-1},e=n||{},r="Output length must be a multiple of 8";if(t.outputUpper=e.outputUpper||!1,e.b64Pad&&(t.b64Pad=e.b64Pad),e.outputLen){if(e.outputLen%8!=0)throw new Error(r);t.outputLen=e.outputLen}else if(e.shakeLen){if(e.shakeLen%8!=0)throw new Error(r);t.outputLen=e.shakeLen}if("boolean"!=typeof t.outputUpper)throw new Error("Invalid outputUpper formatting option");if("string"!=typeof t.b64Pad)throw new Error("Invalid b64Pad formatting option");return t}class c{constructor(n,t,e){const r=e||{};if(this.t=t,this.i=r.encoding||"UTF8",this.numRounds=r.numRounds||1,isNaN(this.numRounds)||this.numRounds!==parseInt(this.numRounds,10)||1>this.numRounds)throw new Error("numRounds must a integer >= 1");this.o=n,this.h=[],this.u=0,this.l=!1,this.A=0,this.p=!1,this.U=[],this.R=[]}update(n){let t,e=0;const r=this.T>>>5,i=this.F(n,this.h,this.u),s=i.binLen,o=i.value,w=s>>>5;for(t=0;t<w;t+=r)e+this.T<=s&&(this.m=this.g(o.slice(t,t+r),this.m),e+=this.T);return this.A+=e,this.h=o.slice(e>>>5),this.u=s%this.T,this.l=!0,this}getHash(n,t){let e,r,i=this.H;const o=u(t);if(this.B){if(-1===o.outputLen)throw new Error("Output length must be specified in options");i=o.outputLen}const w=s(n,i,this.v,o);if(this.p&&this.C)return w(this.C(o));for(r=this.Y(this.h.slice(),this.u,this.A,this.S(this.m),i),e=1;e<this.numRounds;e+=1)this.B&&i%32!=0&&(r[r.length-1]&=16777215>>>24-i%32),r=this.Y(r,i,0,this.I(this.o),i);return w(r)}setHMACKey(n,t,e){if(!this.L)throw new Error("Variant does not support HMAC");if(this.l)throw new Error("Cannot set MAC key after calling update");const r=i(t,(e||{}).encoding||"UTF8",this.v);this.M(r(n))}M(n){const t=this.T>>>3,e=t/4-1;let r;if(1!==this.numRounds)throw new Error("Cannot set numRounds with MAC");if(this.p)throw new Error("MAC key already set");for(t<n.binLen/8&&(n.value=this.Y(n.value,n.binLen,0,this.I(this.o),this.H));n.value.length<=e;)n.value.push(0);for(r=0;r<=e;r+=1)this.U[r]=909522486^n.value[r],this.R[r]=1549556828^n.value[r];this.m=this.g(this.U,this.m),this.A=this.T,this.p=!0}getHMAC(n,t){const e=u(t);return s(n,this.H,this.v,e)(this.N())}N(){let n;if(!this.p)throw new Error("Cannot call getHMAC without first setting MAC key");const t=this.Y(this.h.slice(),this.u,this.A,this.S(this.m),this.H);return n=this.g(this.R,this.I(this.o)),n=this.Y(t,this.H,this.T,n,this.H),n}}class f{constructor(n,t){this.X=n,this.k=t}}function a(n,t){let e;return t<32?(e=32-t,new f(n.X>>>t|n.k<<e,n.k>>>t|n.X<<e)):(e=64-t,new f(n.k>>>t|n.X<<e,n.X>>>t|n.k<<e))}function l(n,t){return new f(n.X>>>t,n.k>>>t|n.X<<32-t)}function E(n,t,e){return new f(n.X&t.X^n.X&e.X^t.X&e.X,n.k&t.k^n.k&e.k^t.k&e.k)}function A(n){const t=a(n,28),e=a(n,34),r=a(n,39);return new f(t.X^e.X^r.X,t.k^e.k^r.k)}function p(n,t){let e,r;e=(65535&n.k)+(65535&t.k),r=(n.k>>>16)+(t.k>>>16)+(e>>>16);const i=(65535&r)<<16|65535&e;e=(65535&n.X)+(65535&t.X)+(r>>>16),r=(n.X>>>16)+(t.X>>>16)+(e>>>16);return new f((65535&r)<<16|65535&e,i)}function U(n,t,e,r){let i,s;i=(65535&n.k)+(65535&t.k)+(65535&e.k)+(65535&r.k),s=(n.k>>>16)+(t.k>>>16)+(e.k>>>16)+(r.k>>>16)+(i>>>16);const o=(65535&s)<<16|65535&i;i=(65535&n.X)+(65535&t.X)+(65535&e.X)+(65535&r.X)+(s>>>16),s=(n.X>>>16)+(t.X>>>16)+(e.X>>>16)+(r.X>>>16)+(i>>>16);return new f((65535&s)<<16|65535&i,o)}function d(n,t,e,r,i){let s,o;s=(65535&n.k)+(65535&t.k)+(65535&e.k)+(65535&r.k)+(65535&i.k),o=(n.k>>>16)+(t.k>>>16)+(e.k>>>16)+(r.k>>>16)+(i.k>>>16)+(s>>>16);const w=(65535&o)<<16|65535&s;s=(65535&n.X)+(65535&t.X)+(65535&e.X)+(65535&r.X)+(65535&i.X)+(o>>>16),o=(n.X>>>16)+(t.X>>>16)+(e.X>>>16)+(r.X>>>16)+(i.X>>>16)+(s>>>16);return new f((65535&o)<<16|65535&s,w)}function R(n){const t=a(n,19),e=a(n,61),r=l(n,6);return new f(t.X^e.X^r.X,t.k^e.k^r.k)}function y(n){const t=a(n,1),e=a(n,8),r=l(n,7);return new f(t.X^e.X^r.X,t.k^e.k^r.k)}function T(n){const t=a(n,14),e=a(n,18),r=a(n,41);return new f(t.X^e.X^r.X,t.k^e.k^r.k)}const F=[new f(o[0],3609767458),new f(o[1],602891725),new f(o[2],3964484399),new f(o[3],2173295548),new f(o[4],4081628472),new f(o[5],3053834265),new f(o[6],2937671579),new f(o[7],3664609560),new f(o[8],2734883394),new f(o[9],1164996542),new f(o[10],1323610764),new f(o[11],3590304994),new f(o[12],4068182383),new f(o[13],991336113),new f(o[14],633803317),new f(o[15],3479774868),new f(o[16],2666613458),new f(o[17],944711139),new f(o[18],2341262773),new f(o[19],2007800933),new f(o[20],1495990901),new f(o[21],1856431235),new f(o[22],3175218132),new f(o[23],2198950837),new f(o[24],3999719339),new f(o[25],766784016),new f(o[26],2566594879),new f(o[27],3203337956),new f(o[28],1034457026),new f(o[29],2466948901),new f(o[30],3758326383),new f(o[31],168717936),new f(o[32],1188179964),new f(o[33],1546045734),new f(o[34],1522805485),new f(o[35],2643833823),new f(o[36],2343527390),new f(o[37],1014477480),new f(o[38],1206759142),new f(o[39],344077627),new f(o[40],1290863460),new f(o[41],3158454273),new f(o[42],3505952657),new f(o[43],106217008),new f(o[44],3606008344),new f(o[45],1432725776),new f(o[46],1467031594),new f(o[47],851169720),new f(o[48],3100823752),new f(o[49],1363258195),new f(o[50],3750685593),new f(o[51],3785050280),new f(o[52],3318307427),new f(o[53],3812723403),new f(o[54],2003034995),new f(o[55],3602036899),new f(o[56],1575990012),new f(o[57],1125592928),new f(o[58],2716904306),new f(o[59],442776044),new f(o[60],593698344),new f(o[61],3733110249),new f(o[62],2999351573),new f(o[63],3815920427),new f(3391569614,3928383900),new f(3515267271,566280711),new f(3940187606,3454069534),new f(4118630271,4000239992),new f(116418474,1914138554),new f(174292421,2731055270),new f(289380356,3203993006),new f(460393269,320620315),new f(685471733,587496836),new f(852142971,1086792851),new f(1017036298,365543100),new f(1126000580,2618297676),new f(1288033470,3409855158),new f(1501505948,4234509866),new f(1607167915,987167468),new f(1816402316,1246189591)];function b(n){return"SHA-384"===n?[new f(3418070365,w[0]),new f(1654270250,w[1]),new f(2438529370,w[2]),new f(355462360,w[3]),new f(1731405415,w[4]),new f(41048885895,w[5]),new f(3675008525,w[6]),new f(1203062813,w[7])]:[new f(h[0],4089235720),new f(h[1],2227873595),new f(h[2],4271175723),new f(h[3],1595750129),new f(h[4],2917565137),new f(h[5],725511199),new f(h[6],4215389547),new f(h[7],327033209)]}function m(n,t){let e,r,i,s,o,w,h,u,c,a,l,b;const m=[];for(e=t[0],r=t[1],i=t[2],s=t[3],o=t[4],w=t[5],h=t[6],u=t[7],l=0;l<80;l+=1)l<16?(b=2*l,m[l]=new f(n[b],n[b+1])):m[l]=U(R(m[l-2]),m[l-7],y(m[l-15]),m[l-16]),c=d(u,T(o),(H=w,B=h,new f((g=o).X&H.X^~g.X&B.X,g.k&H.k^~g.k&B.k)),F[l],m[l]),a=p(A(e),E(e,r,i)),u=h,h=w,w=o,o=p(s,c),s=i,i=r,r=e,e=p(c,a);var g,H,B;return t[0]=p(e,t[0]),t[1]=p(r,t[1]),t[2]=p(i,t[2]),t[3]=p(s,t[3]),t[4]=p(o,t[4]),t[5]=p(w,t[5]),t[6]=p(h,t[6]),t[7]=p(u,t[7]),t}class g extends c{constructor(n,t,e){if("SHA-384"!==n&&"SHA-512"!==n)throw new Error("Chosen SHA variant is not supported");super(n,t,e);const r=e||{};this.C=this.N,this.L=!0,this.v=-1,this.F=i(this.t,this.i,this.v),this.g=m,this.S=function(n){return n.slice()},this.I=b,this.Y=function(t,e,r,i){return function(n,t,e,r,i){let s,o;const w=31+(t+129>>>10<<5),h=t+e;for(;n.length<=w;)n.push(0);for(n[t>>>5]|=128<<24-t%32,n[w]=4294967295&h,n[w-1]=h/4294967296|0,s=0;s<n.length;s+=32)r=m(n.slice(s,s+32),r);return o="SHA-384"===i?[r[0].X,r[0].k,r[1].X,r[1].k,r[2].X,r[2].k,r[3].X,r[3].k,r[4].X,r[4].k,r[5].X,r[5].k]:[r[0].X,r[0].k,r[1].X,r[1].k,r[2].X,r[2].k,r[3].X,r[3].k,r[4].X,r[4].k,r[5].X,r[5].k,r[6].X,r[6].k,r[7].X,r[7].k],o}(t,e,r,i,n)},this.m=b(n),this.T=1024,this.H="SHA-384"===n?384:512,this.B=!1,r.hmacKey&&this.M(function(n,t,e,r){const s=n+" must include a value and format";if(!t){if(!r)throw new Error(s);return r}if(void 0===t.value||!t.format)throw new Error(s);return i(t.format,t.encoding||"UTF8",e)(t.value)}("hmacKey",r.hmacKey,this.v))}}export{g as default};
