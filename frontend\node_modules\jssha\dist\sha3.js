/**
 * A JavaScript implementation of the SHA family of hashes - defined in FIPS PUB 180-4, FIPS PUB 202,
 * and SP 800-185 - as well as the corresponding HMAC implementation as defined in FIPS PUB 198-1.
 *
 * Copyright 2008-2023 <PERSON>, 1998-2009 <PERSON> & Contributors
 * Distributed under the BSD License
 * See http://caligatio.github.com/jsSHA/ for more information
 *
 * Two ECMAScript polyfill functions carry the following license:
 *
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except in compliance with
 * the License. You may obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0
 *
 * THIS CODE IS PROVIDED ON AN *AS IS* BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED,
 * INCLUDING WITHOUT LIMITATION ANY IMPLIED WARRANTIES OR CONDITIONS OF TITLE, FITNESS FOR A PARTICULAR PURPOSE,
 * MERCHANTABLITY OR NON-INFRINGEMENT.
 *
 * See the Apache Version 2.0 License for specific language governing permissions and limitations under the License.
 */
!function(r,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):(r="undefined"!=typeof globalThis?globalThis:r||self).jsSHA=t()}(this,(function(){"use strict";var r=function(t,n){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,t){r.__proto__=t}||function(r,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(r[n]=t[n])},r(t,n)};"function"==typeof SuppressedError&&SuppressedError;var t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",n="ARRAYBUFFER not supported by this environment",e="UINT8ARRAY not supported by this environment";function i(r,t,n,e){var i,o,s,u=t||[0],f=(n=n||0)>>>3,a=-1===e?3:0;for(i=0;i<r.length;i+=1)o=(s=i+f)>>>2,u.length<=o&&u.push(0),u[o]|=r[i]<<8*(a+e*(s%4));return{value:u,binLen:8*r.length+n}}function o(r,o,s){switch(o){case"UTF8":case"UTF16BE":case"UTF16LE":break;default:throw new Error("encoding must be UTF8, UTF16BE, or UTF16LE")}switch(r){case"HEX":return function(r,t,n){return function(r,t,n,e){var i,o,s,u;if(0!=r.length%2)throw new Error("String of HEX type must be in byte increments");var f=t||[0],a=(n=n||0)>>>3,h=-1===e?3:0;for(i=0;i<r.length;i+=2){if(o=parseInt(r.substr(i,2),16),isNaN(o))throw new Error("String of HEX type contains invalid characters");for(s=(u=(i>>>1)+a)>>>2;f.length<=s;)f.push(0);f[s]|=o<<8*(h+e*(u%4))}return{value:f,binLen:4*r.length+n}}(r,t,n,s)};case"TEXT":return function(r,t,n){return function(r,t,n,e,i){var o,s,u,f,a,h,c,w,v=0,l=n||[0],E=(e=e||0)>>>3;if("UTF8"===t)for(c=-1===i?3:0,u=0;u<r.length;u+=1)for(s=[],128>(o=r.charCodeAt(u))?s.push(o):2048>o?(s.push(192|o>>>6),s.push(128|63&o)):55296>o||57344<=o?s.push(224|o>>>12,128|o>>>6&63,128|63&o):(u+=1,o=65536+((1023&o)<<10|1023&r.charCodeAt(u)),s.push(240|o>>>18,128|o>>>12&63,128|o>>>6&63,128|63&o)),f=0;f<s.length;f+=1){for(a=(h=v+E)>>>2;l.length<=a;)l.push(0);l[a]|=s[f]<<8*(c+i*(h%4)),v+=1}else for(c=-1===i?2:0,w="UTF16LE"===t&&1!==i||"UTF16LE"!==t&&1===i,u=0;u<r.length;u+=1){for(o=r.charCodeAt(u),!0===w&&(o=(f=255&o)<<8|o>>>8),a=(h=v+E)>>>2;l.length<=a;)l.push(0);l[a]|=o<<8*(c+i*(h%4)),v+=2}return{value:l,binLen:8*v+e}}(r,o,t,n,s)};case"B64":return function(r,n,e){return function(r,n,e,i){var o,s,u,f,a,h,c=0,w=n||[0],v=(e=e||0)>>>3,l=-1===i?3:0,E=r.indexOf("=");if(-1===r.search(/^[a-zA-Z0-9=+/]+$/))throw new Error("Invalid character in base-64 string");if(r=r.replace(/=/g,""),-1!==E&&E<r.length)throw new Error("Invalid '=' found in base-64 string");for(o=0;o<r.length;o+=4){for(f=r.substr(o,4),u=0,s=0;s<f.length;s+=1)u|=t.indexOf(f.charAt(s))<<18-6*s;for(s=0;s<f.length-1;s+=1){for(a=(h=c+v)>>>2;w.length<=a;)w.push(0);w[a]|=(u>>>16-8*s&255)<<8*(l+i*(h%4)),c+=1}}return{value:w,binLen:8*c+e}}(r,n,e,s)};case"BYTES":return function(r,t,n){return function(r,t,n,e){var i,o,s,u,f=t||[0],a=(n=n||0)>>>3,h=-1===e?3:0;for(o=0;o<r.length;o+=1)i=r.charCodeAt(o),s=(u=o+a)>>>2,f.length<=s&&f.push(0),f[s]|=i<<8*(h+e*(u%4));return{value:f,binLen:8*r.length+n}}(r,t,n,s)};case"ARRAYBUFFER":try{new ArrayBuffer(0)}catch(r){throw new Error(n)}return function(r,t,n){return function(r,t,n,e){return i(new Uint8Array(r),t,n,e)}(r,t,n,s)};case"UINT8ARRAY":try{new Uint8Array(0)}catch(r){throw new Error(e)}return function(r,t,n){return i(r,t,n,s)};default:throw new Error("format must be HEX, TEXT, B64, BYTES, ARRAYBUFFER, or UINT8ARRAY")}}function s(r,i,o,s){switch(r){case"HEX":return function(r){return function(r,t,n,e){var i,o,s="0123456789abcdef",u="",f=t/8,a=-1===n?3:0;for(i=0;i<f;i+=1)o=r[i>>>2]>>>8*(a+n*(i%4)),u+=s.charAt(o>>>4&15)+s.charAt(15&o);return e.outputUpper?u.toUpperCase():u}(r,i,o,s)};case"B64":return function(r){return function(r,n,e,i){var o,s,u,f,a,h="",c=n/8,w=-1===e?3:0;for(o=0;o<c;o+=3)for(f=o+1<c?r[o+1>>>2]:0,a=o+2<c?r[o+2>>>2]:0,u=(r[o>>>2]>>>8*(w+e*(o%4))&255)<<16|(f>>>8*(w+e*((o+1)%4))&255)<<8|a>>>8*(w+e*((o+2)%4))&255,s=0;s<4;s+=1)h+=8*o+6*s<=n?t.charAt(u>>>6*(3-s)&63):i.b64Pad;return h}(r,i,o,s)};case"BYTES":return function(r){return function(r,t,n){var e,i,o="",s=t/8,u=-1===n?3:0;for(e=0;e<s;e+=1)i=r[e>>>2]>>>8*(u+n*(e%4))&255,o+=String.fromCharCode(i);return o}(r,i,o)};case"ARRAYBUFFER":try{new ArrayBuffer(0)}catch(r){throw new Error(n)}return function(r){return function(r,t,n){var e,i=t/8,o=new ArrayBuffer(i),s=new Uint8Array(o),u=-1===n?3:0;for(e=0;e<i;e+=1)s[e]=r[e>>>2]>>>8*(u+n*(e%4))&255;return o}(r,i,o)};case"UINT8ARRAY":try{new Uint8Array(0)}catch(r){throw new Error(e)}return function(r){return function(r,t,n){var e,i=t/8,o=-1===n?3:0,s=new Uint8Array(i);for(e=0;e<i;e+=1)s[e]=r[e>>>2]>>>8*(o+n*(e%4))&255;return s}(r,i,o)};default:throw new Error("format must be HEX, B64, BYTES, ARRAYBUFFER, or UINT8ARRAY")}}var u=4294967296,f="Cannot set numRounds with MAC";function a(r,t){var n,e,i=r.binLen>>>3,o=t.binLen>>>3,s=i<<3,u=4-i<<3;if(i%4!=0){for(n=0;n<o;n+=4)e=i+n>>>2,r.value[e]|=t.value[n>>>2]<<s,r.value.push(0),r.value[e+1]|=t.value[n>>>2]>>>u;return(r.value.length<<2)-4>=o+i&&r.value.pop(),{value:r.value,binLen:r.binLen+t.binLen}}return{value:r.value.concat(t.value),binLen:r.binLen+t.binLen}}function h(r){var t={outputUpper:!1,b64Pad:"=",outputLen:-1},n=r||{},e="Output length must be a multiple of 8";if(t.outputUpper=n.outputUpper||!1,n.b64Pad&&(t.b64Pad=n.b64Pad),n.outputLen){if(n.outputLen%8!=0)throw new Error(e);t.outputLen=n.outputLen}else if(n.shakeLen){if(n.shakeLen%8!=0)throw new Error(e);t.outputLen=n.shakeLen}if("boolean"!=typeof t.outputUpper)throw new Error("Invalid outputUpper formatting option");if("string"!=typeof t.b64Pad)throw new Error("Invalid b64Pad formatting option");return t}function c(r,t,n,e){var i=r+" must include a value and format";if(!t){if(!e)throw new Error(i);return e}if(void 0===t.value||!t.format)throw new Error(i);return o(t.format,t.encoding||"UTF8",n)(t.value)}var w=function(){function r(r,t,n){var e=n||{};if(this.t=t,this.i=e.encoding||"UTF8",this.numRounds=e.numRounds||1,isNaN(this.numRounds)||this.numRounds!==parseInt(this.numRounds,10)||1>this.numRounds)throw new Error("numRounds must a integer >= 1");this.o=r,this.u=[],this.h=0,this.v=!1,this.l=0,this.A=!1,this.p=[],this.m=[]}return r.prototype.update=function(r){var t,n=0,e=this.U>>>5,i=this.R(r,this.u,this.h),o=i.binLen,s=i.value,u=o>>>5;for(t=0;t<u;t+=e)n+this.U<=o&&(this.T=this.F(s.slice(t,t+e),this.T),n+=this.U);return this.l+=n,this.u=s.slice(n>>>5),this.h=o%this.U,this.v=!0,this},r.prototype.getHash=function(r,t){var n,e,i=this.S,o=h(t);if(this.C){if(-1===o.outputLen)throw new Error("Output length must be specified in options");i=o.outputLen}var u=s(r,i,this.H,o);if(this.A&&this.g)return u(this.g(o));for(e=this.L(this.u.slice(),this.h,this.l,this.B(this.T),i),n=1;n<this.numRounds;n+=1)this.C&&i%32!=0&&(e[e.length-1]&=16777215>>>24-i%32),e=this.L(e,i,0,this.k(this.o),i);return u(e)},r.prototype.setHMACKey=function(r,t,n){if(!this.Y)throw new Error("Variant does not support HMAC");if(this.v)throw new Error("Cannot set MAC key after calling update");var e=o(t,(n||{}).encoding||"UTF8",this.H);this.K(e(r))},r.prototype.K=function(r){var t,n=this.U>>>3,e=n/4-1;if(1!==this.numRounds)throw new Error(f);if(this.A)throw new Error("MAC key already set");for(n<r.binLen/8&&(r.value=this.L(r.value,r.binLen,0,this.k(this.o),this.S));r.value.length<=e;)r.value.push(0);for(t=0;t<=e;t+=1)this.p[t]=909522486^r.value[t],this.m[t]=1549556828^r.value[t];this.T=this.F(this.p,this.T),this.l=this.U,this.A=!0},r.prototype.getHMAC=function(r,t){var n=h(t);return s(r,this.S,this.H,n)(this.N())},r.prototype.N=function(){var r;if(!this.A)throw new Error("Cannot call getHMAC without first setting MAC key");var t=this.L(this.u.slice(),this.h,this.l,this.B(this.T),this.S);return r=this.F(this.m,this.k(this.o)),r=this.L(t,this.S,this.U,r,this.S)},r}(),v=function(r,t){this.I=r,this.M=t};function l(r,t){var n;return t>32?(n=64-t,new v(r.M<<t|r.I>>>n,r.I<<t|r.M>>>n)):0!==t?(n=32-t,new v(r.I<<t|r.M>>>n,r.M<<t|r.I>>>n)):r}function E(r,t){return new v(r.I^t.I,r.M^t.M)}var A=[new v(0,1),new v(0,32898),new v(2147483648,32906),new v(2147483648,2147516416),new v(0,32907),new v(0,2147483649),new v(2147483648,2147516545),new v(2147483648,32777),new v(0,138),new v(0,136),new v(0,2147516425),new v(0,2147483658),new v(0,2147516555),new v(2147483648,139),new v(2147483648,32905),new v(2147483648,32771),new v(2147483648,32770),new v(2147483648,128),new v(0,32778),new v(2147483648,2147483658),new v(2147483648,2147516545),new v(2147483648,32896),new v(0,2147483649),new v(2147483648,2147516424)],b=[[0,36,3,41,18],[1,44,10,45,2],[62,6,43,15,61],[28,55,25,21,56],[27,20,39,8,14]];function p(r){var t,n=[];for(t=0;t<5;t+=1)n[t]=[new v(0,0),new v(0,0),new v(0,0),new v(0,0),new v(0,0)];return n}function d(r){var t,n=[];for(t=0;t<5;t+=1)n[t]=r[t].slice();return n}function y(r,t){var n,e,i,o,s,u,f,a,h,c=[],w=[];if(null!==r)for(e=0;e<r.length;e+=2)t[(e>>>1)%5][(e>>>1)/5|0]=E(t[(e>>>1)%5][(e>>>1)/5|0],new v(r[e+1],r[e]));for(n=0;n<24;n+=1){for(o=p(),e=0;e<5;e+=1)c[e]=(s=t[e][0],u=t[e][1],f=t[e][2],a=t[e][3],h=t[e][4],new v(s.I^u.I^f.I^a.I^h.I,s.M^u.M^f.M^a.M^h.M));for(e=0;e<5;e+=1)w[e]=E(c[(e+4)%5],l(c[(e+1)%5],1));for(e=0;e<5;e+=1)for(i=0;i<5;i+=1)t[e][i]=E(t[e][i],w[e]);for(e=0;e<5;e+=1)for(i=0;i<5;i+=1)o[i][(2*e+3*i)%5]=l(t[e][i],b[e][i]);for(e=0;e<5;e+=1)for(i=0;i<5;i+=1)t[e][i]=E(o[e][i],new v(~o[(e+1)%5][i].I&o[(e+2)%5][i].I,~o[(e+1)%5][i].M&o[(e+2)%5][i].M));t[0][0]=E(t[0][0],A[n])}return t}function m(r){var t,n,e=0,i=[0,0],o=[4294967295&r,r/u&2097151];for(t=6;t>=0;t--)0===(n=o[t>>2]>>>8*t&255)&&0===e||(i[e+1>>2]|=n<<8*(e+1),e+=1);return e=0!==e?e:1,i[0]|=e,{value:e+1>4?i:[i[0]],binLen:8+8*e}}function U(r){return a(m(r.binLen),r)}function R(r,t){var n,e=m(t),i=t>>>2,o=(i-(e=a(e,r)).value.length%i)%i;for(n=0;n<o;n++)e.value.push(0);return e.value}return function(t){function n(r,n,e){var i=this,s=6,u=0,a=e||{};if(1!==(i=t.call(this,r,n,e)||this).numRounds){if(a.kmacKey||a.hmacKey)throw new Error(f);if("CSHAKE128"===i.o||"CSHAKE256"===i.o)throw new Error("Cannot set numRounds for CSHAKE variants")}switch(i.H=1,i.R=o(i.t,i.i,i.H),i.F=y,i.B=d,i.k=p,i.T=p(),i.C=!1,r){case"SHA3-224":i.U=u=1152,i.S=224,i.Y=!0,i.g=i.N;break;case"SHA3-256":i.U=u=1088,i.S=256,i.Y=!0,i.g=i.N;break;case"SHA3-384":i.U=u=832,i.S=384,i.Y=!0,i.g=i.N;break;case"SHA3-512":i.U=u=576,i.S=512,i.Y=!0,i.g=i.N;break;case"SHAKE128":s=31,i.U=u=1344,i.S=-1,i.C=!0,i.Y=!1,i.g=null;break;case"SHAKE256":s=31,i.U=u=1088,i.S=-1,i.C=!0,i.Y=!1,i.g=null;break;case"KMAC128":s=4,i.U=u=1344,i.X(e),i.S=-1,i.C=!0,i.Y=!1,i.g=i.O;break;case"KMAC256":s=4,i.U=u=1088,i.X(e),i.S=-1,i.C=!0,i.Y=!1,i.g=i.O;break;case"CSHAKE128":i.U=u=1344,s=i.j(e),i.S=-1,i.C=!0,i.Y=!1,i.g=null;break;case"CSHAKE256":i.U=u=1088,s=i.j(e),i.S=-1,i.C=!0,i.Y=!1,i.g=null;break;default:throw new Error("Chosen SHA variant is not supported")}return i.L=function(r,t,n,e,i){return function(r,t,n,e,i,o,s){var u,f,a=0,h=[],c=i>>>5,w=t>>>5;for(u=0;u<w&&t>=i;u+=c)e=y(r.slice(u,u+c),e),t-=i;for(r=r.slice(u),t%=i;r.length<c;)r.push(0);for(r[(u=t>>>3)>>2]^=o<<u%4*8,r[c-1]^=2147483648,e=y(r,e);32*h.length<s&&(f=e[a%5][a/5|0],h.push(f.M),!(32*h.length>=s));)h.push(f.I),0==64*(a+=1)%i&&(y(null,e),a=0);return h}(r,t,0,e,u,s,i)},a.hmacKey&&i.K(c("hmacKey",a.hmacKey,i.H)),i}return function(t,n){if("function"!=typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function e(){this.constructor=t}r(t,n),t.prototype=null===n?Object.create(n):(e.prototype=n.prototype,new e)}(n,t),n.prototype.j=function(r,t){var n=function(r){var t=r||{};return{funcName:c("funcName",t.funcName,1,{value:[],binLen:0}),customization:c("Customization",t.customization,1,{value:[],binLen:0})}}(r||{});t&&(n.funcName=t);var e=a(U(n.funcName),U(n.customization));if(0!==n.customization.binLen||0!==n.funcName.binLen){for(var i=R(e,this.U>>>3),o=0;o<i.length;o+=this.U>>>5)this.T=this.F(i.slice(o,o+(this.U>>>5)),this.T),this.l+=this.U;return 4}return 31},n.prototype.X=function(r){var t=function(r){var t=r||{};return{kmacKey:c("kmacKey",t.kmacKey,1),funcName:{value:[1128353099],binLen:32},customization:c("Customization",t.customization,1,{value:[],binLen:0})}}(r||{});this.j(r,t.funcName);for(var n=R(U(t.kmacKey),this.U>>>3),e=0;e<n.length;e+=this.U>>>5)this.T=this.F(n.slice(e,e+(this.U>>>5)),this.T),this.l+=this.U;this.A=!0},n.prototype.O=function(r){var t=a({value:this.u.slice(),binLen:this.h},function(r){var t,n,e=0,i=[0,0],o=[4294967295&r,r/u&2097151];for(t=6;t>=0;t--)0==(n=o[t>>2]>>>8*t&255)&&0===e||(i[e>>2]|=n<<8*e,e+=1);return i[(e=0!==e?e:1)>>2]|=e<<8*e,{value:e+1>4?i:[i[0]],binLen:8+8*e}}(r.outputLen));return this.L(t.value,t.binLen,this.l,this.B(this.T),r.outputLen)},n}(w)}));
